import './assets/fonts/fonts.css'
import * as ReactDOM from 'react-dom/client'
import { App } from './components/app'
import { datadogRum } from '@datadog/browser-rum'

const APP_VERSION = import.meta.env.VITE_APP_VERSION
const IS_DEV = import.meta.env.MODE === 'development'
const {
  VITE_DATADOG_APPLICATION_ID,
  VITE_DATADOG_CLIENT_TOKEN,
  VITE_DATADOG_SERVICE,
  VITE_DATADOG_ENV,
} = import.meta.env

function initDataDog() {
  if (!IS_DEV && VITE_DATADOG_APPLICATION_ID) {
    datadogRum.init({
      applicationId: VITE_DATADOG_APPLICATION_ID,
      clientToken: VITE_DATADOG_CLIENT_TOKEN,
      // `site` refers to the Datadog site parameter of your organization
      // see https://docs.datadoghq.com/getting_started/site/
      site: 'datadoghq.com',
      service: VITE_DATADOG_SERVICE,
      env: VITE_DATADOG_ENV,
      // Specify a version number to identify the deployed version of your application in Datadog
      version: APP_VERSION,
      sessionSampleRate: 100,
      sessionReplaySampleRate: 100,
      trackUserInteractions: true,
      trackResources: true,
      trackLongTasks: true,
      defaultPrivacyLevel: 'mask-user-input',
    })
  }
}

initDataDog()

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement)

root.render(<App />)
