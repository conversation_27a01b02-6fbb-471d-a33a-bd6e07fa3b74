@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply font-sans; /* This applies BDGrotesk to everything */
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-header; /* This applies BwGradual to all headers */
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 215, 25%, 27%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 218, 11%, 65%;

    --popover: 0 0% 100%;
    --popover-foreground: 215, 25%, 27%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: 0 0% 100%;
    --card-foreground: 215, 25%, 27%;

    --primary: 215 25% 27%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 215, 25%, 27%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 215, 25%, 27%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 0 100% 50%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.75rem;

    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;

    --color-jira: #0052cc;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
}

.react-flow__handle {
  position: absolute;
  pointer-events: none;
  min-width: 5px;
  min-height: 5px;
  background-color: #333;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side='bottom'] {
  --tw-enter-translate-y: -0.5rem;
}

fieldset[disabled] select,
fieldset[disabled] input,
fieldset[disabled] button {
  pointer-events: none;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
