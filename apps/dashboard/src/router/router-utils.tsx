import { MetaTagsManager } from '@libs/common'
import { createSerializer, parseAsJson, parseAsString } from 'nuqs'
import { z } from 'zod'

export const withMetaTags = (
  title: string,
  description: string,
  element: JSX.Element
) => (
  <>
    <MetaTagsManager title={title} description={description} siteName="Prime" />
    {element}
  </>
)

export const containerOpenedItemSchema = z.object({
  issue_id: z.string(),
  source_id: z.number(),
})

export const serializeWorkroom = createSerializer({
  f: parseAsJson((value) => value),
  s: parseAsJson((value) => value),
  selected_columns: parseAsJson((value) => value),
  view: parseAsString,
  opened_item: parseAs<PERSON>son(containerOpenedItemSchema.parse),
})

export const serializePsv = createSerializer({
  f: parseAsJson((value) => value),
  s: parseAs<PERSON><PERSON>((value) => value),
})

export const serializeSecurityTab = createSerializer({
  tab: parseAsString,
})
