import { createBrowserRouter, type RouteObject } from 'react-router'
import {
  loginRouteLoader,
  logoutLoader,
  privateRouteLoader,
  ProtectedLayout,
  PublicLayout,
} from '../auth/auth-utils'
import {
  HomePage,
  WorkroomPage,
  LoginPage,
  WorkroomDetailsPage,
  NotFoundPage,
  PSIPage,
  ContainersPage,
  DesignKitPage,
} from '../pages'
import { ErrorBoundary } from '../components/error-boundary'
import { settingsRoutes } from './settings-routes'
import { withMetaTags } from './router-utils'
import { DocumentsPage } from '../pages/documents/page'
import { DocumentsDetailsPage } from '../pages/documents-details/page'
import type { FilterItem } from '@libs/ui'

const mainRoutes: RouteObject[] = [
  {
    path: '*',
    element: withMetaTags(
      'Not found',
      'The page you are looking for does not exist',
      <NotFoundPage />
    ),
  },
  {
    path: '/',
    id: 'root',
    element: withMetaTags('Dashboard', 'Dashboard page', <HomePage />),
    index: true,
  },
  {
    path: '/workroom',
    element: withMetaTags('Workroom', 'Workroom page', <WorkroomPage />),
  },
  {
    path: '/containers',
    element: withMetaTags('Containers', 'Containers page', <ContainersPage />),
  },
  {
    path: '/design-reviews',
    element: withMetaTags(
      'Security reviews',
      'Security Reviews page',
      <DocumentsPage />
    ),
  },
  {
    path: '/design-reviews/:id',
    element: withMetaTags(
      'Security Reviews Details',
      'Security Reviews Details page',
      <DocumentsDetailsPage />
    ),
  },
  {
    path: '/psv',
    element: withMetaTags(
      'Security Violations',
      'Potential Security Incidents',
      <PSIPage />
    ),
  },
  {
    path: 'workroom/:sourceId/:issueId',
    element: <WorkroomDetailsPage />,
  },
  {
    path: '/design-kit',
    element: withMetaTags(
      'Design Kit',
      'UI Components showcase',
      <DesignKitPage />
    ),
  },
]

export const defaultSortParamString =
  '{"field":"risk_score_category","direction":"desc"}'

export const defaultSortParam = {
  field: 'risk_score_category',
  direction: 'desc',
}

export const defaultFilterParams = [
  { field: 'classification', op: 'eq', value: ['true'] },
  {
    field: 'container',
    op: 'eq',
    value: ['false'],
  },
  {
    field: 'status',
    op: 'eq',
    value: ['open'],
  },
  {
    field: 'is_automated',
    op: 'eq',
    value: ['false'],
  },
  {
    field: 'is_security_enhancement',
    op: 'eq',
    value: ['false'],
  },
]

export const defaultPsvFilterParams = [
  { field: 'status', op: 'eq', value: ['open'] },
]

export const defaultContainersSortParamString =
  '{"field":"risk_score","direction":"desc"}'

export const defaultContainersSortParam = {
  field: 'risk_score',
  direction: 'desc',
}

export const getParsedArrayOfObjects = (arr: string[] | undefined) => {
  if (!arr?.length) return undefined
  return arr.map((item) => JSON.parse(item))
}

export const getStringifiedArrayOfObjects = (
  arr: never[] | string[] | undefined | FilterItem[]
) => {
  if (!arr?.length) return undefined
  return arr.map((item) => JSON.stringify(item))
}

export const router = createBrowserRouter([
  {
    element: <ProtectedLayout />,
    loader: privateRouteLoader,
    errorElement: <ErrorBoundary />,
    children: [...mainRoutes, ...settingsRoutes],
    hydrateFallbackElement: <div></div>,
  },
  {
    element: <PublicLayout />,
    children: [
      {
        path: 'login',
        errorElement: <ErrorBoundary isLoginError />,
        element: withMetaTags('Login', 'Login page', <LoginPage />),
        loader: loginRouteLoader,
      },
    ],
  },
  {
    element: <PublicLayout />,
    loader: logoutLoader,
    children: [{ path: '/logout' }],
  },
])
