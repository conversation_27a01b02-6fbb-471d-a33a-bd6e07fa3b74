import { type RouteObject } from 'react-router'
import { withMetaTags } from './router-utils'
import { SourcesPage, TasksStatusPage, AuditLogsPage } from '../pages'
import { SettingsLayout } from '../pages/settings/layout'
import { JiraAttributesPage } from '../pages/settings/jira-attributes/page'
import { UsersManagementPage } from '../pages/settings/users-management/page'
import { AddUserPage } from '../pages/settings/users-management/add-user/page'
import { SecurityFrameworksPage } from '../pages/settings/security-frameworks/page'
import { CustomRecommendationsPage } from '../pages/settings/custom-recommendations/page'
import { AuthenticationPage } from '../pages/settings/authentication/page'
import { NotificationsPage } from '../pages/settings/notifications/page'

export const settingsRoutes: RouteObject[] = [
  {
    element: <SettingsLayout />,
    children: [
      {
        path: 'settings/security-frameworks',
        element: withMetaTags(
          'Security Frameworks',
          'Security Frameworks page',
          <SecurityFrameworksPage />
        ),
      },
      {
        path: 'settings/sources',
        element: withMetaTags('Sources', 'Sources page', <SourcesPage />),
      },
      {
        path: 'settings/tasks-status',
        element: withMetaTags(
          'Tasks Status',
          'Tasks status page',
          <TasksStatusPage />
        ),
      },
      {
        path: 'settings/jira-attributes',
        element: withMetaTags(
          'Jira Attributes',
          'Jira attributes selection',
          <JiraAttributesPage />
        ),
      },
      {
        path: 'settings/users-management',
        element: withMetaTags(
          'Users Management',
          'Users management page',
          <UsersManagementPage />
        ),
      },
      {
        path: 'settings/users-management/add-user',
        element: withMetaTags('Add User', 'Add user page', <AddUserPage />),
      },
      {
        path: 'settings/custom-recommendations',
        element: withMetaTags(
          'Custom Recommendations',
          'Custom Recommendations page',
          <CustomRecommendationsPage />
        ),
      },
      {
        path: 'settings/audit-logs',
        element: withMetaTags(
          'Audit Logs',
          'Audit logs page',
          <AuditLogsPage />
        ),
      },
      {
        path: 'settings/authentication',
        element: withMetaTags(
          'Authentication',
          'Authentication page',
          <AuthenticationPage />
        ),
      },
      {
        path: 'settings/notifications',
        element: withMetaTags(
          'Notifications settings',
          'Notifications settings page',
          <NotificationsPage />
        ),
      },
    ],
  },
]
