import { Link } from 'react-router'
import { LogIn, TriangleAlertIcon } from 'lucide-react'
import { Button } from '@libs/ui'
import { t } from 'i18next'

interface LoginErrorProps {
  errorDetails?: string
}
export const LoginError = ({ errorDetails }: LoginErrorProps) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 p-4">
      <TriangleAlertIcon size={50} color="red" />
      {errorDetails ? (
        <h1 className="text-2xl font-bold my-4">{errorDetails}</h1>
      ) : (
        <div>
          <p className="text-gray-600 mb-4">
            {t('errors.anUnexpectedError')}. {t('pleaseTryAgainLater')}.
          </p>
        </div>
      )}
      <Link to="/">
        <Button
          variant="default"
          className="gap-4 capitalize"
          dataTestId="go-to-login"
        >
          <LogIn size={18} />
          {t('goToLogin')}
        </Button>
      </Link>
    </div>
  )
}
