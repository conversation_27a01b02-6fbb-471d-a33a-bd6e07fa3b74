import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oader,
  <PERSON>put,
  <PERSON>go,
} from '@libs/ui'
import { Circle, CircleCheck, CircleX, LockIcon } from 'lucide-react'
import React, { useState } from 'react'
import { t } from 'i18next'

interface MfaProps {
  email: string
  handleReset: any
  isPending: boolean
  resetError: string
}
export const ResetPassword = ({
  email,
  isPending,
  resetError,
  handleReset,
}: MfaProps) => {
  const [password, setPassword] = useState<string>('')
  const [confirmPassword, setConfirmPassword] = useState<string>('')
  const [passwordTouched, setPasswordTouched] = useState<boolean>(false)
  const [confirmPasswordTouched, setConfirmPasswordTouched] =
    useState<boolean>(false)

  const isLengthValid = password.length >= 8
  const hasNumber = /\d/.test(password)
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasSpecialCharacter = /[^\w\s]|(?<=\S)\s(?=\S)/.test(password)
  const passwordsMatch = password === confirmPassword

  const isFormValid =
    isLengthValid &&
    hasNumber &&
    hasUpperCase &&
    hasLowerCase &&
    hasSpecialCharacter &&
    passwordsMatch

  const renderIcon = (condition: boolean, touched: boolean) => {
    if (!touched)
      return <Circle className="text-gray-400 w-4 h-4 flex-shrink-0" />
    return condition ? (
      <CircleCheck className="text-green-600 w-4 h-4 flex-shrink-0" />
    ) : (
      <CircleX className="text-red-600 w-4 h-4 flex-shrink-0" />
    )
  }

  return (
    <div className="flex justify-center flex-col items-center">
      <Card className="w-full max-w-xs">
        <CardHeader>
          <CardTitle className="text-center">
            <Logo className="w-full flex justify-center mb-6" />
            <span className="text-2xl ">
              {t('resetPasswordTitle')} {email}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 items-center">
          <div className="w-full relative ml-auto flex-1 md:grow-0">
            <LockIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              onChange={(e) => setPassword(e.target.value)}
              onBlur={() => setPasswordTouched(true)}
              className="w-full pl-8"
              id="password"
              type="password"
              placeholder="Password"
              value={password}
              required
            />
            <p className="text-gray-400 font-normal text-left mt-6 text-sm">
              Password requirements:
              <ol>
                <li className="flex gap-2 mt-2 items-center">
                  {renderIcon(isLengthValid, passwordTouched)}
                  {t('passwordRequirements.validLength')}
                </li>
                <li className="flex gap-2 mt-2 items-center">
                  {renderIcon(hasNumber, passwordTouched)}
                  {t('passwordRequirements.hasNumber')}
                </li>
                <li className="flex gap-2 mt-2 items-center">
                  {renderIcon(hasUpperCase, passwordTouched)}
                  {t('passwordRequirements.hasUpperCase')}
                </li>
                <li className="flex gap-2 mt-2 items-center">
                  {renderIcon(hasLowerCase, passwordTouched)}
                  {t('passwordRequirements.hasLowerCase')}
                </li>
                <li className="flex gap-2 mt-2 items-center">
                  {renderIcon(hasSpecialCharacter, passwordTouched)}
                  {t('passwordRequirements.hasSpecialCharacter')}
                </li>
              </ol>
            </p>
          </div>
          <div className="w-full relative ml-auto flex-1 md:grow-0">
            <LockIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              onChange={(e) => setConfirmPassword(e.target.value)}
              onBlur={() => setConfirmPasswordTouched(true)}
              className="w-full pl-8"
              id="confirm-password"
              type="password"
              placeholder="Confirm Password"
              value={confirmPassword}
              required
            />
            {confirmPasswordTouched && !passwordsMatch && (
              <div className="mt-4 text-sm text-red-600 flex items-center gap-1 border rounded-md border-red-600 p-2 bg-red-50">
                <CircleX />
                {t('passwordRequirements.passwordsMatch')}
              </div>
            )}
          </div>
          {resetError && (
            <div className="mt-4 text-sm text-red-600 flex items-center gap-1 border rounded-md border-red-600 p-2 bg-red-50">
              <CircleX />
              {resetError}
            </div>
          )}
          <Button
            className="w-full mt-4"
            onClick={() => handleReset(password)}
            disabled={!isFormValid || isPending}
            dataTestId="reset-password"
          >
            <span>{t('submit')}</span>
            {isPending && (
              <div className="flex ml-2">
                <ClipLoader
                  data-testid="loading-spinner"
                  color="white"
                  size={20}
                />
              </div>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
