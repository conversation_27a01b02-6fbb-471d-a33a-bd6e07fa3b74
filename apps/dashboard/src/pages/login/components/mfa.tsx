import { useEffect, useRef, useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  ClipLoader,
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
  Logo,
} from '@libs/ui'
import QRCode from 'qrcode'
import { useTranslation } from 'react-i18next'
import type { AuthLoginResponse } from 'prime-front-service-client'
import { CircleX } from 'lucide-react'

interface MfaProps {
  email: string
  mfaDetails: AuthLoginResponse
  handleMfa: any
  isPending: boolean
  mfaError: string
}
export const Mfa = ({
  mfaDetails,
  email,
  isPending,
  mfaError,
  handleMfa,
}: MfaProps) => {
  const { t } = useTranslation()

  const [setupMfa] = useState(mfaDetails.shared_code)
  const [code, setCode] = useState('')

  const canvasRef = useRef(null)

  useEffect(() => {
    const generateQRCode = async () => {
      if (mfaDetails.shared_code) {
        const otpAuthURL = `otpauth://totp/${email}?secret=${mfaDetails.shared_code}&issuer=Prime`
        await QRCode.toCanvas(canvasRef.current, otpAuthURL)
      }
    }
    generateQRCode().then()
  }, [mfaDetails])

  return (
    <div className="flex justify-center flex-col items-center">
      <Card className="w-full max-w-xs">
        <CardHeader>
          <CardTitle className="text-2xl text-center">
            <Logo className="w-full flex justify-center mb-6" />
            {setupMfa ? t('configureMfaTitle') : t('mfaTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 items-center">
          {setupMfa ? (
            <div>
              <div className="font-light text-sm text-gray-500 mb-4">
                {t('configureMfaDescription')}
              </div>
              <div className="w-full flex items-center justify-center">
                <canvas
                  data-testid="qr-code"
                  className="mb-4"
                  ref={canvasRef}
                ></canvas>
              </div>
              <div className="font-light text-sm text-gray-500">
                {t('configureMfaCode')}
              </div>
            </div>
          ) : (
            <div>
              <div className="font-light text-sm text-gray-500 ">
                {t('mfaDescription')}
              </div>
            </div>
          )}
          <div className="w-full flex items-center justify-center mt-2">
            <InputOTP maxLength={6} onChange={(value) => setCode(value)}>
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
              </InputOTPGroup>
              <InputOTPSeparator />
              <InputOTPGroup>
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
          </div>
          {mfaError && (
            <div className="mt-4 text-sm text-red-600 flex items-center gap-1 border rounded-md border-red-600 p-2 bg-red-50">
              <CircleX />
              {mfaError}
            </div>
          )}
          <Button
            className="w-full mt-4"
            dataTestId="submit-mfa"
            onClick={() =>
              handleMfa(setupMfa, {
                email: email,
                session: mfaDetails.session,
                code: code,
              })
            }
          >
            <span>{t('submit')}</span>
            {isPending && (
              <div className="flex ml-2">
                <ClipLoader
                  data-testid="loading-spinner"
                  color="white"
                  size={20}
                />
              </div>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
