/*eslint-disable max-lines*/
import type { ElementType, FormEvent } from 'react'
import { useState } from 'react'
import {
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  ClipLoader,
  GoogleIcon,
  Input,
  Logo,
  MicrosoftIcon,
  OktaIcon,
} from '@libs/ui'
import { CircleX, LockIcon, MailIcon } from 'lucide-react'
import { toast } from 'sonner'
import {
  login,
  mfaChallenge,
  mfaSetup,
  newPasswordRequired,
  useIdentify,
} from '../../api/use-auth-api'
import { Mfa } from './components/mfa'
import type {
  AuthLoginResponse,
  MfaSetupRequest,
} from 'prime-front-service-client'
import { Link, useSearchParams } from 'react-router'
import { LoginError } from './components/login-error'
import { AuthLoginResponseType } from 'prime-front-service-client'
import { ResetPassword } from './components/reset-password'
import { useTranslation } from 'react-i18next'

const providerIcons: Record<string, ElementType> = {
  Google: GoogleIcon,
  Okta: OktaIcon,
  Microsoft: MicrosoftIcon,
}

const IconComponent = ({ providerName }: { providerName: string }) => {
  const Icon = providerIcons[providerName]
  return Icon ? <Icon /> : null
}
export const LoginPage = () => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const ssoLoginError = searchParams.get('error')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showProviders, setShowProviders] = useState(false)
  const [loginError, setLoginError] = useState('')
  const [mfaError, setMfaError] = useState('')
  const [loginData, setLoginData] = useState<AuthLoginResponse>()
  const { isPending, data, isSuccess, mutateAsync } = useIdentify()
  const [mfaLoading, setMfaLoading] = useState(false)
  const [passwordRequiredLoading, setPasswordRequiredLoading] = useState(false)
  const [passwordRequiredError, setPasswordRequiredError] = useState('')

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    await mutateAsync(email, {
      onError: () => {
        toast.error(t('errors.failedToIdentifyUser'))
      },
      onSuccess: () => {
        setShowProviders(true)
      },
    })
  }

  const handleMfa = async (setup: boolean, mfaRequest: MfaSetupRequest) => {
    setMfaError(t(''))
    setMfaLoading(true)
    if (setup) {
      try {
        await mfaSetup(mfaRequest)
      } catch (error) {
        if (error instanceof Error && error.message === 'incorrectCode') {
          setMfaError(t('errors.incorrectMfaCode'))
        }
        console.error('MFA Login failed:', error)
      }
    } else {
      try {
        await mfaChallenge(mfaRequest)
      } catch (error) {
        if (error instanceof Error && error.message === 'incorrectCode') {
          setMfaError(t('errors.incorrectMfaCode'))
        }
        console.error('MFA Login failed:', error)
      }
    }
    setMfaLoading(false)
  }

  const handleNewPassword = async (newPassword: string) => {
    setPasswordRequiredLoading(true)
    try {
      setPasswordRequiredError('')
      const res = await newPasswordRequired({
        email: email,
        session: loginData?.session || '',
        new_password: newPassword,
      })
      if (res) {
        const resData = await res.json()
        setLoginData(resData)
      }
    } catch (error) {
      if (
        error instanceof Error &&
        error.message === 'incorrectUserOrPassword'
      ) {
        setPasswordRequiredError(t('errors.incorrectUserOrPassword'))
      }
      console.error('Login failed:', error)
    }
  }

  const handleLogin = async () => {
    if (email === '' || password === '') {
      setLoginError(t('errors.emailAndPasswordRequired'))
      return
    }

    try {
      setLoginError('')
      const res = await login({
        email,
        password,
      })
      if (res) {
        const resData: AuthLoginResponse = await res.json()
        setLoginData(resData)
      }
    } catch (error) {
      if (
        error instanceof Error &&
        error.message === 'incorrectUserOrPassword'
      ) {
        setLoginError(t('errors.incorrectUserOrPassword'))
      }
      console.error('Login failed:', error)
    }
  }

  return ssoLoginError ? (
    <LoginError errorDetails={ssoLoginError} />
  ) : (
    <div className="flex justify-center flex-col items-center mt-10">
      {!loginData && (
        <Card className="w-full max-w-xs">
          <CardHeader>
            <CardTitle className="text-2xl text-center">
              <Logo className="w-full flex justify-center mb-6" />
              {t('login.login')}
            </CardTitle>
          </CardHeader>
          <CardContent className="grid gap-4">
            {showProviders ? (
              <>
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4">
                    <div className="grid gap-4">
                      <div className="w-full relative ml-auto flex-1 md:grow-0">
                        <MailIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          onChange={(e) => {
                            setEmail(e.target.value)
                            setLoginError('')
                          }}
                          className="w-full pl-8"
                          id="email"
                          type="email"
                          placeholder="Email"
                          value={email}
                          required
                        />
                      </div>

                      <div className="w-full relative ml-auto flex-1 md:grow-0">
                        <LockIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          onChange={(e) => {
                            setPassword(e.target.value)
                            setLoginError('')
                          }}
                          className="w-full pl-8"
                          id="password"
                          type="password"
                          placeholder="Password"
                          value={password}
                          required
                        />
                      </div>
                    </div>
                    <Button
                      onClick={handleLogin}
                      disabled={isPending || email === '' || password === ''}
                      className="w-full"
                      dataTestId="login-with-password"
                    >
                      <span>{t('login.loginWithPassword')}</span>
                      {isPending && (
                        <div className="flex ml-2">
                          <ClipLoader color="white" size={24} />
                        </div>
                      )}
                    </Button>
                  </div>
                  {loginError && (
                    <div className="mt-4 text-sm text-red-600 flex items-center gap-1 border rounded-md border-red-600 p-2 bg-red-50">
                      <CircleX />
                      {loginError}
                    </div>
                  )}
                </form>
                <div className="flex gap-3 w-full flex-row items-center justify-center my-3">
                  <hr role="separator" className="border-b-1 flex-1" />
                  <span className="text-sm text-muted-foreground">or</span>
                  <hr role="separator" className="border-b-1 flex-1" />
                </div>
                <div className="grid gap-4">
                  {isSuccess &&
                    data.saml_providers
                      .filter((provider) => provider.name !== 'COGNITO')
                      .map((provider, i) => (
                        <Button
                          key={i}
                          className="w-full gap-2"
                          dataTestId="login-with"
                          asChild
                        >
                          <a href={provider.url}>
                            <span>
                              <IconComponent providerName={provider.name} />
                            </span>
                            {t('login.loginWith')} {provider.name}
                          </a>
                        </Button>
                      ))}
                </div>
              </>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <div className="w-full relative ml-auto flex-1 md:grow-0">
                      <MailIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full pl-8"
                        id="email"
                        type="email"
                        placeholder="Email"
                        value={email}
                        required
                      />
                    </div>
                  </div>
                  <Button
                    disabled={isPending || email === ''}
                    className="w-full"
                    type="submit"
                    dataTestId="continue"
                  >
                    <span>{t('continue')}</span>
                    {isPending && (
                      <div className="flex ml-2">
                        <ClipLoader
                          data-testid="loading-indicator"
                          color="white"
                          size={20}
                        />
                      </div>
                    )}
                  </Button>
                </div>
              </form>
            )}
          </CardContent>
        </Card>
      )}
      {loginData?.response_type === AuthLoginResponseType.MFA_REQUIRED && (
        <Mfa
          mfaDetails={loginData}
          email={email}
          handleMfa={handleMfa}
          isPending={mfaLoading}
          mfaError={mfaError}
        />
      )}
      {loginData?.response_type ===
        AuthLoginResponseType.NEW_PASSWORD_REQUIRED && (
        <ResetPassword
          email={email}
          handleReset={handleNewPassword}
          isPending={passwordRequiredLoading}
          resetError={passwordRequiredError}
        />
      )}
      <p className="w-full max-w-xs text-xs text-muted-foreground mt-10 text-center">
        {t('login.loginPolicy')}
        <Link
          className="ml-1 underline"
          to={'https://www.primesec.ai/policies/privacy-policy'}
          target="_blank"
        >
          {t('login.privacyPolicy')}
        </Link>
      </p>
    </div>
  )
}
