import { Document, Page, pdfjs } from 'react-pdf'
import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'
import { useEffect, useState } from 'react'
import { Button, Skeleton } from '@libs/ui'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { toast } from 'sonner'
import { apiConfig } from '@libs/common'
import { mapHighlights, normalize } from './util'
import type { DesignDocType } from 'prime-front-service-client'

const { basePath } = apiConfig

pdfjs.GlobalWorkerOptions.workerSrc = `${window.location.origin}/pdf.worker.min.js`

interface DocumentDisplayProps {
  documentId: number
  highlights?: string[]
  highlightedPage: number | null
  onHighlightDetected: (highlight: string, page: number) => void
  designDocType?: DesignDocType
}

export const DocumentDisplay = ({
  documentId,
  highlights = [],
  highlightedPage,
  onHighlightDetected,
  designDocType,
}: DocumentDisplayProps) => {
  const [loading, setLoading] = useState<boolean>(false)
  const [pdfBlobUrl, setPdfBlobUrl] = useState<string | null>(null)
  const [numPages, setNumPages] = useState<number | null>(null)
  const [pageNumber, setPageNumber] = useState(1)
  const [perPageHighlights, setPerPageHighlights] = useState<
    Record<number, string[]>
  >({})

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages)
    setPageNumber(1)
  }

  useEffect(() => {
    if (highlightedPage !== null) {
      setPageNumber(highlightedPage)
    }
  }, [highlightedPage])

  useEffect(() => {
    if (designDocType === 'container') {
      return
    }
    const fetchPdf = async () => {
      const baseUrl = `${basePath}/design-docs/download/${documentId}`
      try {
        setLoading(true)
        const response = await fetch(baseUrl, {
          credentials: 'include',
        })
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        setPdfBlobUrl(url)
      } catch (error) {
        toast.error('Failed to fetch file. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    if (documentId && designDocType) {
      fetchPdf()
    }
  }, [designDocType, documentId])

  const highlightTextLayer = () => {
    const highlights = mapHighlights(perPageHighlights[pageNumber] || [])
    if (highlights.length === 0) return

    const textLayers = document.querySelectorAll(
      '.react-pdf__Page__textContent'
    )

    textLayers.forEach((layer) => {
      const spans = Array.from(layer.querySelectorAll('span'))
      if (spans.length === 0) return

      const spanTextList = spans.map((span) => ({
        rawText: span.textContent || '',
        text: normalize(span.textContent || ''),
        node: span,
      }))

      const fullTextSpaced = spanTextList.map((s) => s.text).join(' ')
      const fullTextCompact = spanTextList.map((s) => s.text).join('')

      const fullTextOffsets: number[] = []
      let cursor = 0
      for (const s of spanTextList) {
        fullTextOffsets.push(cursor)
        cursor += s.text.length
      }

      highlights.forEach((rawHighlight) => {
        const normalizedHighlight = normalize(rawHighlight)
        if (!normalizedHighlight) return

        const matchInFullText = (text: string) => {
          const matchIndexes: number[] = []
          let idx = text.indexOf(normalizedHighlight)
          while (idx !== -1) {
            matchIndexes.push(idx)
            idx = text.indexOf(
              normalizedHighlight,
              idx + normalizedHighlight.length
            )
          }
          return matchIndexes
        }

        const matchIndexes = [
          ...matchInFullText(fullTextCompact),
          ...matchInFullText(fullTextSpaced),
        ]

        matchIndexes.forEach((startIdx) => {
          const endIdx = startIdx + normalizedHighlight.length

          for (let i = 0; i < spanTextList.length; i++) {
            const spanStart = fullTextOffsets[i]
            const spanEnd = spanStart + spanTextList[i].text.length

            if (spanEnd < startIdx) continue
            if (spanStart > endIdx) break

            const span = spanTextList[i].node
            span.style.backgroundColor = 'rgba(13, 148, 136, 0.3)'
            span.style.backgroundBlendMode = 'multiply'
            span.style.borderRadius = '2px'
          }
        })
      })
    })
  }

  useEffect(() => {
    const timeout = setTimeout(() => {
      highlightTextLayer()
    }, 300)

    return () => clearTimeout(timeout)
  }, [pageNumber, perPageHighlights])

  const goToPrevPage = () => setPageNumber((prev) => Math.max(prev - 1, 1))
  const goToNextPage = () =>
    setPageNumber((prev) => Math.min(prev + 1, numPages || 1))

  useEffect(() => {
    const detectHighlightsInAllPages = async () => {
      if (!pdfBlobUrl || !highlights?.length) return

      const loadingTask = pdfjs.getDocument(pdfBlobUrl)
      const pdf = await loadingTask.promise

      const mappedHighlights = mapHighlights(highlights)
      const detected = new Set<string>()
      const pageHighlightMap: Record<number, string[]> = {}

      const getPageText = async (pageNum: number) => {
        const page = await pdf.getPage(pageNum)
        const textContent = await page.getTextContent()
        const textItems = textContent.items
          .map((item) => ('str' in item ? normalize(item.str) : ''))
          .filter((str) => str && str.length > 2)

        return textItems.join(' ')
      }

      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const currentPageText = await getPageText(pageNum)
        const nextPageText =
          pageNum < pdf.numPages ? await getPageText(pageNum + 1) : ''
        const combinedText = `${currentPageText} ${nextPageText}`

        mappedHighlights.forEach((highlight) => {
          const key = `${highlight}|${pageNum}`
          const highlightNormalized = normalize(highlight)

          if (!detected.has(key)) {
            const inCurrent = currentPageText.includes(highlightNormalized)
            const inCombined = combinedText.includes(highlightNormalized)

            if (inCurrent) {
              detected.add(key)
              onHighlightDetected(highlight, pageNum)
              pageHighlightMap[pageNum] = [
                ...(pageHighlightMap[pageNum] || []),
                highlight,
              ]
            } else if (inCombined) {
              detected.add(key)
              onHighlightDetected(highlight, pageNum)

              const splitIndex = currentPageText.length
              const firstPart = highlightNormalized.slice(0, splitIndex).trim()
              const secondPart = highlightNormalized.slice(splitIndex).trim()

              if (firstPart.length > 2) {
                pageHighlightMap[pageNum] = [
                  ...(pageHighlightMap[pageNum] || []),
                  firstPart,
                ]
              }
              if (secondPart.length > 2) {
                pageHighlightMap[pageNum + 1] = [
                  ...(pageHighlightMap[pageNum + 1] || []),
                  secondPart,
                ]
              }
            }
          }
        })
      }

      setPerPageHighlights(pageHighlightMap)
    }

    detectHighlightsInAllPages()
  }, [pdfBlobUrl, highlights])

  if (loading) {
    return (
      <div
        data-testid="loading-indicator"
        className="flex justify-center items-center min-h-96"
      >
        <div className="flex-col gap-4 items-center">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="w-full ">
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-64" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full">
      {pdfBlobUrl && (
        <div className="w-full h-full flex flex-col flex-1 min-h-0 items-center">
          <div className="flex-1 min-h-0  overflow-auto max-w-full rounded-xl shadow">
            <Document file={pdfBlobUrl} onLoadSuccess={onDocumentLoadSuccess}>
              <Page
                pageNumber={pageNumber}
                width={
                  document
                    ?.getElementById('pdf-container')
                    ?.getBoundingClientRect().width || 800
                }
              />
            </Document>
          </div>

          <div className="flex w-full items-center justify-end my-2">
            <Button
              variant="ghost"
              dataTestId="prev-page"
              onClick={goToPrevPage}
              disabled={pageNumber <= 1}
            >
              <ChevronLeft size={18} />
            </Button>

            <span className="text-sm">
              Page {pageNumber} of {numPages}
            </span>

            <Button
              variant="ghost"
              dataTestId="next-page"
              onClick={goToNextPage}
              disabled={pageNumber >= (numPages || 1)}
            >
              <ChevronRight size={18} />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
