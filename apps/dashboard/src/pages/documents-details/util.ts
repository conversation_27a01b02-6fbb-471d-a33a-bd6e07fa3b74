import type {
  ExternalCaseWorkroom,
  ExternalContainer,
} from 'prime-front-service-client'

type Item = {
  id: string
  title: string
  type: string
  children?: Item[]
}
export const mapHighlights = (highlights: string[]) => {
  return highlights?.flatMap((item) =>
    item
      .split(/[●○•,\n]/)
      .map((part) => part.trim())
      .filter(Boolean)
      .map((part) =>
        part
          .replace(/\[diagram\]/gi, '')
          .trim()
          .toLowerCase()
      )
  )
}

export const normalize = (text: string) =>
  text
    .toLowerCase()
    .replace(/[.,/#!$%^&*;:{}=\-_`~()"[\]\\']/g, '')
    .replace(/\s+/g, ' ')
    .trim()

export const formatMermaidDiagram = (diagram: string | undefined): string => {
  if (!diagram) return ''

  return diagram
    .replace(/```mermaid/, '')
    .replace(/```$/, '')
    .replace(/\\n/g, '\n')
    .trim()
}

export function buildTree(
  containerId: string,
  issueSummaryData: ExternalContainer,
  flatItems: ExternalCaseWorkroom[]
): Item {
  const nodeMap: Record<string, Item> = {}
  const rootChildren: Item[] = []

  for (const issue of flatItems) {
    nodeMap[issue?.issue_id] = {
      id: issue.issue_id,
      title: issue.title,
      type: issue.provider_fields?.issuetype || '',
      children: [],
    }
  }

  for (const issue of flatItems) {
    const currentNode = nodeMap[issue.issue_id]
    const parentIds = issue.parents || []
    const firstParentId = parentIds[0]
    if (firstParentId && nodeMap[firstParentId]) {
      nodeMap[firstParentId].children!.push(currentNode)
    } else {
      rootChildren.push(currentNode)
    }
  }

  return {
    id: containerId,
    title: issueSummaryData?.title || '',
    type: issueSummaryData?.provider_fields?.issuetype || '',
    children: rootChildren,
  }
}
