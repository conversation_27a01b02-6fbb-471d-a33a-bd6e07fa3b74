/*eslint-disable max-lines*/
import { Link, NavLink, useParams } from 'react-router'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTrigger,
  Button,
  Card,
  ClipLoader,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  FileDownloader,
  MermaidDiagram,
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  Switch,
  Tabs,
  TabsList,
  TabsTrigger,
} from '@libs/ui'
import {
  useDesignDocById,
  useReprocessDesignDoc,
  useUpdateDesignDoc,
} from '../../api/use-design-docs-api'
import Markdown from 'react-markdown'
import { t } from 'i18next'
import { useEffect, useMemo, useState } from 'react'
import { RecommendationItem } from './components/recommendation-item'
import { ChatContent } from '../../components/chatbot/chat-content'
import {
  ExternalLink,
  FileSpreadsheet,
  Link2Icon,
  RefreshCw,
  SparklesIcon,
  Upload,
} from 'lucide-react'
import { DocumentDisplay } from './document-display'
import { formatMermaidDiagram } from './util'
import { ContainerDetails } from './components/container-details'
import { AttackScenarioItem } from './components/attack-scenario-item'
import { toast } from 'sonner'
import { serializeWorkroom } from '../../router/router-utils'
import {
  defaultFilterParams,
  defaultSortParam,
  getParsedArrayOfObjects,
} from '../../router/router'
import { staticColumns } from '../workroom/components'
import { useAtomValue } from 'jotai/index'
import { defaultViewAtom } from '../../config/store'
import { apiConfig } from '@libs/common'
import { DocumentUpload } from '../documents/components/document-upload'
import type { ResponseError } from 'prime-front-service-client'
import { duplicateCode } from '../../api/error-map'
import { MitigationModal } from './components/mitigation-modal'
import { MitigationPlanHistory } from './components/mitigation-plan-history'
import { parseAsStringLiteral, useQueryState } from 'nuqs'
import { useFlagsWrapper } from '../../hooks/use-flags-wrapper'

const MAX_SIZE = 4.5
const { basePath } = apiConfig

const TabsProps = [
  'summary',
  'recommendations',
  'attackVectors',
  'mitigationPlan',
] as const

export const DocumentsDetailsPage = () => {
  const { id } = useParams()
  const { mitigationPlan } = useFlagsWrapper()
  const { data: docItem, refetch } = useDesignDocById(id as any)

  const updateDesignDocMutation = useUpdateDesignDoc()

  const defaultView = useAtomValue(defaultViewAtom)

  const reprocessDesignDocMutation = useReprocessDesignDoc()

  const [mermaidType, setMermaidType] = useState<'system' | 'data'>('system')

  const [open, setOpen] = useState<boolean>(false)

  const [display, setDisplay] = useQueryState(
    'display',
    parseAsStringLiteral(TabsProps).withDefault('summary')
  )

  const [selectedScenario, setSelectedScenario] = useState('')

  const [showCompanyRecommendations, setShowCompanyRecommendations] =
    useState<boolean>(true)

  const [highlightedPages, setHighlightedPages] = useState<Map<string, number>>(
    new Map()
  )
  const [highlightedPage, setHighlightedPage] = useState<number | null>(null)

  const workroomState = {
    f: [
      ...defaultFilterParams,
      {
        field: 'parent_id',
        op: 'eq',
        value: [docItem?.issue_id],
      },
    ],
    s: defaultView?.s
      ? getParsedArrayOfObjects(defaultView.s)
      : [defaultSortParam],
    selected_columns: defaultView?.selected_columns || staticColumns,
  }

  const onScenarioClick = (scenario: string) => {
    setSelectedScenario(scenario)
    setDisplay('attackVectors')
  }

  useEffect(() => {
    if (display === 'recommendations' || display === 'summary') {
      setSelectedScenario('')
    }
  }, [display])

  const reprocessDocument = () => {
    reprocessDesignDocMutation.mutate(
      { doc_id: docItem?.id || 0 },
      {
        onSuccess: () => {
          toast.success(t('reprocessSuccess'))
        },
        onError: () => {
          toast.error(t('failedToReprocess'))
        },
      }
    )
  }

  const onHighlightDetected = (highlight: string, page: number) => {
    setHighlightedPages((prev) => {
      const updated = new Map(prev)
      updated.set(highlight, page)
      return updated
    })
  }
  const onHighlightClick = (highlight: string) => {
    const page = highlightedPages.get(highlight)
    if (page) {
      setHighlightedPage(page)
    }
  }

  const formattedDiagram = formatMermaidDiagram(docItem?.mermaid_diagram || '')
  const formattedDataDiagram = formatMermaidDiagram(
    docItem?.dataflow_diagram || ''
  )

  const topRecommendations = docItem?.top_recommendations || []

  const companyRecommendations = docItem?.policy_recommendations || []

  const recommendationsToRender =
    showCompanyRecommendations && !!companyRecommendations?.length
      ? companyRecommendations
      : topRecommendations

  const highlights = useMemo(() => {
    return (
      docItem?.top_recommendations?.flatMap(
        (rec) => rec.quotes?.map((quote) => quote) ?? []
      ) ?? []
    )
  }, [docItem])

  const sourceFileLink = useMemo(
    () => `${basePath}/design-docs/download/${docItem?.id}`,
    [docItem]
  )

  const handleUpload = (files: File[]) => {
    const loadingToastId = toast.loading(
      `Uploading ${files.length} document(s)...`
    )

    if (docItem?.id)
      updateDesignDocMutation.mutate(
        {
          doc_id: docItem.id || 0,
          design_doc_file: files[0],
        },
        {
          onSuccess: () => {
            toast.success(
              t('documentsUploadSuccess', { docsNumber: files.length }),
              {
                description: (
                  <div>
                    <Link
                      to={{
                        pathname: '/settings/tasks-status',
                      }}
                      className="underline"
                    >
                      {t('checkStatusOfUploadedFiles')}
                    </Link>
                  </div>
                ),
                duration: 8000,
              }
            )
            refetch()
            setOpen(false)
          },
          onError: async (error: unknown) => {
            const errorStatus = await (error as ResponseError)?.response?.status

            if (errorStatus === duplicateCode) {
              toast.error(t('errors.designReviewAlreadyExist'))
            } else {
              toast.error(t('errors.failedToUploadDocuments'))
            }
          },
          onSettled: () => {
            toast.dismiss(loadingToastId)
          },
        }
      )
  }

  return (
    <div className="w-full p-4 h-screen flex flex-col">
      <Card className="w-full  border-none dropshadow shadow-xl flex-col mb-4 rounded-3xl">
        <div className="border-b border-gray-300 flex items-center p-4">
          <div className="w-2 h-2 bg-muted-foreground mask mask-diamond mr-2 transform rotate-45"></div>
          <NavLink
            to="/design-reviews"
            className="text-sm text-muted-foreground underline capitalize"
          >
            {t('securityReviews')}
          </NavLink>
        </div>
        <div className="flex justify-between items-center gap-4 p-4 pb-8">
          <h1 className="text-3xl font-semibold tracking-tight ">
            {docItem?.title}
          </h1>

          <div className="flex items-center gap-2">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" dataTestId="ask-prime-button">
                  <SparklesIcon className="mr-2 h-4 w-4" />
                  {t('askPrime')}
                </Button>
              </SheetTrigger>
              <SheetContent className="min-w-[600px] overflow-auto">
                <SheetHeader>
                  <SheetTitle>PrimeChat</SheetTitle>
                </SheetHeader>
                <div className="pt-4 mb-8 h-full">
                  <ChatContent chat_location="design_doc" doc_id={Number(id)} />
                </div>
              </SheetContent>
            </Sheet>
            <AlertDialog>
              <AlertDialogTrigger>
                <Button
                  dataTestId="reprocess"
                  variant="outline"
                  className="capitalize flex items-center gap-2"
                >
                  <RefreshCw size={16} />
                  {t('reprocess')}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <h1 className="font-medium text-lg">{t('areYouSure')}</h1>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>
                    <Button
                      dataTestId="reprocess-cancel"
                      variant="outline"
                      className="capitalize flex items-center gap-2"
                    >
                      {t('cancel')}
                    </Button>
                  </AlertDialogCancel>
                  <AlertDialogAction>
                    <Button
                      dataTestId="reprocess-confirm"
                      className="capitalize flex items-center gap-2 "
                      onClick={() => reprocessDocument()}
                    >
                      {t('reprocess')}{' '}
                      {reprocessDesignDocMutation?.isPending && (
                        <div className="flex ml-2">
                          <ClipLoader data-testid="loading-spinner" size={20} />
                        </div>
                      )}
                    </Button>
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </Card>

      <div className="h-full flex gap-2 flex-1 overflow-hidden">
        <Card className="w-1/2 min-w-[300px] max-w-[50%] flex-shrink overflow-auto dropshadow shadow-xl border-none rounded-3xl relative">
          <Tabs
            className="p-4"
            defaultValue={display}
            value={display}
            onValueChange={(value: string) =>
              setDisplay(value as 'summary' | 'recommendations')
            }
          >
            <TabsList>
              <TabsTrigger value="summary" className="capitalize">
                {t('summary')}
              </TabsTrigger>
              {!!docItem?.attack_scenarios?.length && (
                <TabsTrigger value="attackVectors" className="capitalize">
                  {t('reviewAttackVectors')}
                </TabsTrigger>
              )}
              <TabsTrigger value="recommendations" className="capitalize">
                {t('recommendations')}
              </TabsTrigger>
              {!!recommendationsToRender.length && mitigationPlan && (
                <TabsTrigger value="mitigationPlan" className="capitalize">
                  {t('mitigationPlan')}
                </TabsTrigger>
              )}
            </TabsList>
          </Tabs>

          {display === 'summary' && (
            <div className="h-full grid 2xl:grid-rows-[1fr_1fr] gap-2 p-6">
              <div>
                <div className="space-y-6 text-slate-500 text-sm list-disc">
                  <Markdown
                    components={{
                      ul: ({ children }) => (
                        <ul className="list-disc pl-4 space-y-4">{children}</ul>
                      ),
                    }}
                  >
                    {docItem?.summary || ''}
                  </Markdown>
                </div>
              </div>
              <div className="mermaid-section mb-6">
                <Card className="border border-slate-200 rounded-2xl bg-slate-100 h-full shadow-none">
                  <div className="flex w-full items-center justify-start m-2">
                    <Tabs
                      value={mermaidType}
                      onValueChange={(value) =>
                        setMermaidType(value as 'system' | 'data')
                      }
                    >
                      <TabsList>
                        <TabsTrigger value="system" className="capitalize">
                          {t('systemFlow')}
                        </TabsTrigger>
                        <TabsTrigger value="data" className="capitalize">
                          {t('dataFlow')}
                        </TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </div>
                  {mermaidType === 'system' && formattedDiagram ? (
                    <MermaidDiagram chart={formattedDiagram} />
                  ) : mermaidType === 'data' && formattedDiagram ? (
                    <MermaidDiagram chart={formattedDataDiagram} />
                  ) : (
                    <div className="flex items-center justify-center h-full text-gray-500">
                      No diagram available
                    </div>
                  )}
                </Card>
              </div>
            </div>
          )}

          {display === 'recommendations' && (
            <div className="">
              <div className="p-6">
                {!!companyRecommendations.length && (
                  <div className="flex items-center justify-end w-full gap-2 my-2">
                    <span className="text-xs">{t('companyGuidelines')}</span>
                    <Switch
                      className="data-[state=checked]:bg-emerald-600"
                      checked={showCompanyRecommendations}
                      onCheckedChange={setShowCompanyRecommendations}
                    />
                  </div>
                )}
                {recommendationsToRender.map((recommendation, index) => (
                  <div key={recommendation.id} className="mb-2">
                    <RecommendationItem
                      recommendation={recommendation}
                      recommendationNumber={index + 1}
                      highlightedPages={highlightedPages}
                      onHighlightClick={onHighlightClick}
                      onScenarioClick={onScenarioClick}
                    />
                  </div>
                ))}
              </div>
              {mitigationPlan && recommendationsToRender.length > 0 && (
                <div className="flex justify-end sticky bottom-0 p-4 pr-4 bg-white border-t">
                  <MitigationModal
                    recommendations={recommendationsToRender}
                    docId={Number(id)}
                  />
                </div>
              )}
            </div>
          )}
          {display === 'attackVectors' && (
            <div className="p-6">
              {docItem?.attack_scenarios?.map((scenario) => (
                <AttackScenarioItem
                  key={crypto.randomUUID()}
                  isOpen={selectedScenario === scenario.name}
                  attackScenario={scenario}
                />
              ))}
            </div>
          )}
          {display === 'mitigationPlan' && (
            <div className="p-6">
              <MitigationPlanHistory docId={Number(id)} />
              <div className="absolute bottom-4 right-4 z-50">
                <FileDownloader
                  pathname={`/security-reviews/${Number(
                    id
                  )}/mitigations/export`}
                  fileType="zip"
                  fileName={`mitigation_plan_history_${
                    new Date().toISOString().split('T')[0]
                  }.zip`}
                />
              </div>
            </div>
          )}
        </Card>
        <Card className="w-1/2 flex-1 min-h-0 overflow-hidden dropshadow shadow-xl border-none rounded-3xl p-6">
          {docItem?.issue_id &&
          docItem?.source_id &&
          docItem?.doc_source_type === 'container' ? (
            <div className="h-full">
              <div className="flex w-full justify-between items-center py-4">
                <h1 className="mb-2 capitalize text-lg font-semibold">
                  {t('containersMaps')}
                </h1>
                <NavLink
                  to={{
                    pathname: '/workroom',
                    search: serializeWorkroom(workroomState),
                  }}
                  data-testid="workroom-link"
                >
                  <Button
                    className="flex items-center gap-2 capitalize"
                    variant="ghost"
                    dataTestId="go-to-workroom"
                  >
                    <ExternalLink />
                    {t('goToWorkroom')}
                  </Button>
                </NavLink>
              </div>
              <div className="overflow-auto h-full rounded-lg bg-gray-100 px-4 py-6 mb-10">
                <ContainerDetails
                  containerId={docItem?.issue_id}
                  sourceId={docItem?.source_id}
                />
              </div>
            </div>
          ) : (
            <div className="flex flex-col h-full">
              <div className="flex w-full justify-between items-center py-4">
                <h1 className="mb-2 capitalize text-lg font-semibold">
                  {t('sourceFile')}
                </h1>
                {docItem?.doc_source_type === 'url' ||
                docItem?.doc_source_type === 'reference' ? (
                  <Link
                    className="flex items-center gap-1 capitalize underline text-slate-500 text-xs"
                    to={docItem?.url || ''}
                    target="_blank"
                  >
                    <Link2Icon className="h-4 w-4" />
                    {t('source')}
                  </Link>
                ) : (
                  <div className="flex items-center gap-2">
                    <Link
                      className="flex items-center gap-1 underline text-slate-500 text-xs"
                      to={sourceFileLink}
                      target="_blank"
                    >
                      <FileSpreadsheet className="h-4 w-4" />
                      {docItem?.title}
                    </Link>
                    <Button
                      variant="ghost"
                      className="flex items-center gap-1 capitalize"
                      dataTestId="upload-new-version"
                      onClick={() => setOpen(true)}
                    >
                      <Upload />
                      {t('uploadNewVersion')}
                    </Button>
                  </div>
                )}
              </div>
              <div className="flex-1 min-h-0 overflow-auto">
                <DocumentDisplay
                  documentId={Number(id)}
                  highlights={highlights}
                  highlightedPage={highlightedPage}
                  onHighlightDetected={onHighlightDetected}
                  designDocType={docItem?.doc_source_type ?? undefined}
                />
              </div>
            </div>
          )}
        </Card>
      </div>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold capitalize">
              {t('uploadFile')}
            </DialogTitle>
          </DialogHeader>
          <DocumentUpload
            open={open}
            onOpenChange={setOpen}
            onUpload={handleUpload}
            isPending={updateDesignDocMutation?.isPending}
            maxFileSize={MAX_SIZE}
            maxFiles={1}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
