import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useDebounce } from '@libs/ui'
import { toast } from 'sonner'
import { casesAPI } from '../../../../../api/clients'
import type { SelectedCase } from '../types'
import { AUTOCOMPLETE_LIMIT } from '../types'

export const useCaseSearch = () => {
  const [searchValue, setSearchValue] = useState('')
  const [selectedCase, setSelectedCase] = useState<SelectedCase | null>(null)
  const debouncedSearch = useDebounce(searchValue, 300)

  const { data: searchResults, isLoading } = useQuery({
    queryKey: ['searchCases', debouncedSearch, AUTOCOMPLETE_LIMIT],
    queryFn: async () => {
      if (!debouncedSearch.trim()) return []
      try {
        return await casesAPI.autocompleteSearchGlobalCases({
          value: debouncedSearch,
          limit: AUTOCOMPLETE_LIMIT,
        })
      } catch (error) {
        toast.error('Failed to search cases')
        throw new Error('Failed to search cases')
      }
    },
    enabled: debouncedSearch.trim().length > 0,
    refetchOnWindowFocus: false,
    retry: false,
  })

  return {
    searchValue,
    setSearchValue,
    selectedCase,
    setSelectedCase,
    searchResults,
    isLoading,
    debouncedSearch,
  }
}
