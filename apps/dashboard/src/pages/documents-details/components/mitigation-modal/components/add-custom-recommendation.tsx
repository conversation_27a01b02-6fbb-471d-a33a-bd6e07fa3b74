import { useState } from 'react'
import { CheckIcon, Plus, XIcon } from 'lucide-react'
import { Button, Input, Textarea } from '@libs/ui'
import { t } from 'i18next'
import type { MitigationPriority } from 'prime-front-service-client'
import type { CustomRecommendationForm, MitigationItem } from '../types'
import { DatePicker, PrioritySelector } from './index'

interface AddCustomRecommendationProps {
  onAddCustomItem: (item: Omit<MitigationItem, 'id' | 'selected'>) => void
}

export const AddCustomRecommendation = ({
  onAddCustomItem,
}: AddCustomRecommendationProps) => {
  const [showForm, setShowForm] = useState(false)
  const [form, setForm] = useState<CustomRecommendationForm>({
    title: '',
    description: '',
    assignee: '',
    dueDate: null,
    priority: null,
  })

  const updateForm = (
    field: keyof CustomRecommendationForm,
    value: string | Date | MitigationPriority | null
  ) => {
    setForm((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSave = () => {
    onAddCustomItem({
      title: form.title.trim(),
      description: form.description.trim(),
      assignee: form.assignee.trim() || null,
      due_date: form.dueDate,
      priority: form.priority,
    })
    handleCancel()
  }

  const handleCancel = () => {
    setForm({
      title: '',
      description: '',
      assignee: '',
      dueDate: null,
      priority: null,
    })
    setShowForm(false)
  }

  const isFormValid = form.title.trim() && form.description.trim()

  if (!showForm) {
    return (
      <Button
        dataTestId="add-custom-recommendation"
        variant="outline"
        className="w-fit"
        onClick={() => setShowForm(true)}
      >
        <Plus className="mr-2 h-4 w-4" />
        {t('addCustomRecommendation')}
      </Button>
    )
  }

  return (
    <div className="border rounded-lg p-4 space-y-4">
      <div className="grid grid-cols-12 gap-4 items-start">
        <div className="col-span-2">
          <Textarea
            placeholder="Title"
            value={form.title}
            onChange={(e) => updateForm('title', e.target.value)}
            className="w-full min-h-[80px] resize-none"
          />
        </div>

        <div className="col-span-4">
          <Textarea
            placeholder="Description"
            value={form.description}
            onChange={(e) => updateForm('description', e.target.value)}
            className="w-full min-h-[80px] resize-none"
          />
        </div>

        <div className="col-span-2">
          <Input
            placeholder="Assignee"
            value={form.assignee}
            onChange={(e) => updateForm('assignee', e.target.value)}
            className="w-full"
          />
        </div>

        <div className="col-span-2">
          <DatePicker
            value={form.dueDate}
            onChange={(date) => updateForm('dueDate', date)}
            dataTestId="custom-due-date-button"
          />
        </div>

        <div className="col-span-2">
          <PrioritySelector
            value={form.priority}
            onChange={(priority) => updateForm('priority', priority)}
            className="w-full"
          />
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button
          size="sm"
          variant="outline"
          onClick={handleCancel}
          className="h-8 w-8 p-0"
          dataTestId="cancel-custom-recommendation"
        >
          <XIcon className="h-4 w-4" />
        </Button>
        <Button
          size="sm"
          onClick={handleSave}
          className="h-8 w-8 p-0"
          disabled={!isFormValid}
          dataTestId="save-custom-recommendation"
        >
          <CheckIcon className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
