import { useState } from 'react'
import { ChevronsUpDown } from 'lucide-react'
import {
  Button,
  Command,
  CommandGroup,
  CommandInput,
  Label,
  Popover,
  PopoverContent,
  PopoverTrigger,
  RadioGroup,
  RadioGroupItem,
  useDebounce,
} from '@libs/ui'
import { useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import { t } from 'i18next'
import { casesAPI } from '../../../../../api/clients'
import type { SelectedCase } from '../types'
import { AUTOCOMPLETE_LIMIT } from '../types'

interface CaseSelectorProps {
  selectedCase: SelectedCase | null
  onCaseSelect: (selectedCase: SelectedCase | null) => void
  disabled?: boolean
  className?: string
  dataTestId?: string
}

export const CaseSelector = ({
  selectedCase,
  onCaseSelect,
  disabled = false,
  className,
  dataTestId = 'case-selector-button',
}: CaseSelectorProps) => {
  const [open, setOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const debouncedSearch = useDebounce(searchValue, 300)

  const { data: searchResults, isLoading } = useQuery({
    queryKey: ['searchCases', debouncedSearch, AUTOCOMPLETE_LIMIT],
    queryFn: async () => {
      if (!debouncedSearch.trim()) return []
      try {
        return await casesAPI.autocompleteSearchGlobalCases({
          value: debouncedSearch,
          limit: AUTOCOMPLETE_LIMIT,
        })
      } catch (error) {
        toast.error('Failed to search cases')
        throw new Error('Failed to search cases')
      }
    },
    enabled: debouncedSearch.trim().length > 0,
    refetchOnWindowFocus: false,
    retry: false,
  })

  return (
    <Popover open={open} onOpenChange={setOpen} modal={true}>
      <PopoverTrigger asChild>
        <Button
          dataTestId={dataTestId}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          size="sm"
          className={className}
          disabled={disabled}
        >
          {selectedCase?.issue_id || t('selectTicket')}
          <ChevronsUpDown className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={t('searchCases')}
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandGroup className="max-h-[300px] overflow-y-auto">
            {isLoading ? (
              <div className="p-6 text-center text-muted-foreground">
                Loading...
              </div>
            ) : searchResults?.length ? (
              <RadioGroup
                value={selectedCase?.id?.toString() || ''}
                onValueChange={(value) => {
                  const selectedResult = searchResults?.find(
                    (c) => c.id.toString() === value
                  )
                  if (selectedResult) {
                    onCaseSelect({
                      id: selectedResult.id,
                      title: selectedResult.title?.trim() || 'Untitled',
                      issue_id: selectedResult.issue_id,
                    })
                  }
                }}
                className="p-2"
              >
                {searchResults?.map((result) => {
                  const name = result.title?.trim() || 'Untitled'
                  const caseId = result.id

                  return (
                    <div
                      key={caseId.toString()}
                      className="flex items-center space-x-2"
                    >
                      <RadioGroupItem
                        value={caseId.toString()}
                        id={caseId.toString()}
                      />
                      <Label
                        htmlFor={caseId.toString()}
                        className="flex flex-col cursor-pointer"
                      >
                        <span className="text-sm">{name}</span>
                        <span className="text-xs text-muted-foreground">
                          {result.issue_id}
                        </span>
                      </Label>
                    </div>
                  )
                })}
              </RadioGroup>
            ) : debouncedSearch.trim().length > 0 ? (
              <div className="p-6 text-center text-sm">
                {t('noTicketsFound')}
              </div>
            ) : (
              <div className="p-2 text-center text-muted-foreground text-xs truncate">
                {t('startTypingToSearchTickets')}
              </div>
            )}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
