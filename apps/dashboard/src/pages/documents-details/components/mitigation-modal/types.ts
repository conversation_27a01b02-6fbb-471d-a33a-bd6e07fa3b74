import type {
  DesignDocTopPolicyRecommendation,
  DesignDocTopRecommendation,
  MitigationPriority,
} from 'prime-front-service-client'

export interface MitigationModalProps {
  docId: number
  recommendations:
    | DesignDocTopRecommendation[]
    | DesignDocTopPolicyRecommendation[]
}

export interface MitigationItem {
  id: number
  title: string
  description: string
  assignee: string | null
  due_date: Date | null
  priority: MitigationPriority | null
  selected: boolean
}

export interface SelectedCase {
  id: number
  title: string
  issue_id: string
}

export interface ExportOptions {
  exportToPdf: boolean
  exportToCsv: boolean
  commentInJira: boolean
}

export interface CustomRecommendationForm {
  title: string
  description: string
  assignee: string
  dueDate: Date | null
  priority: MitigationPriority | null
}

// Constants
export const AUTOCOMPLETE_LIMIT = 20

export const PRIORITY_OPTIONS: Array<{
  value: MitigationPriority
  label: string
}> = [
  { value: 'critical' as MitigationPriority, label: 'Critical' },
  { value: 'high' as MitigationPriority, label: 'High' },
  { value: 'medium' as MitigationPriority, label: 'Medium' },
  { value: 'low' as MitigationPriority, label: 'Low' },
]
