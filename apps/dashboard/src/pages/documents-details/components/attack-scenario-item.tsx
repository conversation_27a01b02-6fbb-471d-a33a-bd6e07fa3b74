import { useState } from 'react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@libs/ui'
import { t } from 'i18next'
import Markdown from 'react-markdown'
import { RecommendationItem } from './recommendation-item'
import type { AttackScenario } from 'prime-front-service-client'

interface AttackScenarioItemProps {
  isOpen: boolean
  attackScenario: AttackScenario
}

export const AttackScenarioItem = ({
  attackScenario,
  isOpen,
}: AttackScenarioItemProps) => {
  const [openItems, setOpenItems] = useState<string[]>(
    isOpen ? ['attack-vector'] : []
  )

  return (
    <div>
      <Accordion
        type="multiple"
        value={openItems}
        onValueChange={setOpenItems}
        className="w-full mt-4"
      >
        <AccordionItem value="attack-vector">
          <AccordionTrigger className="capitalize">
            <h1 className="text-xl font-semibold">{attackScenario?.name}</h1>
          </AccordionTrigger>
          <AccordionContent>
            <Markdown
              components={{
                ul: ({ children }) => (
                  <ul className="list-disc px-10 space-y-4 mt-2">{children}</ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal px-10 space-y-4 mt-2">
                    {children}
                  </ol>
                ),
                h1: ({ children }) => (
                  <h1 className="text-xl font-medium my-2">{children}</h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-lg font-semibold my-2">{children}</h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-lg font-medium my-2">{children}</h3>
                ),
              }}
            >
              {attackScenario.markdown}
            </Markdown>
            <AccordionItem
              value="recommendations"
              className="my-4 border-b-0 ml-6"
            >
              <AccordionTrigger className="capitalize">
                <h1 className="text-lg font-semibold">
                  {t('recommendations')}
                </h1>
              </AccordionTrigger>
              <AccordionContent>
                <div className="p-6">
                  {attackScenario.recommendations?.map(
                    (recommendation, index) => (
                      <div key={index} className="mb-2">
                        <RecommendationItem
                          recommendation={recommendation}
                          recommendationNumber={index + 1}
                        />
                      </div>
                    )
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  )
}
