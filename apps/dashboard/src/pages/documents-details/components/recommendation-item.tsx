import type { DesignDocTopRecommendation } from 'prime-front-service-client'
import { t } from 'i18next'
import { mapHighlights } from '../util'
import { useCallback, useMemo } from 'react'
import {
  Button,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@libs/ui'
import { ChevronDown } from 'lucide-react'
import { useState } from 'react'
import type { DesignDocTopPolicyRecommendation } from 'prime-front-service-client/src/models/DesignDocTopPolicyRecommendation'

interface RecommendationItemProps {
  recommendationNumber: number
  recommendation: DesignDocTopRecommendation | DesignDocTopPolicyRecommendation
  highlightedPages?: Map<string, number>
  onHighlightClick?: (highlightText: string) => void
  onScenarioClick?: (scenarioName: string) => void
}
export const RecommendationItem = ({
  recommendationNumber,
  recommendation,
  highlightedPages,
  onHighlightClick,
  onScenarioClick,
}: RecommendationItemProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const handleHighlightClick = (highlightText: string) => {
    const cleanedHighlight = mapHighlights([highlightText])
    if (onHighlightClick) onHighlightClick(cleanedHighlight[0])
  }

  const highlightPage = useCallback(
    (quote: string) => {
      const cleanedHighlight = mapHighlights([quote])
      return highlightedPages?.get(cleanedHighlight[0]) || 0
    },
    [highlightedPages, recommendation]
  )

  const showPolicy = useMemo(
    () =>
      'policy_quotes' in recommendation &&
      !!recommendation.policy_quotes?.length,
    [recommendation]
  )

  return (
    <Collapsible
      className="border-b pb-2 mb-2"
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <CollapsibleTrigger>
        <div className="flex items-center justify-between">
          <div className="flex items-start gap-2">
            <div className="border border-teal-700 text-teal-700 rounded-full h-6 w-6 aspect-square flex items-center justify-center">
              {recommendationNumber}
            </div>
            <div className="flex flex-col items-start">
              <div className="font-bold text-sm text-left">
                {recommendation.title}
              </div>
              <div className="text-sm text-gray-500 text-left">
                {recommendation.description}
              </div>
            </div>
          </div>
          <div
            className="ml-2"
            style={{ transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)' }}
          >
            <ChevronDown size={20} />
          </div>
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent>
        <div className="flex flex-col items-start gap-2 px-8">
          {!!recommendation.evidence && (
            <div className="mt-4 flex flex-col gap-2">
              <div className="font-bold text-sm capitalize">
                {t('explanation')}
              </div>
              <div className="text-sm text-gray-500">
                {recommendation.evidence}
              </div>
            </div>
          )}
          {'attack_scenarios_names' in recommendation &&
            !!recommendation?.attack_scenarios_names?.length && (
              <div className="my-4 flex flex-col gap-2">
                <div className="font-bold text-sm capitalize">
                  {t('attackVectors')}
                </div>
                <div className="flex items-center gap-2">
                  {recommendation?.attack_scenarios_names?.map((scenario) => (
                    <Button
                      key={crypto.randomUUID()}
                      variant="outline"
                      dataTestId={scenario}
                      className="py-0 font-semibold text-sm h-6"
                      onClick={() =>
                        onScenarioClick && onScenarioClick(scenario)
                      }
                    >
                      {scenario}
                    </Button>
                  ))}
                </div>
              </div>
            )}

          {'quotes' in recommendation && !!recommendation?.quotes?.length && (
            <div className="my-4 flex flex-col gap-2">
              <div className="font-bold text-sm capitalize">{t('quotes')}</div>
              {recommendation.quotes.map((quote, index) => (
                <div className="text-sm text-gray-500" key={index}>
                  {index + 1}. "{quote}"
                  {!!highlightedPages?.size && (
                    <span>
                      {!!highlightPage(quote) && (
                        <span>
                          ,
                          <Button
                            dataTestId="ref-page"
                            variant="ghost"
                            className="cursor-pointer underline -ml-1"
                            onClick={() => handleHighlightClick(quote)}
                          >
                            {t('page')} {highlightPage(quote)}
                          </Button>
                        </span>
                      )}
                    </span>
                  )}
                </div>
              ))}
            </div>
          )}
          {showPolicy && 'policy_quotes' in recommendation && (
            <div className="mt-4 flex flex-col gap-2">
              <div className="font-bold text-sm capitalize">
                {t('policyReference')}
              </div>
              {recommendation.policy_quotes.map((policyQuote, index) => (
                <div className="text-sm text-gray-500" key={index}>
                  • {t('asPerPolicy')} "{policyQuote.quote_source}",{' '}
                  {t('relevantReference')}: "{policyQuote.quote_text}"
                </div>
              ))}
            </div>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
