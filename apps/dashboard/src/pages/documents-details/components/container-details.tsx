import { useGetSummaryForIssue } from '../../../api/use-summaries-api'
import { useGetCasesForAccount } from '../../../api/use-cases-api'
import { useMemo } from 'react'
import { ClipLoader, NestedAccordion } from '@libs/ui'
import { buildTree } from '../util'

interface ContainerDetailsProps {
  sourceId: number
  containerId: string
}

export const ContainerDetails = ({
  containerId,
  sourceId,
}: ContainerDetailsProps) => {
  const { data: issueSummaryData, isPending } = useGetSummaryForIssue(
    {
      issue_id: containerId,
      source_id: sourceId,
    },
    true
  )

  const { data: cases, isPending: isPendingCases } = useGetCasesForAccount({
    limit: 500,
    offset: 0,
    filters: [
      JSON.stringify({ field: 'parent_id', op: 'eq', value: [containerId] }),
    ],
  })

  const containerTree = useMemo(() => {
    if (cases?.results?.length && issueSummaryData) {
      return buildTree(containerId, issueSummaryData, cases?.results)
    }
  }, [containerId, issueSummaryData, cases?.results])

  if (isPendingCases || isPending) {
    return (
      <div
        data-testid="loading-indicator"
        className="flex justify-center items-center min-h-96"
      >
        <ClipLoader />
      </div>
    )
  }

  return (
    containerTree && (
      <div className="flex overflow-y-auto center items-center justify-center">
        <NestedAccordion data={[containerTree]} />
      </div>
    )
  )
}
