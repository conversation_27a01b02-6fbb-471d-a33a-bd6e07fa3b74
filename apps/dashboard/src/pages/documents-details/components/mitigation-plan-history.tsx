import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Skeleton,
} from '@libs/ui'
import type { MitigationResponse } from 'prime-front-service-client'
import { useGetMitigations } from '../../../api/use-mitigations-api'
import { cn } from '@libs/common'

const getPriorityStyles = (priority: string | undefined) => {
  switch (priority?.toLowerCase()) {
    case 'critical':
      return 'bg-red-200 text-red-950'
    case 'high':
      return 'bg-red-100 text-red-800'
    case 'medium':
      return 'bg-orange-100 text-orange-800'
    case 'low':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

interface MitigationPlanHistoryProps {
  docId: number
}

export const MitigationPlanHistory = ({
  docId,
}: MitigationPlanHistoryProps) => {
  const { data, isPending } = useGetMitigations(docId)

  if (isPending) {
    return (
      <div className="space-y-4">
        <Table>
          <TableHeader>
            <TableRow className="border-b-slate-500">
              <TableHead className="w-48">Title</TableHead>
              <TableHead className="font-bold">Description</TableHead>
              <TableHead className="font-bold">Assignee</TableHead>
              <TableHead className="font-bold">Due Date</TableHead>
              <TableHead className="font-bold">Priority</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell className="font-medium">
                  <Skeleton className="h-4 w-32" />
                </TableCell>
                <TableCell className="text-sm text-muted-foreground space-y-2">
                  <Skeleton className="h-4 w-3/4 rounded-md" />
                  <Skeleton className="h-4 w-full rounded-md" />
                  <Skeleton className="h-4 w-2/4 rounded-md" />
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  <Skeleton className="h-4 w-24 rounded-md" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    )
  }

  if (!data || data.length === 0) {
    return (
      <div className="text-center p-8 text-muted-foreground">
        No mitigation history found
      </div>
    )
  }

  return (
    <div className="space-y-4 relative w-full">
      <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
        <Table>
          <TableHeader>
            <TableRow className="border-b-slate-500">
              <TableHead className="font-bold w-48 ">Title</TableHead>
              <TableHead className="font-bold">Description</TableHead>
              <TableHead className="font-bold">Assignee</TableHead>
              <TableHead className="font-bold">Due Date</TableHead>
              <TableHead className="font-bold">Priority</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((mitigation: MitigationResponse) => (
              <TableRow key={mitigation.id}>
                <TableCell className="font-medium">
                  {mitigation.title}
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {mitigation.description}
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {mitigation.assignee}
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {mitigation.due_date &&
                    new Date(mitigation.due_date).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  {mitigation.priority && (
                    <span
                      className={cn(
                        'px-3 py-1 rounded-full text-xs font-semibold capitalize',
                        getPriorityStyles(mitigation.priority)
                      )}
                    >
                      {mitigation.priority}
                    </span>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
