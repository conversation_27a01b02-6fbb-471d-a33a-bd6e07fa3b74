import { Skeleton } from '@libs/ui'

export const ContainerInnerSkeleton = () => {
  return (
    <div className="mx-auto w-full flex-1 p-6">
      <Skeleton className="h-8 w-3/4 mb-10" />

      <div className="flex justify-between gap-4 text-slate-700 mb-10">
        <div className="flex">
          <div className="w-24 h-20 inline-flex items-center justify-center rounded-lg bg-slate-100 px-4 py-2">
            <div>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>
        </div>
        <div className="flex gap-4 items-center">
          {Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="w-24 h-20 rounded-xl bg-slate-50 p-4 border"
            >
              <Skeleton className="h-8 w-12 mb-2" />
              <Skeleton className="h-4 w-16" />
            </div>
          ))}
        </div>
      </div>

      <div className="mb-6">
        <div className="grid w-full grid-cols-[1fr_1fr_1fr] gap-2 mb-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <Skeleton key={index} className="h-10" />
          ))}
        </div>

        <div className="mt-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <Skeleton key={index} className="h-4 w-full mb-4" />
          ))}
        </div>
      </div>
    </div>
  )
}
