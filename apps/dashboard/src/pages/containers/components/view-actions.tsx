import { Button, Input } from '@libs/ui'
import { Edit2, Loader2, Trash2 } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { t } from 'i18next'
import { useAtom } from 'jotai'
import { editModePresetAtom } from './views-selector'
import type { QueryView } from 'prime-front-service-client'
import {
  useAddOrUpdateQueryCasesView,
  useDeleteCasesView,
} from '../../../api/use-config-api'
import { toast } from 'sonner'
import { usePresets } from '../hooks/use-presets'

interface ViewActionsProps {
  preset: QueryView
}

export const ViewActions = ({ preset }: ViewActionsProps) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editMode, setEditMode] = useAtom(editModePresetAtom)
  const inputRef = useRef<HTMLInputElement | null>(null)

  const { refetchPresets } = usePresets()
  const deleteFilterPresetsMutation = useDeleteCasesView()
  const updateFilterPresetsMutation = useAddOrUpdateQueryCasesView()

  useEffect(() => {
    if (editMode !== preset.query_id) {
      setIsEditing(false)
    }
  }, [editMode, preset.query_id])

  const handleEdit = async (query_id: string, name: string) => {
    try {
      await updateFilterPresetsMutation.mutateAsync({
        queryView: {
          ...preset,
          name,
        },
        view_type: 'containers',
      })

      await refetchPresets()
      setIsEditing(false)
      setEditMode(null)
      toast.success(t('viewUpdatedSuccessfully'))
    } catch {
      toast.error(t('errors.failedToUpdateView'))
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditMode(null)
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      await deleteFilterPresetsMutation.mutateAsync({
        query_id: preset.query_id,
        view_type: 'containers',
      })

      await refetchPresets()
      toast.success(t('viewDeletedSuccessfully'))
    } catch {
      toast.error(t('errors.failedToDeleteView'))
    }
  }

  if (isEditing) {
    return (
      <div className="flex gap-2 px-2 py-1">
        <Input
          ref={inputRef}
          defaultValue={preset.name}
          className="h-8"
          autoFocus
        />
        <div className="flex gap-2">
          <Button
            onClick={() =>
              handleEdit(preset.query_id, inputRef?.current?.value || '')
            }
            size="sm"
            dataTestId="save-preset-button"
          >
            {updateFilterPresetsMutation.isPending && (
              <Loader2
                data-testid="edit-pending"
                className="h-4 w-4 animate-spin"
              />
            )}
            {t('save')}
          </Button>
          <Button
            onClick={handleCancel}
            variant="secondary"
            size="sm"
            dataTestId="cancel-preset-button"
          >
            {t('cancel')}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        onClick={(e) => {
          e.stopPropagation()
          setIsEditing(true)
          setEditMode(preset.query_id)
        }}
        className="h-6 w-6"
        dataTestId="edit-button"
      >
        <Edit2 size={14} />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={handleDelete}
        className="h-6 w-6"
        disabled={deleteFilterPresetsMutation.isPending}
        dataTestId="delete-button"
      >
        {deleteFilterPresetsMutation.isPending ? (
          <Loader2 size={14} className="animate-spin" />
        ) : (
          <Trash2 size={14} />
        )}
      </Button>
    </>
  )
}
