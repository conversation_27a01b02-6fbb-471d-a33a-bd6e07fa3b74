/*eslint-disable max-lines*/
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Button,
  riskScoreCategoryColors,
  Sheet,
  SheetClose,
  SheetContent,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  Summary5W,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  useDisclosure,
} from '@libs/ui'
import { X } from 'lucide-react'
import { useAtomValue } from 'jotai'
import { useCallback, useEffect } from 'react'
import { useGetSummaryForIssue } from '../../../api/use-summaries-api'
import { defaultViewAtom } from '../../../config/store'
import {
  defaultFilterParams,
  defaultSortParam,
  getParsedArrayOfObjects,
} from '../../../router/router'
import { staticColumns } from '../../workroom/components'
import { t } from 'i18next'
import { NavLink, useNavigate } from 'react-router'
import {
  containerOpenedItemSchema,
  serializeWorkroom,
} from '../../../router/router-utils'
import { parseAsJson, useQueryState } from 'nuqs'
import { ContainerInnerSkeleton } from './container-inner-skeleton'
import { cn } from '@libs/common'
import type { ResponseError } from 'prime-front-service-client'
import { RiskScoreCategory } from 'prime-front-service-client'
import { riskScoreClasses } from './container-columns'
import { ChatContent } from '../../../components/chatbot/chat-content'
import { useFlagsWrapper } from '../../../hooks/use-flags-wrapper'
import {
  useStartSecurityReviewContainer,
  errorsMap,
  useGetSecurityReviewByCaseId,
} from '../../../api/use-design-docs-api'
import { toast } from 'sonner'
import { duplicateCode } from '../../../api/error-map'

const ContainerFallback = () => {
  return (
    <div className="text-sm">
      <div className="font-semibold text-sm mb-1">{t('noSummaryTitle')}</div>
      <div>{t('noContainerDescription')}</div>
    </div>
  )
}

const ConcernsFallback = () => {
  return (
    <div className="text-sm">
      <div className="font-semibold text-sm mb-1">{t('noConcernsTitle')}</div>
      <div>{t('noConcernsDescription')}</div>
    </div>
  )
}

export const ContainersDrawer = () => {
  const navigate = useNavigate()

  const {
    epicsDrawerChatbot: containersDrawerChatbot,
    generateContainerReviewDocument,
  } = useFlagsWrapper()

  const [openedContainer, setOpenedContainer] = useQueryState(
    'opened_item',
    parseAsJson(containerOpenedItemSchema.parse)
  )

  const { isOpen, onOpen, onClose } = useDisclosure(false)
  const defaultView = useAtomValue(defaultViewAtom)

  const {
    data: issueSummaryData,
    refetch,
    isPending,
  } = useGetSummaryForIssue({
    issue_id: openedContainer?.issue_id || '',
    source_id: openedContainer?.source_id || 0,
  })

  const {
    data: securityReviewData,
    isPending: isSecurityReviewPending,
    error: securityReviewError,
    refetch: refetchSecurityReview,
  } = useGetSecurityReviewByCaseId(issueSummaryData?.id ?? 0)

  const startSecurityReviewContainerMutation = useStartSecurityReviewContainer()

  const handleClose = useCallback(() => {
    setOpenedContainer(null)
    onClose()
  }, [onClose, setOpenedContainer])

  useEffect(() => {
    if (openedContainer?.issue_id && openedContainer.source_id) {
      onOpen()
      refetch()
    } else {
      handleClose()
    }
  }, [
    handleClose,
    onOpen,
    openedContainer?.issue_id,
    openedContainer?.source_id,
    refetch,
  ])

  const workroomState = {
    f: [
      ...defaultFilterParams,
      {
        field: 'parent_id',
        op: 'eq',
        value: [openedContainer?.issue_id],
      },
    ],
    s: defaultView?.s
      ? getParsedArrayOfObjects(defaultView.s)
      : [defaultSortParam],
    selected_columns: defaultView?.selected_columns || staticColumns,
  }

  const generateReviewDocuments = async () => {
    startSecurityReviewContainerMutation.mutate(
      {
        source_id: openedContainer?.source_id || 0,
        container_issue_id: openedContainer?.issue_id || '',
      },
      {
        onSuccess: async () => {
          toast.success(t('reviewStartedSuccessfully'))
          refetchSecurityReview()
        },
        onError: async (error: Error) => {
          const responseError = error as ResponseError
          const message = await responseError?.response?.json()
          const errorStatus = await responseError?.response?.status

          if (errorStatus === duplicateCode) {
            toast.error(t('errors.designReviewAlreadyExist'))
          }
          if (message?.exception === errorsMap.generateAlreadyRunning) {
            toast.info(t('errors.generatingReviewAlreadyRunning'))
          } else {
            toast.error(t('errors.reviewStartFailed'))
          }
        },
      }
    )
  }

  if (!openedContainer) return null

  return (
    <Sheet
      open={isOpen}
      onOpenChange={(open) => (open ? onOpen() : handleClose())}
    >
      <SheetContent
        className="flex flex-col p-0 max-w-[500px] sm:max-w-[840px] overflow-auto"
        removeOverlay
      >
        <div className="mx-auto w-full flex-1">
          <SheetHeader className="p-6 sticky top-0 bg-white">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-sm font-semibold">
                {openedContainer.issue_id}
              </SheetTitle>
              <SheetClose
                onClick={() => handleClose()}
                className="h-6 w-6 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none  "
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </SheetClose>
            </div>
          </SheetHeader>

          {isPending ? (
            <ContainerInnerSkeleton />
          ) : (
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-10">
                {issueSummaryData?.title}
              </h2>
              <div className="">
                <div className="flex justify-between gap-4 text-slate-700 mb-10 ">
                  <div
                    className={cn(
                      'inline-flex items-center justify-center px-4 py-2 border rounded-3xl bg-slate-100 min-w-24',
                      riskScoreClasses(issueSummaryData?.risk_score ?? 0)
                    )}
                  >
                    <div>
                      <div className={cn('text-2xl font-semibold')}>
                        {issueSummaryData?.risk_score}
                      </div>
                      <div className="text-xs text-slate-600">Risk Score</div>
                    </div>
                  </div>

                  <div className="flex gap-8">
                    <NavLink
                      to={{
                        pathname: '/workroom',
                        search: serializeWorkroom({
                          ...workroomState,
                          f: [
                            ...workroomState.f,
                            {
                              field: 'risk_score_category',
                              op: 'eq',
                              value: ['intervene'],
                            },
                          ],
                        }),
                      }}
                      data-testid="workroom-link-intervene"
                    >
                      <div
                        className={cn(
                          riskScoreCategoryColors[RiskScoreCategory.intervene],
                          'min-w-24 rounded-3xl p-4'
                        )}
                      >
                        <div className="text-2xl font-semibold">
                          {issueSummaryData?.risk.intervene}
                        </div>
                        <div className="text-xs">{t('intervene')}</div>
                      </div>
                    </NavLink>

                    <NavLink
                      to={{
                        pathname: '/workroom',
                        search: serializeWorkroom({
                          ...workroomState,
                          f: [
                            ...workroomState.f,
                            {
                              field: 'risk_score_category',
                              op: 'eq',
                              value: ['analyze'],
                            },
                          ],
                        }),
                      }}
                      data-testid="workroom-link-analyze"
                    >
                      <div
                        className={cn(
                          riskScoreCategoryColors[RiskScoreCategory.analyze],
                          'min-w-24 rounded-3xl p-4'
                        )}
                      >
                        <div className="text-2xl font-semibold">
                          {issueSummaryData?.risk.analyze}
                        </div>
                        <div className="text-xs">{t('analyze')}</div>
                      </div>
                    </NavLink>

                    <NavLink
                      to={{
                        pathname: '/workroom',
                        search: serializeWorkroom({
                          ...workroomState,
                          f: [
                            ...workroomState.f,
                            {
                              field: 'risk_score_category',
                              op: 'eq',
                              value: ['monitor'],
                            },
                          ],
                        }),
                      }}
                      data-testid="workroom-link-monitor"
                    >
                      <div
                        className={cn(
                          riskScoreCategoryColors[RiskScoreCategory.monitor],
                          'min-w-24 rounded-3xl p-4'
                        )}
                      >
                        <div className="text-2xl font-semibold">
                          {issueSummaryData?.risk.monitor}
                        </div>
                        <div className="text-xs">{t('monitor')}</div>
                      </div>
                    </NavLink>
                  </div>
                </div>
                <Tabs defaultValue="expected" className="mb-6">
                  <TabsList
                    className={cn(
                      'grid w-full',
                      containersDrawerChatbot
                        ? 'grid-cols-[1fr_1fr_1fr_1fr]'
                        : 'grid-cols-[1fr_1fr_1fr]'
                    )}
                  >
                    <TabsTrigger value="expected">
                      {t('expectedOutcome')}
                    </TabsTrigger>
                    <TabsTrigger value="context">{t('context')}</TabsTrigger>
                    <TabsTrigger value="concerns">{t('concerns')}</TabsTrigger>
                    {containersDrawerChatbot && (
                      <TabsTrigger value="ai">{t('askPrime')}</TabsTrigger>
                    )}
                  </TabsList>
                  <TabsContent value="expected" className="mt-6">
                    {issueSummaryData?.issue_summary_short ? (
                      <div className="text-sm text-slate-600 mb-4">
                        {issueSummaryData?.issue_summary_short}
                      </div>
                    ) : (
                      <ContainerFallback />
                    )}
                  </TabsContent>
                  <TabsContent value="context" className="mt-4 text-sm">
                    {issueSummaryData?.issue_summary_5w ? (
                      <Summary5W summary={issueSummaryData?.issue_summary_5w} />
                    ) : (
                      <ContainerFallback />
                    )}
                  </TabsContent>
                  <TabsContent value="concerns" className="mt-4">
                    {issueSummaryData?.concerns?.length ? (
                      <ul className="text-slate-600 text-sm my-6 ml-6 list-disc [&>li]:mt-2">
                        {issueSummaryData?.concerns.map((concern, index) => (
                          <li key={index}>{concern.long_description}</li>
                        ))}
                      </ul>
                    ) : (
                      <ConcernsFallback />
                    )}
                  </TabsContent>
                  {containersDrawerChatbot && (
                    <TabsContent value="ai" className="mt-4">
                      <ChatContent
                        key={`chat-${openedContainer?.source_id}-${openedContainer?.issue_id}`}
                        container_id={issueSummaryData?.id ?? undefined}
                        chat_location="container"
                      />
                    </TabsContent>
                  )}
                </Tabs>
              </div>
            </div>
          )}
        </div>
        <SheetFooter
          className={cn(
            'flex flex-row items-center p-5 bg-white sticky bottom-0 shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1),0_-2px_4px_-1px_rgba(0,0,0,0.06)]',
            generateContainerReviewDocument ? 'justify-between' : 'justify-end'
          )}
        >
          {generateContainerReviewDocument && (
            <div>
              {(securityReviewError as ResponseError)?.response?.status ===
              404 ? (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      dataTestId="genReviewDocument"
                      className="capitalize"
                      variant="outline"
                      disabled={isSecurityReviewPending}
                    >
                      {t('genSecurityReview')}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>
                        {t('areYouAbsolutelySure')}?
                      </AlertDialogTitle>
                      <AlertDialogDescription>
                        {t('thisActionMightTakeAWhile')}.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                      <AlertDialogAction asChild>
                        <Button
                          dataTestId="generate-review-documents"
                          onClick={generateReviewDocuments}
                        >
                          {t('confirm')}
                        </Button>
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              ) : (
                <Button
                  dataTestId="goToSecurityReview"
                  className="capitalize"
                  variant="outline"
                  disabled={isSecurityReviewPending}
                  onClick={() => {
                    navigate({
                      pathname: `/design-reviews/${securityReviewData?.id}`,
                    })
                  }}
                >
                  {t('goToSecurityReview')}
                </Button>
              )}
            </div>
          )}

          <NavLink
            to={{
              pathname: '/workroom',
              search: serializeWorkroom(workroomState),
            }}
            data-testid="workroom-link"
          >
            <Button className="w-full" dataTestId="view-tickets-button">
              {t('goToTicketsView')}
            </Button>
          </NavLink>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
