import type { CellContext, ColumnDef } from '@tanstack/react-table'
import { createColumnHelper } from '@tanstack/react-table'
import type { ExternalCaseWorkroom } from 'prime-front-service-client'
import { cn } from '@libs/common'
import {
  JiraIcn,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  SortRenderer,
  Progress,
} from '@libs/ui'
import { t } from 'i18next'
import { parseAsJson, useQueryState } from 'nuqs'
import { containerOpenedItemSchema } from '../../../router/router-utils'

export const columnHelper = createColumnHelper<ExternalCaseWorkroom>()

export const containersStaticColumns = [
  'issue_id',
  'title',
  'risk_score',
  'progress_percentage',
]

export const riskScoreLevel = {
  intervene: 70,
  analyze: 40,
  monitor: 0,
}

export const riskScoreClasses = (riskScore: number) => {
  if (riskScore > riskScoreLevel.intervene) {
    return 'border-red-300 bg-red-50'
  } else if (riskScore > riskScoreLevel.analyze) {
    return 'border-orange-300 bg-orange-50'
  }
  return 'border-yellow-400 bg-yellow-50'
}

const ColumnHeader = ({
  title,
  content,
  withJiraIcon,
}: {
  title: string
  content: string
  withJiraIcon?: boolean
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className="flex items-center gap-2">
          {withJiraIcon && <JiraIcn className="w-4 h-4 text-jira" />}
          {title}
        </div>
      </TooltipTrigger>
      <TooltipContent>{content}</TooltipContent>
    </Tooltip>
  )
}

const CellWWithDrawer = ({
  info,
  value,
}: {
  info: CellContext<ExternalCaseWorkroom, string>
  value: string
}) => {
  const [_, setOpenedContainer] = useQueryState(
    'opened_item',
    parseAsJson(containerOpenedItemSchema.parse)
  )
  return (
    <div
      className="hover:underline max-w-64 w-max cursor-pointer pl-2"
      onClick={() => {
        setOpenedContainer({
          issue_id: info?.row?.original?.issue_id,
          source_id: info?.row?.original?.source_id,
        })
      }}
    >
      {value}
    </div>
  )
}

export const containerColumns = [
  // issue_id
  columnHelper.accessor('issue_id', {
    id: 'issue_id',
    header: ({ column }) => (
      <SortRenderer
        column={column}
        title={t('tableColumns.issue_id')}
        staticColumns={[]}
        withJiraIcon
      />
    ),
    size: 80,
    cell: (info) => {
      const value = info.getValue()
      return <CellWWithDrawer info={info} value={value} />
    },
    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  // title
  columnHelper.accessor('title', {
    header: () => (
      <ColumnHeader
        title={t('tableColumns.title')}
        content={t('tableColumns.containerTitle')}
        withJiraIcon
      />
    ),
    id: 'title',
    cell: (info) => {
      return <CellWWithDrawer info={info} value={info.getValue()} />
    },
    footer: (info) => info.column.id,
    size: 300,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  // risk_score
  columnHelper.accessor('issue_analysis.risk_score', {
    header: ({ column }) => (
      <SortRenderer
        column={column}
        title={t('tableColumns.riskScore')}
        staticColumns={[]}
      />
    ),
    id: 'risk_score',
    size: 100,
    cell: (info) => {
      const value = info.getValue() ?? 0
      return (
        <div
          className={cn(
            'border-2 font-bold rounded-full h-8 w-8 flex items-center justify-center',
            riskScoreClasses(value)
          )}
        >
          {value}
        </div>
      )
    },
    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  //progress_percentage
  columnHelper.accessor('progress_percentage', {
    header: ({ column }) => (
      <SortRenderer
        column={column}
        title={t('tableColumns.progress_percentage')}
        staticColumns={[]}
        withJiraIcon
      />
    ),
    id: 'progress_percentage',
    size: 100,
    cell: (info) => {
      const value = info.getValue() ?? 0
      return (
        <div className=" flex items-center gap-2">
          <Progress
            value={value}
            progressColor="bg-slate-600"
            className="h-2 w-28"
          />{' '}
          <span className="text-muted-foreground text-xs">{value}%</span>
        </div>
      )
    },
    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,
]
