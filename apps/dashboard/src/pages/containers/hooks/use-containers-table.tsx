import { useQuery } from '@tanstack/react-query'
import { casesAPI } from '../../../api/clients'
import { t } from 'i18next'
import { toast } from 'sonner'
import { useGetWorkroomFields } from '../../../api/use-config-api'
import {
  transformedSorting,
  useFilters,
  useSorting,
  useContainersColumns,
} from '../hooks'
import { refetchCasesAtom } from '../../../api/use-cases-api'
import { atom, useAtom, useAtomValue } from 'jotai'
import { useEffect, useRef } from 'react'
import {
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import {
  useSensors,
  useSensor,
  MouseSensor,
  TouchSensor,
  KeyboardSensor,
} from '@dnd-kit/core'
import type { PaginationResponseExternalCaseWorkroom } from 'prime-front-service-client'

const LIMIT = 20
export const currentPageAtom = atom(0)

export const useContainersTable = () => {
  const tableRef = useRef<HTMLDivElement>(null)

  const { stringifiedFilters, clearFilters } = useFilters()
  const { sorting, setSorting, sortPayload } = useSorting()
  const refetchCasesValue = useAtomValue(refetchCasesAtom)
  const [currentPage, setCurrentPage] = useAtom(currentPageAtom)

  const { data: workroomFields } = useGetWorkroomFields(true)

  useEffect(() => {
    setCurrentPage(0)
  }, [stringifiedFilters])

  const { data, isPending, isError } =
    useQuery<PaginationResponseExternalCaseWorkroom>({
      queryKey: [
        'getContainerCasesForAccount',
        sortPayload,
        stringifiedFilters,
        refetchCasesValue,
        currentPage,
      ] as const,
      queryFn: async () => {
        const transformedSortingPayload = transformedSorting(sortPayload)

        const containerFilters = [
          JSON.stringify({
            field: 'classification',
            op: 'eq',
            value: ['True', 'False'],
          }),
          JSON.stringify({ field: 'container', op: 'eq', value: ['True'] }),
        ]

        const filterParam = stringifiedFilters.length
          ? [...stringifiedFilters, ...containerFilters]
          : [...containerFilters]

        try {
          return await casesAPI.getCasesForAccount({
            limit: LIMIT,
            offset: currentPage * LIMIT,
            s: transformedSortingPayload.length
              ? transformedSortingPayload
              : undefined,
            f: filterParam.length ? filterParam : undefined,
          })
        } catch (error) {
          toast.error(t('errors.failedToFetchCases'))
          throw new Error(t('errors.failedToFetchCases'))
        }
      },
    })

  const {
    columns,
    columnVisibility,
    setColumnVisibility,
    columnOrder,
    setColumnOrder,
    handleDragEnd,
  } = useContainersColumns(workroomFields || [])

  const flatData = data?.results ?? []

  const table = useReactTable({
    data: flatData,
    columns: columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    manualSorting: true,
    state: {
      sorting,
      columnVisibility,
      columnOrder,
    },
    onColumnOrderChange: setColumnOrder,
  })

  const sensors = useSensors(
    useSensor(MouseSensor, {}),
    useSensor(TouchSensor, {}),
    useSensor(KeyboardSensor, {})
  )
  const handlePageChange = (newPage: number) => {
    if (newPage >= 0 && newPage < Math.ceil((data?.total ?? 0) / LIMIT)) {
      setCurrentPage(newPage)
    }
  }

  return {
    table,
    tableRef,
    workroomFields,
    isPending,
    isError,
    clearFilters,
    handleDragEnd,
    sensors,
    columnOrder,
    currentPage,
    totalPages: Math.ceil((data?.total ?? 0) / LIMIT),
    totalItems: data?.total ?? 0,
    handlePageChange,
    LIMIT,
  }
}
