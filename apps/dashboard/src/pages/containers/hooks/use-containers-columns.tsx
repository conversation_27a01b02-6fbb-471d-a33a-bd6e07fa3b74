import { useState, useCallback, useEffect } from 'react'
import type { ColumnDef, VisibilityState } from '@tanstack/react-table'
import type {
  ExternalCaseWorkroom,
  ProviderFieldInfoOptions,
} from 'prime-front-service-client'
import { useUrlState, SortRenderer } from '@libs/ui'
import type { UrlStateProps } from './use-selected-columns'
import { useUrlStateDefaultOptions } from './use-selected-columns'
import type { DragEndEvent } from '@dnd-kit/core'
import { arrayMove } from '@dnd-kit/sortable'
import { atom, useAtom } from 'jotai'
import { containerColumns, columnHelper } from '../components/container-columns'

export const defaultVisibleColumns = {
  select: true,
  issue_id: true,
  title: true,
  'issue_analysis.risk_score': true,
  progress_percentage: true,
}

function arrayToObject<T>(arr: string[], value: T): Record<string, T> {
  return arr.reduce((acc, curr) => {
    acc[curr] = value
    return acc
  }, {} as Record<string, T>)
}

export const columnOrderAtom = atom<string[]>([])

export const useContainersColumns = (
  workroomFields: Array<ProviderFieldInfoOptions>
) => {
  const [urlState, setUrlState] = useUrlState<UrlStateProps>(
    {},
    useUrlStateDefaultOptions
  )
  const [columns, setColumns] = useState(containerColumns)
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    urlState.selected_columns?.length
      ? arrayToObject(urlState.selected_columns, true)
      : defaultVisibleColumns
  )
  const [columnOrder, setColumnOrder] = useAtom(columnOrderAtom)

  useEffect(() => {
    if (urlState.selected_columns?.length) {
      setColumnOrder(urlState.selected_columns)
    } else {
      setColumnOrder(Object.keys(defaultVisibleColumns))
    }
  }, [urlState.selected_columns, setColumnOrder])

  const setNewColumns = useCallback(() => {
    if (!workroomFields) return

    const newColumns: ColumnDef<ExternalCaseWorkroom, unknown>[] = []

    workroomFields.forEach(({ name, type, id }) => {
      if (columns.some((column) => column.id === id)) {
        return
      }

      newColumns.push(
        columnHelper.accessor(id as any, {
          id,
          header: ({ column }) =>
            SortRenderer({
              title: name,
              column,
              staticColumns: [],
            }),
          cell: (info) => {
            const accessor = info.column.id
            const accessorWithoutProviderFieldsPrefix = accessor.split('.')[1]
            const providerFields = info?.row?.original?.provider_fields as never
            const valueToRender =
              providerFields?.[accessorWithoutProviderFieldsPrefix]
            return (
              <span className="text-muted-foreground">
                {type === 'date' && valueToRender
                  ? new Date(valueToRender).toLocaleDateString()
                  : valueToRender}
              </span>
            )
          },
          size: 150,
          meta: {
            title: name,
            isDynamic: true,
            type: type,
          },
        })
      )
    })

    if (newColumns.length > 0) {
      const updatedColumns = [...columns, ...newColumns]

      const newColumnVisibility: VisibilityState = updatedColumns.reduce(
        (acc, column) => {
          if (column.id !== undefined) {
            acc[column.id] = !!urlState.selected_columns?.includes(column.id)
          }
          return acc
        },
        {} as Record<string, boolean>
      )

      setColumns(updatedColumns)

      setColumnVisibility(newColumnVisibility)

      setColumnOrder((prevOrder) => {
        const visibleNewColumns = newColumns
          .filter((col) => newColumnVisibility[col.id as string])
          .map((col) => col.id as string)
        const newOrder = [...prevOrder, ...visibleNewColumns]

        return newOrder
      })
    }
  }, [columns, workroomFields, urlState.selected_columns])

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    if (active && over && active.id !== over.id) {
      setColumnOrder((columnOrder) => {
        const oldIndex = columnOrder.indexOf(active.id as string)
        const newIndex = columnOrder.indexOf(over.id as string)

        const newSortedColumns = arrayMove(columnOrder, oldIndex, newIndex)
        setUrlState((prevState) => {
          return {
            ...prevState,
            selected_columns: newSortedColumns,
          }
        })

        return newSortedColumns
      })
    }
  }

  useEffect(() => {
    setNewColumns()
  }, [workroomFields, setNewColumns])

  useEffect(() => {
    if (!workroomFields.length) return

    if (urlState.selected_columns?.length) {
      setColumnVisibility((prev) => {
        const newVisibility = { ...prev }
        Object.keys(newVisibility).forEach((key) => {
          newVisibility[key] = urlState.selected_columns!.includes(key)
        })
        return newVisibility
      })
    } else {
      setColumnVisibility(defaultVisibleColumns)
    }
  }, [workroomFields, urlState.selected_columns])

  useEffect(() => {
    const newColumnVisibility: VisibilityState = columns.reduce(
      (acc: { [key: string]: boolean }, column) => {
        if (column.id === 'select') {
          acc['select'] = true
        } else if (column.id !== undefined) {
          acc[column.id] =
            urlState.selected_columns?.includes(column.id) ?? false
        }
        return acc
      },
      { select: true }
    )

    setColumnVisibility(newColumnVisibility)

    setColumnOrder((prevOrder) => {
      const updatedOrder = prevOrder.includes('select')
        ? prevOrder
        : ['select', ...prevOrder]
      return updatedOrder.filter((column) =>
        columns.some((c) => c.id === column)
      )
    })
  }, [columns, urlState.selected_columns, urlState.view])

  return {
    columns,
    setColumns,
    columnVisibility,
    setColumnVisibility,
    columnOrder,
    setColumnOrder,
    handleDragEnd,
  }
}
