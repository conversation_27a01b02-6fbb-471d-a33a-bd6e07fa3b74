import { useCallback } from 'react'
import {
  useAddOrUpdateQueryCasesView,
  useGetFilterPresets,
} from '../../../api/use-config-api'
import { toast } from 'sonner'
import { t } from 'i18next'
import {
  defaultContainersSortParam,
  getParsedArrayOfObjects,
  getStringifiedArrayOfObjects,
} from '../../../router/router'
import type { FilterItem } from '@libs/ui'
import { useUrlState } from '@libs/ui'
import type { UrlStateProps } from './use-selected-columns'
import { useUrlStateDefaultOptions } from './use-selected-columns'
import { containersStaticColumns } from '../components/container-columns'
import { defaultContainersViewAtom } from '../../../config/store'
import { useAtom } from 'jotai'

export const usePresets = () => {
  const [state, setState] = useUrlState<UrlStateProps>(
    {},
    useUrlStateDefaultOptions
  )
  const [defaultContainersView, setDefaultContainersView] = useAtom(
    defaultContainersViewAtom
  )

  const { data: presets, refetch: refetchPresets } =
    useGetFilterPresets('containers')
  const useAddFilterPresetsMutation = useAddOrUpdateQueryCasesView()

  const currentView = state.view
  const currentPreset = presets?.find(
    (preset) => preset.query_id === currentView
  )

  const switchView = useCallback(
    (query_id: string) => {
      if (query_id === 'default-view') {
        setState({
          view: undefined,
          f: defaultContainersView?.f
            ? getParsedArrayOfObjects(defaultContainersView.f)
            : [
                {
                  field: 'provider_fields.issuetype',
                  op: 'eq',
                  value: ['Epic'],
                },
                {
                  field: 'container',
                  op: 'eq',
                  value: ['True'],
                },
              ],
          s: defaultContainersView?.s
            ? getParsedArrayOfObjects(defaultContainersView.s)
            : [defaultContainersSortParam],
          selected_columns:
            defaultContainersView?.selected_columns || containersStaticColumns,
        })
      } else {
        const nextPreset = presets?.find((p) => p.query_id === query_id)
        const existingFilters = nextPreset?.query_list
          ? getParsedArrayOfObjects(nextPreset.query_list)
          : []

        const hasEpicFilter = existingFilters?.some(
          (filter) => filter.field === 'provider_fields.issuetype'
        )
        const hasContainerFilter = existingFilters?.some(
          (filter) => filter.field === 'container'
        )

        const filters = [
          ...(existingFilters || []),
          ...(!hasEpicFilter
            ? [
                {
                  field: 'provider_fields.issuetype',
                  op: 'eq',
                  value: ['Epic'],
                },
              ]
            : []),
          ...(!hasContainerFilter
            ? [
                {
                  field: 'container',
                  op: 'eq',
                  value: ['True'],
                },
              ]
            : []),
        ]

        setState({
          view: query_id,
          f: filters,
          s: getParsedArrayOfObjects(nextPreset?.sort_list ?? undefined) || [
            defaultContainersSortParam,
          ],
          selected_columns:
            nextPreset?.selected_columns || containersStaticColumns,
        })
      }
    },
    [presets, setState, defaultContainersView]
  )

  const addPreset = async (name: string) => {
    const query_id = crypto.randomUUID().split('-')[0]

    const defaultParams = {
      f: defaultContainersView?.f
        ? getParsedArrayOfObjects(defaultContainersView.f)
        : [
            {
              field: 'provider_fields.issuetype',
              op: 'eq',
              value: ['Epic'],
            },
          ],
      s: defaultContainersView?.s
        ? getParsedArrayOfObjects(defaultContainersView.s)
        : [defaultContainersSortParam],
      selected_columns:
        defaultContainersView?.selected_columns || containersStaticColumns,
    }

    try {
      await useAddFilterPresetsMutation.mutateAsync({
        queryView: {
          name,
          query_id,
          query_list: getStringifiedArrayOfObjects(defaultParams.f) || [],
          sort_list: getStringifiedArrayOfObjects(defaultParams.s) || [],
          selected_columns: defaultParams.selected_columns,
        },
        view_type: 'containers',
      })

      await refetchPresets()
      switchView(query_id)
      setState({
        view: query_id,
        ...defaultParams,
      })
      toast.success(t('presetCreatedSuccessfully'))
    } catch (error) {
      toast.error(t('errors.failedToCreatePreset'))
    }
  }

  const updateContainerPreset = useCallback(
    async (updatedFilters?: FilterItem[]) => {
      try {
        const presetData = {
          f: getStringifiedArrayOfObjects(updatedFilters || state.f || []),
          s: getStringifiedArrayOfObjects(state.s || []),
          selected_columns: state.selected_columns || containersStaticColumns,
        }

        if (!currentView) {
          setDefaultContainersView(presetData)
          toast.success(t('filtersSavedSuccessfully'))
        } else {
          await useAddFilterPresetsMutation.mutateAsync({
            queryView: {
              name: currentPreset?.name || '',
              query_id: currentView,
              query_list: presetData.f || [],
              sort_list: presetData.s,
              selected_columns: presetData.selected_columns,
            },
            view_type: 'containers',
          })
          await refetchPresets()
          toast.success(t('filtersSavedSuccessfully'))
        }
      } catch (error) {
        console.error('Failed to update preset:', error)
        toast.error(t('errors.failedToSaveFilters'))
      }
    },
    [
      currentView,
      currentPreset,
      state,
      setDefaultContainersView,
      useAddFilterPresetsMutation,
      refetchPresets,
    ]
  )

  return {
    presets: presets || [],
    currentView,
    currentPreset,
    refetchPresets,
    isUpdatePresetPending: useAddFilterPresetsMutation.isPending,
    updateContainerPreset,
    switchView,
    addPreset,
  }
}
