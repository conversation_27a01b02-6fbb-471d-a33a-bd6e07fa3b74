import { t } from 'i18next'
import type { FilterItem } from '@libs/ui'
import {
  PageTitle,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Skeleton,
  useUrlState,
} from '@libs/ui'
import {
  useUrlStateDefaultOptions,
  type UrlStateProps,
} from '../workroom/hooks/use-selected-columns'
import { ContainersTable } from './components/containers-table'
import { useContainersTable } from './hooks'
import { ViewsSelector } from './components/views-selector'
import { useEffect, useMemo } from 'react'
import { useSearchParams } from 'react-router'
import {
  containerOpenedItemSchema,
  serializeWorkroom,
} from '../../router/router-utils'
import { useAtomValue } from 'jotai'
import { defaultContainersViewAtom } from '../../config/store'
import {
  defaultContainersSortParam,
  getParsedArrayOfObjects,
} from '../../router/router'
import { containersStaticColumns } from './components/container-columns'
import { parseAsJson, useQueryState } from 'nuqs'

export const ContainersPage = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const defaultContainersView = useAtomValue(defaultContainersViewAtom)

  const [_, setOpenedContainer] = useQueryState(
    'opened_item',
    parseAsJson(containerOpenedItemSchema.parse)
  )

  const [state, setState] = useUrlState<UrlStateProps>(
    {},
    {
      ...useUrlStateDefaultOptions,
    }
  )
  const { workroomFields, isPending } = useContainersTable()

  const defaultValue = useMemo(
    () =>
      state.f?.find(
        (filter: FilterItem) => filter.field === 'provider_fields.issuetype'
      )?.value?.[0] || 'Epic',
    [state.f]
  )

  const issueTypeOptions =
    workroomFields?.find((field) => field.id === 'provider_fields.issuetype')
      ?.options || []

  const handleSelectChange = (value: string) => {
    setState((prevState) => {
      const currentFilters = [...(prevState.f || [])]

      return {
        ...prevState,
        f: [
          ...currentFilters.filter(
            (filter) => filter.field !== 'provider_fields.issuetype'
          ),
          {
            field: 'provider_fields.issuetype',
            op: 'eq',
            value: [value],
          },
        ],
        opened_item: null,
      }
    })
  }

  useEffect(() => {
    if (searchParams.size === 0) {
      const serializedContainersSearch = serializeWorkroom({
        f: defaultContainersView?.f
          ? getParsedArrayOfObjects(defaultContainersView.f)
          : [
              {
                field: 'provider_fields.issuetype',
                op: 'eq',
                value: ['Epic'],
              },
            ],
        s: defaultContainersView?.s
          ? getParsedArrayOfObjects(defaultContainersView.s)
          : [defaultContainersSortParam],
        selected_columns:
          defaultContainersView?.selected_columns || containersStaticColumns,
      })
      setSearchParams(serializedContainersSearch, { replace: true })
      return
    }
  }, [
    defaultContainersView?.f,
    defaultContainersView?.s,
    defaultContainersView?.selected_columns,
    searchParams.size,
    setSearchParams,
  ])

  useEffect(() => {
    const issue_id = searchParams.get('issue_id')
    const source_id = searchParams.get('source_id')
    if (issue_id && source_id) {
      const search = serializeWorkroom({
        f: defaultContainersView?.f
          ? getParsedArrayOfObjects(defaultContainersView.f)
          : [
              {
                field: 'provider_fields.issuetype',
                op: 'eq',
                value: ['Epic'],
              },
            ],
        s: defaultContainersView?.s
          ? getParsedArrayOfObjects(defaultContainersView.s)
          : [defaultContainersSortParam],
        selected_columns:
          defaultContainersView?.selected_columns || containersStaticColumns,
        opened_item: {
          issue_id: issue_id,
          source_id: Number(source_id),
        },
        view: searchParams.get('view') ?? defaultValue,
      })
      setSearchParams(search, { replace: true })
      setOpenedContainer({
        issue_id: issue_id,
        source_id: Number(source_id),
      })
    }
  }, [searchParams.size, searchParams])

  return (
    <div className="h-full flex flex-col">
      <div className="px-6 py-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <PageTitle title={t('containers')} />
            <div className="flex items-center gap-1">
              {isPending ? (
                <Skeleton className="w-[110px] h-[40px]" />
              ) : (
                <Select onValueChange={handleSelectChange} value={defaultValue}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {issueTypeOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <ViewsSelector />
          </div>
        </div>
      </div>
      <div className="px-6 pt-4">
        <ContainersTable />
      </div>
    </div>
  )
}
