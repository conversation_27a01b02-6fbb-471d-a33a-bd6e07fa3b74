import { FrownIcon } from 'lucide-react'
import { Link } from 'react-router'
import { t } from 'i18next'

export const NotFoundPage = () => {
  return (
    <div className="flex h-[100dvh] flex-col items-center justify-center bg-gray-50 px-4 dark:bg-gray-950">
      <div className="mx-auto max-w-md text-center">
        <FrownIcon className="mx-auto h-16 w-16 text-gray-500 dark:text-gray-400" />
        <h1 className="mt-6 text-4xl font-bold tracking-tight text-gray-900 dark:text-gray-50">
          {t('pageNotFound')}
        </h1>
        <p className="mt-4 text-gray-500 dark:text-gray-400">
          {t('pageNotFoundDescription')}
        </p>
        <div className="mt-6">
          <Link
            className="inline-flex items-center rounded-md bg-gray-900 px-4 py-2 text-sm font-medium text-gray-50 shadow transition-colors hover:bg-gray-900/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:pointer-events-none disabled:opacity-50 dark:bg-gray-50 dark:text-gray-900 dark:hover:bg-gray-50/90 dark:focus-visible:ring-gray-300"
            to="/"
          >
            {t('goBackHome')}
          </Link>
        </div>
      </div>
    </div>
  )
}
