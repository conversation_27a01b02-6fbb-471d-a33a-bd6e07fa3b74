import React, { useCallback, useMemo, useRef, useEffect } from 'react'
import type {
  ExternalControl,
  ExternalImplementation,
} from 'prime-front-service-client'
import { t } from 'i18next'
import { Separator } from '@libs/ui'
import { ControlCard } from './control-card'
import type { ExternalPrimeConcern } from 'prime-front-service-client/src/models/ExternalPrimeConcern'
import type { ExternalFrameworkConcern } from 'prime-front-service-client/src/models/ExternalFrameworkConcern'
import { useAtom } from 'jotai/index'
import { SecurityFramework as SecurityFrameworkInput } from 'prime-front-service-client'
import { selectedFrameworkAtom } from '../hooks'
import type { PrimeRecommendation } from 'prime-front-service-client/src/models/PrimeRecommendation'

interface ConfirmConcernsProps {
  reviewedConcerns: (ExternalPrimeConcern | ExternalFrameworkConcern)[]
  summary: boolean
  onCopyText: (text: string) => void
}

export const ConfirmConcerns = ({
  reviewedConcerns,
  summary,
  onCopyText,
}: ConfirmConcernsProps) => {
  const contentRef = useRef<HTMLDivElement>(null)

  const [selectedFramework] = useAtom<SecurityFrameworkInput>(
    selectedFrameworkAtom
  )

  const getConcernByType = useCallback(
    (concern: ExternalPrimeConcern | ExternalFrameworkConcern) => {
      if (
        selectedFramework === SecurityFrameworkInput.PRIME &&
        'recommendations' in concern
      ) {
        return concern.recommendations as PrimeRecommendation[]
      }

      if ('controls' in concern) {
        return concern.controls as ExternalControl[]
      }

      return []
    },
    [reviewedConcerns]
  )

  const handleCopyText = useCallback(() => {
    if (contentRef.current) {
      onCopyText(contentRef.current.innerText)
    }
  }, [reviewedConcerns, onCopyText])

  const getAllControlsDismissed = useMemo(
    () =>
      reviewedConcerns?.every((concern) =>
        getConcernByType(concern)?.every(
          (control: PrimeRecommendation | ExternalControl) => {
            if ('implementations' in control)
              return control?.implementations?.every(
                (rec: ExternalImplementation) => rec.status !== 'approved'
              )
          }
        )
      ),
    [reviewedConcerns]
  )

  useEffect(() => {
    handleCopyText()
  }, [reviewedConcerns, handleCopyText])

  return (
    <div className="max-h-[500px] overflow-auto" ref={contentRef}>
      {getAllControlsDismissed ? (
        <h1 className="text-md capitalize">{t('noApprovedConcerns')}</h1>
      ) : (
        reviewedConcerns.map((concern) => (
          <div key={concern.id}>
            <h1 className="text-md text-gray-700 font-semibold">
              {summary ? concern.short_description : concern.long_description}
            </h1>
            <div className="mt-6">
              {getConcernByType(concern) &&
                getConcernByType(concern)
                  ?.filter((control: PrimeRecommendation | ExternalControl) => {
                    if ('implementations' in control) {
                      return control.implementations.some(
                        (rec: ExternalImplementation) =>
                          rec.status === 'approved'
                      )
                    }
                    return false
                  })
                  .map(
                    (control: ExternalControl | PrimeRecommendation) =>
                      control && (
                        <ControlCard key={control.id} control={control} />
                      )
                  )}
            </div>
            <Separator className="mb-6" />
          </div>
        ))
      )}
    </div>
  )
}
