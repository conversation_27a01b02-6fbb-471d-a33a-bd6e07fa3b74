import { useCallback, useEffect, useMemo, useState } from 'react'
import { SecurityFramework as SecurityFrameworkInput } from 'prime-front-service-client'
import {
  HitrustIcn,
  NistIcn,
  PciDssIcn,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  ToggleGroup,
  ToggleGroupItem,
  PrimeIcn,
  CisIcon,
  Tabs,
  TabsList,
  TabsTrigger,
} from '@libs/ui'
import { t } from 'i18next'
import { useGetConfig } from '../../../api/use-config-api'
import { useAtom } from 'jotai'
import {
  selectedConcernType<PERSON>tom,
  selectedFrameworkAtom,
  selectedRaciAtom,
  useRecommendations,
} from '../hooks'
import type { ExternalFrameworkConcern } from 'prime-front-service-client/src/models/ExternalFrameworkConcern'
import type { ExternalPrimeConcern } from 'prime-front-service-client/src/models/ExternalPrimeConcern'
import { ConcernType } from 'prime-front-service-client/src/models/ConcernType'

interface ConcernsToolbarProps {
  caseDone: boolean
  showSelectFramework: boolean
  concerns: (ExternalFrameworkConcern | ExternalPrimeConcern)[]
  refetch: () => void
}

const frameworkToIcon: Record<SecurityFrameworkInput, JSX.Element> = {
  NIST: <NistIcn className={'w-10 h-6'} />,
  HITRUST: <HitrustIcn className={'w-15 h-6'} />,
  PCI: <PciDssIcn className={'w-15 h-7'} />,
  PRIME: <PrimeIcn className={'w-15 h-7'} />,
  CIS: <CisIcon className={'w-15 h-7'} />,
}

export const ConcernsToolbar = ({
  caseDone,
  concerns,
  showSelectFramework,
  refetch,
}: ConcernsToolbarProps) => {
  const { data: config } = useGetConfig()

  const { primeRaciFilters, frameworkRaciFilters } = useRecommendations()

  const [selectedFramework, setSelectedFramework] =
    useAtom<SecurityFrameworkInput>(selectedFrameworkAtom)

  const [type, setType] = useState<'security' | 'privacy'>('security')

  const [selectedConcernType, setSelectedConcernType] = useAtom(
    selectedConcernTypeAtom
  )

  const raciFilter = useMemo(() => {
    return selectedFramework === SecurityFrameworkInput.PRIME
      ? primeRaciFilters(concerns as ExternalPrimeConcern[])
      : frameworkRaciFilters(concerns as ExternalFrameworkConcern[])
  }, [concerns, selectedFramework])

  const [selectedRaci, setSelectedRaci] = useAtom(selectedRaciAtom)

  const handleRaciToggle = useCallback(
    (raci: string) => {
      setSelectedRaci((prevSelectedRaci) => {
        if (prevSelectedRaci.includes(raci)) {
          return prevSelectedRaci.filter((item) => item !== raci)
        } else {
          return [...prevSelectedRaci, raci]
        }
      })
    },
    [setSelectedRaci]
  )

  useEffect(() => {
    if (selectedFramework === SecurityFrameworkInput.PRIME) {
      setSelectedRaci([])
    } else {
      setSelectedRaci([])
    }
  }, [selectedFramework, setSelectedRaci])

  useEffect(() => {
    type === 'security'
      ? setSelectedConcernType(ConcernType.Mitre)
      : setSelectedConcernType(ConcernType.Linddun)
  }, [setSelectedConcernType, type])

  useEffect(() => {
    setSelectedRaci([])
  }, [selectedConcernType, setSelectedRaci])

  return (
    <div className="concerns-toolbar px-9">
      <div className="mb-6">
        <h1 className="text-lg text-slate-700 capitalize">
          {t('recommendationsTitle')}
        </h1>
      </div>
      {!caseDone && (
        <div className="flex mb-4">
          <Tabs
            defaultValue={type}
            value={type}
            onValueChange={(value: string) =>
              setType(value as 'security' | 'privacy')
            }
          >
            <TabsList>
              <TabsTrigger value="security" className="capitalize">
                {t('security')}
              </TabsTrigger>
              <TabsTrigger value="privacy" className="capitalize">
                {t('privacy')}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      )}
      {!caseDone && (
        <div>
          <div className="mb-6 flex justify-start">
            <ToggleGroup
              className="flex flex-wrap justify-start gap-2 max-w-full"
              type="multiple"
              value={selectedRaci}
            >
              {raciFilter?.map((raci: string) => (
                <ToggleGroupItem
                  className="border border-slate-500 text-slate-500 bg-slate-100 font-light rounded-xl data-[state=on]:text-slate-100 data-[state=on]:bg-slate-500 whitespace-nowrap"
                  key={crypto.randomUUID()}
                  value={raci}
                  aria-label={raci}
                  onClick={() => handleRaciToggle(raci)}
                >
                  <span>{raci}</span>
                </ToggleGroupItem>
              ))}
            </ToggleGroup>
          </div>

          {showSelectFramework && (
            <div>
              <div className="flex w-full justify-end">
                <div>
                  <Select
                    defaultValue={selectedFramework}
                    onValueChange={(value) =>
                      setSelectedFramework((prev) => {
                        if (prev !== value) {
                          refetch()
                        }
                        return value as SecurityFrameworkInput
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent align="center">
                      <SelectItem value={'PRIME'}>
                        <div className="flex items-center gap-2">
                          <PrimeIcn />
                        </div>
                      </SelectItem>
                      {config?.security_framework && (
                        <SelectItem value={config?.security_framework}>
                          <div className="flex items-center gap-2">
                            {frameworkToIcon[config?.security_framework]}
                          </div>
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
