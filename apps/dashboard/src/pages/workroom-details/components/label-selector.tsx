import * as React from 'react'
import { Check, Plus, PlusIcon, XIcon } from 'lucide-react'
import {
  Badge,
  Button,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@libs/ui'
import { cn } from '@libs/common'
import {
  refetchCases<PERSON>tom,
  useAddLabel,
  useGetLabels,
} from '../../../api/use-cases-api'
import { useEffect } from 'react'
import { useSetAtom } from 'jotai'
import { t } from 'i18next'

interface LabelSelectorProps {
  source_id: number
  issue_id: string
  caseLabels: string[]
}
export const LabelSelector = ({
  source_id,
  issue_id,
  caseLabels,
}: LabelSelectorProps) => {
  const setRefetchCases = useSetAtom(refetchCasesAtom)
  const { data } = useGetLabels()
  const labelsMutation = useAddLabel()
  const [open, setOpen] = React.useState(false)
  const [selectedLabels, setSelectedLabels] =
    React.useState<string[]>(caseLabels)
  const [accountLabels, setAccountLabels] = React.useState<string[]>(
    data?.labels || []
  )
  const [search, setSearch] = React.useState('')
  const [commandInputError, setCommandInputError] = React.useState('')

  const mutateLabels = (labels: string[]) => {
    labelsMutation.mutate(
      {
        source_id,
        issue_id,
        labels,
      },
      {
        onSuccess: () => {
          setRefetchCases((prev) => prev + 1)
        },
      }
    )
  }

  const handleSelect = (currentValue: string) => {
    setSelectedLabels((prev) => {
      const nextLabels = prev.includes(currentValue)
        ? prev.filter((item) => item !== currentValue)
        : [...prev, currentValue]

      mutateLabels(nextLabels)

      return nextLabels
    })
  }

  const handleDelete = (currentValue: string) => {
    const nextLabels = [...selectedLabels].filter(
      (item) => item !== currentValue
    )
    setSelectedLabels((prev) => prev.filter((item) => item !== currentValue))

    mutateLabels(nextLabels)
  }

  const handleCreateNew = () => {
    const newLabel = search.trim()
    if (!newLabel) {
      setCommandInputError(t('labelCannotBeEmpty'))
      return
    }
    if (newLabel.includes(' ')) {
      setCommandInputError(t('labelCannotContainSpaces'))
      return
    }
    setAccountLabels((prev) => [...prev, newLabel])
    setSelectedLabels((prev) => [...prev, newLabel])
    setSearch('')

    mutateLabels([...selectedLabels, newLabel])
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      handleCreateNew()
    }
  }

  useEffect(() => {
    if (data?.labels) {
      setAccountLabels(data?.labels || [])
    }
  }, [data?.labels])

  useEffect(() => {
    setSelectedLabels(caseLabels)
  }, [caseLabels])

  return (
    <div className="flex flex-wrap gap-3">
      {selectedLabels.map((selectedValue) => {
        const label = accountLabels.find((l) => l === selectedValue)
        return label ? (
          <Badge
            key={crypto.randomUUID()}
            variant="secondary"
            className="rounded-full text-gray-400 border-gray-400 h-6 bg-white font-light gap-2"
          >
            <span> {label}</span>
            <Button
              onClick={() => handleDelete(selectedValue)}
              size="icon"
              variant="ghost"
              dataTestId="delete-label-button"
              className="w-3.5 h-3.5 hover:bg-slate-200"
            >
              <XIcon />
            </Button>
          </Badge>
        ) : null
      })}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="rounded-full h-6 gap-2"
            dataTestId="add-label-button"
          >
            <span>{t('addLabel')}</span>
            <PlusIcon size={16} />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command
            filter={(value, search) => {
              return value.toLocaleLowerCase().includes(search.toLowerCase())
                ? 1
                : 0
            }}
          >
            <CommandInput
              placeholder={t('searchLabels')}
              value={search}
              onValueChange={(value) => {
                setSearch(value)
                setCommandInputError('')
              }}
              onKeyDown={handleKeyDown}
            />
            {commandInputError && (
              <span className="p-2 text-destructive-foreground text-xs">
                {commandInputError}
              </span>
            )}
            <CommandList>
              <CommandEmpty className="p-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  dataTestId="create-new-label"
                  onClick={handleCreateNew}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  <span className="capitalize mr-1">{t('create')}</span>"
                  {search}"
                </Button>
              </CommandEmpty>
              <CommandGroup className="h-60 overflow-auto">
                {accountLabels.map((label) => (
                  <CommandItem key={label} onSelect={() => handleSelect(label)}>
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        selectedLabels.includes(label)
                          ? 'opacity-100'
                          : 'opacity-0'
                      )}
                    />
                    {label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
