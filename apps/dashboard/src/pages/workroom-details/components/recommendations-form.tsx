import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Checkbox,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  TooltipTrigger,
} from '@libs/ui'
import { cn } from '@libs/common'
import { CodeXml, Copy } from 'lucide-react'
import React, { useCallback, useState } from 'react'
import { toast } from 'sonner'
import { t } from 'i18next'
import SyntaxHighlighter from 'react-syntax-highlighter'
import { atelierCaveDark } from 'react-syntax-highlighter/dist/esm/styles/hljs'
import type { ExternalImplementation } from 'prime-front-service-client'

interface RecommendationFormProps {
  recommendations: ExternalImplementation[]
  selectedRecommendations: number[]
  showSnippet: boolean
  onSelectionChange: (selectedRecommendations: number[]) => void
}
export const RecommendationsForm = ({
  recommendations,
  selectedRecommendations,
  showSnippet,
  onSelectionChange,
}: RecommendationFormProps) => {
  const [visibleSnippets, setVisibleSnippets] = useState<number[]>([])

  const handleToggleSnippet = (id: number) => {
    setVisibleSnippets((prev) =>
      prev.includes(id)
        ? prev.filter((snippetId) => snippetId !== id)
        : [...prev, id]
    )
  }
  const handleCopy = (snippet: string) => {
    navigator.clipboard
      .writeText(snippet)
      .then(() => toast.success(t('copySuccess')))
  }

  const handleRecommendationCheckboxChange = (id: number) => {
    const newSelectedRecommendations = selectedRecommendations.includes(id)
      ? selectedRecommendations.filter((recId) => recId !== id)
      : [...selectedRecommendations, id]
    onSelectionChange(newSelectedRecommendations)
  }

  const recommendationControls = useCallback(
    (recommendation: ExternalImplementation) => {
      if (
        recommendation?.controls &&
        typeof recommendation.controls === 'object'
      ) {
        const controlsKeys = Object.keys(recommendation.controls)
        return controlsKeys.length
          ? recommendation?.controls[controlsKeys[0]]
          : []
      }
      return []
    },
    [recommendations]
  )

  const controlIdsTooltip = useCallback(
    (controls: string[]) => {
      return controls.length
        ? controls?.slice(1, controls.length)?.join(', ')
        : ''
    },
    [recommendations]
  )

  return (
    <div className="ml-4">
      {recommendations.map((recommendation: ExternalImplementation) => (
        <div key={recommendation.id} className="flex mt-3">
          <Checkbox
            className="mt-1 mr-3 shadow-none bg-gray-200 font-light text-gray-800 border-none data-[state=checked]:bg-gray-200 data-[state=checked]:text-gray-800"
            checked={selectedRecommendations?.includes(recommendation.id)}
            onClick={() =>
              handleRecommendationCheckboxChange(recommendation.id)
            }
          />
          <div
            className={cn(
              'w-full pr-2',
              visibleSnippets.includes(recommendation.id) ? 'pr-10' : ''
            )}
          >
            <div className="flex items-start justify-between gap-3">
              {recommendation.recommendation}
              {showSnippet && recommendation.controls && (
                <div className="flex items-center text-sm font-light gap-1 text-slate-400 whitespace-nowrap">
                  <div className="font-medium capitalize">{t('controls')}:</div>
                  <div className="flex items-center gap-1">
                    <span>
                      {recommendationControls(recommendation)
                        ?.slice(0, 1)
                        ?.join(', ')}
                    </span>
                    {recommendationControls(recommendation)?.length > 1 && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="text-slate-500 border border-slate-500 rounded-full px-2">
                            {`+${
                              recommendationControls(recommendation)?.length - 1
                            } `}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent side="right">
                          <div className="w-[150px]">
                            <div className="mb-2 capitalize">
                              {t('controls')}:
                            </div>
                            <div>
                              {controlIdsTooltip(
                                recommendationControls(recommendation)
                              )}
                            </div>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center gap-1">
                {recommendation.raci?.map((raci: string) => (
                  <Badge
                    key={crypto.randomUUID()}
                    data-testid="raci-badge"
                    className="font-light bg-white text-gray-400 border-gray-400 px-2 py-1 shadow-none cursor-default hover:bg-white"
                  >
                    {raci}
                  </Badge>
                ))}
              </div>
              {showSnippet &&
                recommendation.raci?.includes('Dev') &&
                !!Object.keys(recommendation?.code_snippets || {}).length && (
                  <Button
                    className={cn(
                      'rounded-full p-1 h-6',
                      visibleSnippets.includes(recommendation.id)
                        ? 'bg-slate-200'
                        : 'bg-transparent'
                    )}
                    variant="outline"
                    dataTestId="toggle-code-snippet"
                    onClick={() => handleToggleSnippet(recommendation.id)}
                  >
                    <CodeXml size={16} />
                  </Button>
                )}
            </div>
            {visibleSnippets.includes(recommendation.id) && (
              <div className="mt-6">
                <div className="text-gray-500 font-light text-sm">
                  {t('codeSnippetDisclaimer')}
                </div>
                <div className="relative w-full py-2">
                  <Button
                    onClick={() =>
                      handleCopy(
                        Object.values(recommendation?.code_snippets || {})[0]
                      )
                    }
                    dataTestId="copy-code-snippet"
                    className="absolute top-6 right-2 bg-gray-700 text-white text-sm p-2 rounded hover:bg-gray-600"
                  >
                    <Copy size={16} />
                  </Button>
                  <SyntaxHighlighter
                    language="java"
                    style={atelierCaveDark}
                    customStyle={{
                      fontSize: '12px',
                    }}
                    showLineNumbers
                  >
                    {Object.values(recommendation?.code_snippets || {}).length
                      ? Object.values(recommendation?.code_snippets || {})[0]
                      : ''}
                  </SyntaxHighlighter>
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
