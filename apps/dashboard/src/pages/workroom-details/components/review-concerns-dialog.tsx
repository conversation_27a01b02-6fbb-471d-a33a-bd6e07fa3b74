import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Button,
  CompletedIcn,
  JiraIcn,
} from '@libs/ui'
import { CheckCircle2, Files, Loader2 } from 'lucide-react'
import { t } from 'i18next'
import { ConfirmConcerns } from './confirm-concerns'
import { useCallback, useState } from 'react'
import { toast } from 'sonner'
import { useIsMutating } from '@tanstack/react-query'
import { CaseStatus } from 'prime-front-service-client'
import { useAddWatcher, useWriteBack } from '../../../api/use-cases-api'
import { useParams } from 'react-router'
import type { ExternalPrimeConcern } from 'prime-front-service-client/src/models/ExternalPrimeConcern'
import type { ExternalFrameworkConcern } from 'prime-front-service-client/src/models/ExternalFrameworkConcern'

interface ReviewConcernsDialogProps {
  openConfirm: boolean
  summary: boolean
  reviewedConcerns: (ExternalPrimeConcern | ExternalFrameworkConcern)[]
  setOpenConfirm: (value: ((prevState: boolean) => boolean) | boolean) => void
  refetch: () => void
  updateCaseStatus: (status: CaseStatus) => Promise<void>
}
export const ReviewConcernsDialog = ({
  openConfirm,
  summary,
  reviewedConcerns,
  setOpenConfirm,
  updateCaseStatus,
  refetch,
}: ReviewConcernsDialogProps) => {
  const params = useParams()
  const { sourceId, issueId } = params
  const writeBackMutation = useWriteBack()
  const addWatcherMutation = useAddWatcher()

  const handleCopyText = useCallback((text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => toast.success(t('copySuccess')))
  }, [])

  const [textToCopy, setTextToCopy] = useState('')
  const [watchersDialogOpen, setWatchersDialogOpen] = useState<boolean>(false)

  const completeReviewLoading = useIsMutating({
    mutationKey: ['updateCaseStatus'],
  })

  const setCommentAndComplete = async () => {
    await updateCaseStatus(CaseStatus.done)
  }

  const writeBack = async () => {
    writeBackMutation.mutate(
      {
        source_id: sourceId as unknown as number,
        issue_id: issueId || '',
      },
      {
        onSuccess: async () => {
          toast.success(t('commentUpdatedSuccessfully'))
          setWatchersDialogOpen(true)
        },
        onError: async () => {
          toast.error(t('errors.commentUpdateFailed'))
        },
      }
    )
  }

  const handleConfirm = () => {
    refetch()
    setOpenConfirm(true)
  }

  const addWatcher = () => {
    addWatcherMutation.mutate(
      { source_id: sourceId ? +sourceId : 0, issue_id: issueId || '' },
      {
        onSuccess: async () => {
          toast.success(t('addWatcherSuccess'))
          setWatchersDialogOpen(false)
        },
        onError: async () => {
          toast.error(t('errors.addWatcherFailed'))
        },
      }
    )
  }

  return (
    <AlertDialog open={openConfirm} onOpenChange={setOpenConfirm}>
      <AlertDialogTrigger asChild onClick={handleConfirm}>
        <Button
          dataTestId="confirm-selected"
          className="flex items-center gap-1"
        >
          <CheckCircle2 className="mr-1" />
          {t('confirm')}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="min-w-[800px] py-8 px-12">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex justify-center items-center sm:justify-center py-4 text-2xl font-normal">
            {t('workroomPage.dialogTitle')}
          </AlertDialogTitle>
          <AlertDialogDescription className="border border-gray-300 p-6 pr-0 rounded-xl">
            <ConfirmConcerns
              summary={summary}
              reviewedConcerns={reviewedConcerns}
              onCopyText={setTextToCopy}
            />
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogDescription></AlertDialogDescription>
        <AlertDialogFooter className="flex justify-center items-center sm:justify-center">
          <AlertDialogCancel className="border-none shadow-none">
            {t('cancel')}
          </AlertDialogCancel>
          <Button
            variant="outline"
            disabled={writeBackMutation.isPending}
            dataTestId="write-to-jira"
            onClick={() => writeBack()}
          >
            {writeBackMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            <JiraIcn className="h-4 mr-1" />
            {t('workroomPage.writeToJira')}
          </Button>
          <Button
            variant="outline"
            className="capitalize"
            dataTestId="copy-concerns"
            onClick={() => handleCopyText(textToCopy)}
          >
            <Files className="h-4 mr-1" />
            {t('copy')}
          </Button>
          <AlertDialogAction
            data-testid="complete-review"
            onClick={setCommentAndComplete}
            disabled={!!completeReviewLoading}
          >
            {!!completeReviewLoading && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            <CompletedIcn white={true} />
            <span className="ml-2">{t('workroomPage.completeReview')}</span>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>

      <AlertDialog
        open={watchersDialogOpen}
        onOpenChange={setWatchersDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center py-2 text-l font-medium">
              {t('addWatchersDialogTitle')}
            </AlertDialogTitle>
            <AlertDialogDescription className="pb-6">
              <div>{t('recommendationWrittenToJira')}</div>
              <div>{t('AddWatchersQuestion')}</div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex items-center">
            <Button dataTestId="copy-concerns" onClick={() => addWatcher()}>
              {t('addWatcherConfirm')}
            </Button>
            <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AlertDialog>
  )
}
