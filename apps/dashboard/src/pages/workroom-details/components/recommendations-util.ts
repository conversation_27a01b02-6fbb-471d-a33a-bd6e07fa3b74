import type {
  CustomRecommendationInput,
  CustomRecommendationOutput,
  ExternalControl,
  ExternalImplementation,
} from 'prime-front-service-client'

import { SecurityFramework as SecurityFrameworkInput } from 'prime-front-service-client'
import type { ExternalFrameworkConcern } from 'prime-front-service-client/src/models/ExternalFrameworkConcern'
import type { ExternalPrimeConcern } from 'prime-front-service-client/src/models/ExternalPrimeConcern'
import type { ConcernType } from 'prime-front-service-client/src/models/ConcernType'
import type { PrimeRecommendation } from 'prime-front-service-client/src/models/PrimeRecommendation'

export const getCustomRecommendationsByControl = (
  controlId: string,
  recommendations: CustomRecommendationInput[]
) => {
  return recommendations.length
    ? recommendations.filter(
        (recommendation) => recommendation.control_id === controlId
      )
    : []
}

export const filterByType = (
  concerns: (ExternalPrimeConcern | ExternalFrameworkConcern)[],
  type: ConcernType
) => {
  return concerns.filter((concern) => concern.methodology?.type === type)
}

export const filterByRaci = (
  concerns: (ExternalPrimeConcern | ExternalFrameworkConcern)[],
  selectedRaci: string[]
): (ExternalPrimeConcern | ExternalFrameworkConcern)[] => {
  if (selectedRaci.length === 0) return concerns

  return concerns
    .map((concern) => {
      const filteredConcern =
        'recommendations' in concern
          ? {
              ...concern,
              recommendations: concern.recommendations
                .map((rec) => ({
                  ...rec,
                  implementations: rec.implementations.filter((impl) =>
                    impl.raci?.some((raci) => selectedRaci.includes(raci))
                  ),
                }))
                .filter((rec) => rec.implementations.length > 0),
            }
          : {
              ...concern,
              controls: concern.controls
                .map((control) => ({
                  ...control,
                  implementations: control.implementations.filter((impl) =>
                    impl.raci?.some((raci) => selectedRaci.includes(raci))
                  ),
                }))
                .filter((control) => control.implementations.length > 0),
            }

      return ('recommendations' in filteredConcern &&
        filteredConcern.recommendations.length > 0) ||
        ('controls' in filteredConcern && filteredConcern.controls.length > 0)
        ? filteredConcern
        : null
    })
    .filter(Boolean) as (ExternalPrimeConcern | ExternalFrameworkConcern)[]
}

export const concernTotalRecommendations = (
  concern: ExternalPrimeConcern | ExternalFrameworkConcern,
  selectedFramework: SecurityFrameworkInput
) => {
  return concern && getConcernByType(concern, selectedFramework)
    ? getConcernByType(concern, selectedFramework).flatMap(
        (control: PrimeRecommendation | ExternalControl) =>
          control.implementations
      ).length
    : 0
}
export const getConcernByType = (
  concern: ExternalPrimeConcern | ExternalFrameworkConcern,
  selectedFramework: SecurityFrameworkInput
) => {
  if (
    selectedFramework === SecurityFrameworkInput.PRIME &&
    'recommendations' in concern
  ) {
    return concern.recommendations as PrimeRecommendation[]
  } else if ('controls' in concern) {
    return concern.controls as ExternalControl[]
  }
  return []
}

export const concernFilteredRecommendations = (
  concern: ExternalPrimeConcern | ExternalFrameworkConcern,
  selectedFramework: SecurityFrameworkInput,
  selectedRaci: string[]
) => {
  const selectedRaciSet = new Set(selectedRaci)

  return selectedRaci?.length
    ? getConcernByType(concern, selectedFramework)
        .flatMap(
          (control: PrimeRecommendation | ExternalControl) =>
            control.implementations
        )
        .filter((recommendation: ExternalImplementation) =>
          recommendation.raci?.some((raciRole: string) =>
            selectedRaciSet.has(raciRole)
          )
        ).length
    : concernTotalRecommendations(concern, selectedFramework)
}

export const getAllPossibleTags = (
  customRecommendations: CustomRecommendationOutput[]
): string[] => {
  return Array.from(
    new Set<string>(
      customRecommendations?.flatMap(
        (recommendation) => recommendation.tags || []
      )
    )
  )
}

export const filterCustomRecommendations = (
  customRecommendations: CustomRecommendationOutput[],
  tags: string[]
): CustomRecommendationOutput[] => {
  const tagsSet = new Set(tags)
  return tags.length
    ? customRecommendations.filter((recommendation) =>
        recommendation.tags?.some((tag) => tagsSet.has(tag))
      )
    : customRecommendations
}

export const getConcernsByFramework = (
  frameworkConcerns: { [key: string]: Array<ExternalFrameworkConcern> },
  primeConcerns: ExternalPrimeConcern[],
  framework: SecurityFrameworkInput
) => {
  if (framework === SecurityFrameworkInput.PRIME) {
    return primeConcerns
  } else {
    return frameworkConcerns[framework]
  }
}
