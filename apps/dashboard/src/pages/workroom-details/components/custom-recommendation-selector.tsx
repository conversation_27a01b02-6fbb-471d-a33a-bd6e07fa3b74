/*eslint-disable max-lines*/
import { Plus, PlusIcon } from 'lucide-react'
import {
  Badge,
  Button,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  Popover,
  PopoverContent,
  PopoverTrigger,
  TagsSelector,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@libs/ui'
import { useEffect, useMemo, useState } from 'react'
import { t } from 'i18next'
import { useGetConfig, useUpdateConfig } from '../../../api/use-config-api'
import { toast } from 'sonner'
import { Link, useParams } from 'react-router'
import { useGetUserInfo } from '../../../api/use-auth-api'
import { useGetUsers } from '../../../api/use-users-api'
import type {
  CustomRecommendationOutput,
  ExternalControl,
  ImplementationStatusUpdate,
} from 'prime-front-service-client'
import { useUpdateRecommendation } from '../../../api/use-cases-api'
import {
  filterCustomRecommendations,
  getAllPossibleTags,
} from './recommendations-util'
import type { PrimeRecommendation } from 'prime-front-service-client/src/models/PrimeRecommendation'
interface CustomRecommendationSelectorProps {
  control: PrimeRecommendation | ExternalControl
  concern_id: number
  selectedRecommendations: number[]
  refetch: () => void
}

const labelClassName =
  'rounded-full text-gray-400 border-gray-400 h-6 bg-white font-light gap-2'
export const CustomRecommendationSelector = ({
  control,
  concern_id,
  selectedRecommendations,
  refetch,
}: CustomRecommendationSelectorProps) => {
  const params = useParams()
  const { sourceId, issueId } = params
  const { data: config, refetch: refetchConfig } = useGetConfig()
  const { mutate: updateConfigMutation, isPending: isUpdatePending } =
    useUpdateConfig()
  const { data: userInfo } = useGetUserInfo()
  const { data: users } = useGetUsers()
  const { mutate: updateRecommendationsMutation } = useUpdateRecommendation()
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState('')
  const [commandInputError, setCommandInputError] = useState('')
  const [tags, setTags] = useState<string[]>([])
  const [filteredRecommendations, setFilteredRecommendations] = useState<
    CustomRecommendationOutput[]
  >([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [openTagFilter, setOpenTagFilter] = useState<boolean>(false)
  const [customRecommendations, setCustomRecommendations] = useState<
    CustomRecommendationOutput[]
  >([])

  const custom_recommendations = config?.custom_recommendations || []

  const user = useMemo(() => {
    return users?.find((user) => user.user_id === userInfo?.user_id)
  }, [users, userInfo])

  useEffect(() => {
    setCustomRecommendations(config?.custom_recommendations || [])
    setTags(getAllPossibleTags(custom_recommendations))
    setFilteredRecommendations(custom_recommendations)
  }, [config, config?.custom_recommendations])

  useEffect(() => {
    setFilteredRecommendations(
      filterCustomRecommendations(customRecommendations, selectedTags)
    )
  }, [selectedTags])

  const mutateRecommendation = (customRecommendation: string) => {
    updateConfigMutation(
      {
        AccountConfigUpdate: {
          custom_recommendations: [
            ...custom_recommendations,
            {
              framework: config?.security_framework,
              control_id: control.id,
              recommendation: customRecommendation,
              updated_at: new Date(),
              created_at: new Date(),
              updated_by: user?.email_address || '',
              created_by: user?.email_address || '',
              issue_id: issueId,
              source_id: sourceId ? +sourceId : 0,
            },
          ],
        },
      },
      {
        onSuccess: async () => {
          await refetchConfig()
          await refetch()
          const newRecommendation = custom_recommendations.find(
            (rec) =>
              rec.recommendation === customRecommendation &&
              rec.control_id === control.id
          )
          if (newRecommendation) {
            await onConfirmedRecommendationsChange(newRecommendation)
          }
          toast.success(t('addedCustomRecommendationSuccessfully'))
        },
        onError: async () => {
          toast.error(t('errors.addingCustomRecommendationFailed'))
        },
      }
    )
  }
  const onConfirmedRecommendationsChange = async (
    recommendation: CustomRecommendationOutput
  ) => {
    updateRecommendationsMutation(
      {
        source_id: sourceId as unknown as number,
        issue_id: issueId || '',
        recommendationStatusUpdate: [
          ...control.implementations.map((rec) => {
            return {
              id: rec.id,
              status: selectedRecommendations.includes(rec.id)
                ? 'approved'
                : 'dismissed',
            } as ImplementationStatusUpdate
          }),
          {
            id: recommendation.id,
            status: 'approved',
            concern_id: concern_id,
            control_id: control.id,
          } as ImplementationStatusUpdate,
        ],
      },
      {
        onSuccess: async () => {
          refetch()
          await refetchConfig()
          setOpen(false)
          toast.success(t('updatedRecommendationsSuccessfully'))
        },
        onError: async () => {
          toast.error(t('errors.updatingRecommendationsFailed'))
        },
      }
    )
  }
  const handleSelect = async (currentValue: CustomRecommendationOutput) => {
    await onConfirmedRecommendationsChange(currentValue)
  }
  const handleCreateNew = () => {
    mutateRecommendation(search)
    setSearch('')
  }
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      handleCreateNew()
    }
  }

  useEffect(() => {
    setSelectedTags([])
    setTags(getAllPossibleTags(custom_recommendations) || [])
  }, [open])

  return (
    <div className="flex flex-wrap gap-3">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="capitalize ml-4"
            dataTestId="add-recommendation"
          >
            <PlusIcon className="mr-2 h-4 w-4" />
            {t('addRecommendation')}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[600px] p-0" align="start">
          <Command className="border-b border-gray-200">
            <div className="flex items-center border-b border-gray-200">
              <div className="grow">
                <CommandInput
                  wrapperClass="border-none"
                  placeholder={t('searchRecommendations')}
                  value={search}
                  onValueChange={(value) => {
                    setSearch(value)
                    setCommandInputError('')
                  }}
                  onKeyDown={handleKeyDown}
                />
              </div>
              <div className="capitalize flex items-center flex-none">
                <TagsSelector
                  tagIcon
                  customTrigger={`${t('filter')} ${
                    selectedTags.length ? `(${selectedTags.length})` : ''
                  }`}
                  createNewTag={false}
                  showSelected={false}
                  selectedTags={selectedTags}
                  availableTags={tags}
                  onTagsChange={(tags) => {
                    setSelectedTags(tags)
                  }}
                  onOpenChange={(open) => {
                    setOpenTagFilter(open)
                  }}
                />
              </div>
            </div>
            {commandInputError && (
              <span className="p-2 text-destructive-foreground text-xs">
                {commandInputError}
              </span>
            )}
            <CommandEmpty className="p-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start"
                onClick={handleCreateNew}
                disabled={isUpdatePending}
                dataTestId="create-new-recommendation"
              >
                <Plus className="mr-2 h-4 w-4" />
                <span className="capitalize mr-1">{t('create')}</span>"{search}"
              </Button>
            </CommandEmpty>
            <CommandGroup className="h-60 overflow-auto text-gray-400">
              {filteredRecommendations?.map((customRecommendation) => (
                <CommandItem
                  disabled={openTagFilter}
                  key={customRecommendation.id}
                  onSelect={() => handleSelect(customRecommendation)}
                >
                  <div className="w-full flex items-center justify-between">
                    <div className="w-[400px]">
                      {customRecommendation.recommendation}
                    </div>
                    <div className="flex flex-wrap gap-1 text-muted-foreground ">
                      {customRecommendation.tags?.length &&
                      customRecommendation.tags?.length > 2 ? (
                        <>
                          <Badge variant="secondary" className={labelClassName}>
                            {customRecommendation?.tags?.[0]}
                          </Badge>
                          <Badge variant="secondary" className={labelClassName}>
                            {customRecommendation?.tags?.[1]}
                          </Badge>

                          <Tooltip>
                            <TooltipTrigger>
                              <Badge
                                variant="secondary"
                                className={labelClassName}
                              >
                                +
                                {customRecommendation?.tags?.length
                                  ? customRecommendation?.tags?.length - 2
                                  : 0}
                              </Badge>
                            </TooltipTrigger>
                            <TooltipContent
                              side="left"
                              className="w-full max-w-60 gap-2 flex flex-wrap"
                            >
                              {customRecommendation?.tags?.map(
                                (label, index) => (
                                  <div key={crypto.randomUUID()}>
                                    {label}
                                    {!!customRecommendation?.tags?.length &&
                                      (customRecommendation?.tags?.length -
                                        1 ===
                                      index
                                        ? ''
                                        : ',')}
                                  </div>
                                )
                              )}
                            </TooltipContent>
                          </Tooltip>
                        </>
                      ) : (
                        customRecommendation?.tags?.map((label) => (
                          <Badge
                            key={crypto.randomUUID()}
                            variant="secondary"
                            className={labelClassName}
                          >
                            {label}
                          </Badge>
                        ))
                      )}
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
          <div className="text-gray-400 font-light text-xs p-2">
            <span className="font-semibold mr-1 capitalize">{t('tip')}:</span>
            {t('customRecommendationTip')}
            <Link
              className="underline ml-1"
              to={`/settings/custom-recommendations`}
            >
              {t('settingsPage')}
            </Link>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
