import {
  Button,
  Checkbox,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
  DialogClose,
  LinddunIcon,
  MitreIcon,
} from '@libs/ui'
import { SparklesIcon, Loader2 } from 'lucide-react'
import { t } from 'i18next'
import { ConcernType } from 'prime-front-service-client/src/models/ConcernType'
import type { ExternalPrimeConcern } from 'prime-front-service-client/src/models/ExternalPrimeConcern'
import type { ExternalFrameworkConcern } from 'prime-front-service-client/src/models/ExternalFrameworkConcern'
import { useGenerateRecommendationsForConcernIds } from '../../../api/use-cases-api'
import { toast } from 'sonner'
import { useParams } from 'react-router'
import { useState } from 'react'

interface GenerateRecommendationsDialogProps {
  selectedConcernType: ConcernType
  concernsByFramework: (ExternalPrimeConcern | ExternalFrameworkConcern)[]
}

export const GenerateRecommendationsDialog = ({
  selectedConcernType,
  concernsByFramework,
}: GenerateRecommendationsDialogProps) => {
  const params = useParams()
  const { sourceId, issueId } = params
  const [selectedConcerns, setSelectedConcerns] = useState<number[]>([])
  const [open, setOpen] = useState(false)

  const handleOpenChange = (open: boolean) => {
    setOpen(open)
    if (!open) {
      setSelectedConcerns([])
    }
  }

  const {
    mutate: generateRecommendationsForConcernIds,
    isPending: isGenerating,
  } = useGenerateRecommendationsForConcernIds()

  const handleConcernToggle = (concernId: number) => {
    setSelectedConcerns((prev) =>
      prev.includes(concernId)
        ? prev.filter((id) => id !== concernId)
        : [...prev, concernId]
    )
  }

  const handleGenerateRecommendations = () => {
    if (selectedConcerns.length === 0) {
      toast.error(t('errors.selectAtLeastOneConcern'))
      return
    }

    generateRecommendationsForConcernIds(
      {
        concern_ids: selectedConcerns,
        issue_id: issueId || '',
        source_id: sourceId as unknown as number,
      },
      {
        onSuccess: () => {
          setOpen(false)
          toast.success(t('recommendationsGeneratedSuccessfully'), {
            duration: 8000,
          })
          setSelectedConcerns([])
        },
        onError: () => {
          toast.error(t('errors.recommendationsGenerationFailed'))
        },
      }
    )
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          dataTestId="generate-recommendations-button-modal"
        >
          <SparklesIcon className="h-5 w-5 mr-1" />
          {t('generatePrimeRecommendations')}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="mb-4">
            Generate Prime Recommendations
          </DialogTitle>
          <DialogDescription>
            <div className="space-y-4 mb-3">
              <div>
                Select concerns to generate recommendations in{' '}
                {selectedConcernType === ConcernType.Mitre
                  ? 'Security'
                  : 'Privacy'}{' '}
                category
              </div>
              <div>
                {selectedConcernType === ConcernType.Mitre ? (
                  <MitreIcon className="h-4 w-24" />
                ) : (
                  <LinddunIcon className="h-3 w-20" />
                )}
              </div>
            </div>
            <div className="flex flex-col gap-4 ">
              {concernsByFramework.map((item) => (
                <div className="flex items-center space-x-2" key={item.id}>
                  <Checkbox
                    id={`concern-${item.id}`}
                    checked={selectedConcerns.includes(item.id)}
                    onCheckedChange={() => handleConcernToggle(item.id)}
                  />
                  <label
                    htmlFor={`concern-${item.id}`}
                    className="text-slate-700"
                  >
                    {item.short_description}
                  </label>
                </div>
              ))}
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose>
            <Button dataTestId="cancel-button" variant="outline">
              {t('cancel')}
            </Button>
          </DialogClose>

          <Button
            dataTestId="generate-button"
            onClick={handleGenerateRecommendations}
            disabled={isGenerating || selectedConcerns.length === 0}
          >
            {isGenerating ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <SparklesIcon className="h-5 w-5 mr-1" />
            )}
            {t('generate')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
