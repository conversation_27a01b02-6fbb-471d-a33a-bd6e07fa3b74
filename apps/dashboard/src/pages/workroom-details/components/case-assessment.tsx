/*eslint-disable max-lines*/
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Badge,
  Button,
  RankLevel,
  Dialog,
  DialogContent,
  DialogTitle,
  Label,
  RadioGroup,
  RadioGroupItem,
  RiskBadge,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  StateBadge,
  Textarea,
  riskScoreCategoryToClass,
} from '@libs/ui'
import type { ExternalCase } from 'prime-front-service-client'
import { t } from 'i18next'
import React, { useCallback, useState } from 'react'
import {
  useUpdateCaseStatus,
  useUpdateRiskScoreCategory,
} from '../../../api/use-cases-api'
import { toast } from 'sonner'
import { CircleXIcon, Loader2, RefreshCw, Undo2 } from 'lucide-react'
import { CaseStatus } from 'prime-front-service-client/src/models/CaseStatus'
import { RiskScoreCategory } from 'prime-front-service-client/src/models/RiskScoreCategory'
import { cn } from '@libs/common'

interface CaseAssessmentProps {
  collapsed?: boolean
  caseInfo: ExternalCase
  refetch: () => void
  summary: boolean
  reopenCase: () => void
  isReopenLoading: boolean
}

export const CaseAssessment = ({
  caseInfo,
  refetch,
  summary,
  reopenCase,
  isReopenLoading,
}: CaseAssessmentProps) => {
  const { risk_score_category } = caseInfo.issue_analysis

  const [open, setOpen] = useState(false)
  const [radioValue, setRadioValue] = useState('')
  const [dismissReason, setDismissReason] = useState('')

  const mutation = useUpdateRiskScoreCategory()
  const { mutate: updateCaseStatusMutation } = useUpdateCaseStatus()

  const shortAssessment = useCallback(() => {
    return caseInfo?.issue_analysis?.short_assessment
  }, [caseInfo])

  const longAssessment = useCallback(() => {
    return caseInfo?.issue_analysis?.long_assessment
  }, [caseInfo])

  const reclassifyChange = async (value: RiskScoreCategory) => {
    if (value === RiskScoreCategory.None) {
      setOpen(true)
    } else {
      await handleUpdateRiskScoreCategory(value as RiskScoreCategory)
    }
  }

  const dismissCase = async () => {
    await handleUpdateStatus()
    setOpen(false)
  }

  const handleUpdateRiskScoreCategory = async (value: RiskScoreCategory) => {
    mutation.mutate(
      {
        source_id: caseInfo.source_id,
        issue_id: caseInfo.issue_id,
        risk_score_category: value as RiskScoreCategory,
      },
      {
        onSuccess: () => {
          toast.success('Risk score category updated successfully')
          refetch()
        },
        onError: () => {
          toast.error('Failed to update risk score category')
        },
      }
    )
  }

  const handleUpdateStatus = async () => {
    updateCaseStatusMutation(
      {
        source_id: caseInfo.source_id,
        issue_id: caseInfo.issue_id,
        status: CaseStatus.dismissed,
        dismissed_reason: dismissReason,
      },
      {
        onSuccess: () => {
          toast.success(t('riskScoreCategoryUpdatedSuccessfully'))
          refetch()
        },
        onError: () => {
          toast.error(t('errors.failedToUpdateRiskScoreCategory'))
        },
      }
    )
  }

  const handleRadioChange = (value: string) => {
    setRadioValue(value)
    if (value !== 'other') {
      setDismissReason(value)
    } else {
      setDismissReason('')
    }
  }

  return (
    <div className="px-9">
      <div className="relative p-3 text-card-foreground rounded-md border border-rose-300 ring-rose-300  font-light">
        <div className="absolute top-[-1.3rem] left-1 right-2 flex justify-between items-center">
          <div className="relative z-50  p-2 flex items-center gap-2 capitalize ">
            <div className="flex items-center bg-white  gap-2">
              <Select
                disabled={caseInfo.status === CaseStatus.done}
                onValueChange={async (value) => {
                  await reclassifyChange(value as RiskScoreCategory)
                }}
                value={risk_score_category}
              >
                <SelectTrigger
                  className={cn(
                    'flex items-center h-8 w-28',
                    risk_score_category === 'intervene'
                      ? riskScoreCategoryToClass[RiskScoreCategory.intervene]
                      : risk_score_category === 'analyze'
                      ? riskScoreCategoryToClass[RiskScoreCategory.analyze]
                      : riskScoreCategoryToClass[RiskScoreCategory.monitor]
                  )}
                >
                  {mutation.isPending ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <div>
                      {(risk_score_category as RiskScoreCategory) ||
                        t('workroomPage.reclassify')}
                    </div>
                  )}
                </SelectTrigger>
                <SelectContent className="capitalize w-[180px]">
                  <SelectItem value={RiskScoreCategory.intervene}>
                    <div
                      className={cn(
                        'flex items-center justify-around h-8 w-24',
                        riskScoreCategoryToClass[RiskScoreCategory.intervene]
                      )}
                    >
                      {RiskScoreCategory.intervene}
                    </div>
                  </SelectItem>
                  <SelectItem
                    className="flex"
                    value={RiskScoreCategory.analyze}
                  >
                    <div
                      className={cn(
                        'flex items-center justify-around h-8 w-24',
                        riskScoreCategoryToClass[RiskScoreCategory.analyze]
                      )}
                    >
                      {RiskScoreCategory.analyze}
                    </div>
                  </SelectItem>
                  <SelectItem value={RiskScoreCategory.monitor}>
                    <div
                      className={cn(
                        'flex items-center justify-around h-8 w-24',
                        riskScoreCategoryToClass[RiskScoreCategory.monitor]
                      )}
                    >
                      {RiskScoreCategory.monitor}
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {caseInfo?.issue_analysis?.confidence_level && (
              <Badge className="flex items-center gap-1 h-8 bg-white text-sm text-gray-800 rounded-md border-gray-300 shadow-none font-light hover:bg-white ml-2 py-1">
                {t('confidence')} -
                <RankLevel level={caseInfo?.issue_analysis?.confidence_level} />
              </Badge>
            )}
            {caseInfo.status === CaseStatus.dismissed ? (
              <Button
                variant="outline"
                onClick={() => reopenCase()}
                disabled={isReopenLoading}
                dataTestId="reopen-button"
                className="ml-2 flex items-center gap-2 capitalize"
              >
                {isReopenLoading && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                <Undo2 className="h-4 w-4" />
                {t('reopen')}
              </Button>
            ) : (
              <Button
                variant="outline"
                disabled={caseInfo.status === CaseStatus.done}
                dataTestId="dismiss-button"
                className="ml-2 flex items-center gap-2 capitalize"
                onClick={() => setOpen(true)}
              >
                <CircleXIcon size={18} />
                {t('dismiss')}
              </Button>
            )}
          </div>
          <div className="flex items-center bg-white gap-2">
            {caseInfo.status && <StateBadge state={caseInfo.status} />}
            {caseInfo.status === CaseStatus.done && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="outline"
                    dataTestId="reopen-case-button"
                    className="flex gap-1 capitalize h-7"
                  >
                    <RefreshCw size={10} />
                    {t('reopenCase')}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>{t('reopenConfirm')}</AlertDialogTitle>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                    <AlertDialogAction onClick={() => reopenCase()}>
                      <span className="capitalize">{t('reopenCase')}</span>
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        </div>
        <div className="mt-6 ml-4">
          <h3 className="text-md mb-3 text-gray-500 font-medium capitalize">
            {t('riskInCategories')}
          </h3>
          <div className="grid grid-cols-5 gap-2 items-center">
            {caseInfo.issue_analysis?.risk_factors.confidentiality_level && (
              <RiskBadge
                category="confidentiality"
                risk={
                  caseInfo.issue_analysis?.risk_factors.confidentiality_level
                }
              />
            )}
            {caseInfo.issue_analysis?.risk_factors.integrity_level && (
              <RiskBadge
                category="integrity"
                risk={caseInfo.issue_analysis?.risk_factors.integrity_level}
              />
            )}
            {caseInfo.issue_analysis?.risk_factors.availability_level && (
              <RiskBadge
                category="availability"
                risk={caseInfo.issue_analysis?.risk_factors.availability_level}
              />
            )}
          </div>
          {summary
            ? shortAssessment() && (
                <div className="mb-6 mt-6">
                  <h1 className="mb-3 text-md text-gray-500 font-medium capitalize">
                    {t('workroomPage.assessmentTitle')}
                  </h1>
                  <div>{caseInfo.issue_analysis?.short_assessment}</div>
                </div>
              )
            : longAssessment() && (
                <div className="mb-6 mt-8">
                  <h1 className="mb-3 text-md text-gray-500 font-medium capitalize">
                    {t('workroomPage.assessmentTitle')}
                  </h1>
                  <div>{caseInfo.issue_analysis?.long_assessment}</div>
                </div>
              )}
        </div>

        <Dialog
          open={open}
          onOpenChange={(isOpen) => {
            setOpen(isOpen)
            if (!isOpen) {
              setDismissReason('')
              setRadioValue('')
            }
          }}
        >
          <DialogContent className="sm:max-w-[425px] font-medium text-sm">
            <DialogTitle>{t('whyWasItDismissed')}</DialogTitle>
            <RadioGroup value={radioValue} onValueChange={handleRadioChange}>
              <div className="flex items-center space-x-2 my-3">
                <RadioGroupItem
                  value="Known issue/Already addressed"
                  id="known-issue"
                />
                <Label htmlFor="known-issue">{t('knownIssue')}</Label>
              </div>
              <div className="flex items-center space-x-2 mb-3">
                <RadioGroupItem
                  value="No security implications"
                  id="no-security-implications"
                />
                <Label htmlFor="no-security-implications">
                  {t('noSecurityImplications')}
                </Label>
              </div>
              <div className="flex items-center space-x-2 mb-3">
                <RadioGroupItem
                  value="Opened by Security - Ignore"
                  id="opened-by-security"
                />
                <Label htmlFor="opened-by-security">
                  {t('openedBySecurity')}
                </Label>
              </div>
              <div className="flex items-start space-x-2 mb-3">
                <RadioGroupItem value="other" id="other" />
                <Label htmlFor="other">
                  Other
                  {radioValue === 'other' && (
                    <Textarea
                      className="my-4 p-4 w-full"
                      id="other-dismiss-reason"
                      placeholder={t('type')}
                      onChange={(e) => setDismissReason(e.target.value)}
                      disabled={radioValue !== 'other'}
                    />
                  )}
                </Label>
              </div>
            </RadioGroup>
            <Button
              onClick={dismissCase}
              disabled={!dismissReason}
              dataTestId="submit-dismiss-button"
            >
              {t('submit')}
            </Button>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
