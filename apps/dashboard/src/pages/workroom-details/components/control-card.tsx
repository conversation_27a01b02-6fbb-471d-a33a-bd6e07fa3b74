import type { ExternalControl } from 'prime-front-service-client'
import type { PrimeRecommendation } from 'prime-front-service-client/src/models/PrimeRecommendation'

interface ControlCardProps {
  control: PrimeRecommendation | ExternalControl
}
export const ControlCard = ({ control }: ControlCardProps) => {
  return (
    <div data-testid="control-form" className="mb-6">
      <div className="flex items-center text-md text-gray-500 font-medium ">
        {control.name}
      </div>
      {!!control.description && (
        <div className="flex items-center text-md text-gray-500 font-medium mt-2 ml-2">
          - {control.description}
        </div>
      )}
      <div className="ml-4">
        {control.implementations
          .filter((recommendation) => recommendation.status === 'approved')
          .map((recommendation) => (
            <div key={recommendation.id} className="flex items-center mt-3">
              {recommendation.recommendation}
            </div>
          ))}
      </div>
    </div>
  )
}
