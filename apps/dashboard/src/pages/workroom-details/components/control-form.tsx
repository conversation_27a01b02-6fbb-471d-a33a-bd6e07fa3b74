import type {
  ExternalControl,
  ExternalImplementation,
} from 'prime-front-service-client'
import {
  Checkbox,
  Collapsible,
  CollapsibleContent,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@libs/ui'
import React, { useState, useEffect, useMemo } from 'react'
import { ChevronUp } from 'lucide-react'
import { t } from 'i18next'
import { RecommendationsForm } from './recommendations-form'
import { CustomRecommendationSelector } from './custom-recommendation-selector'
import type { PrimeRecommendation } from 'prime-front-service-client/src/models/PrimeRecommendation'

interface ControlFormProps {
  control: PrimeRecommendation | ExternalControl
  concern_id: number
  selectedRecommendations: number[]
  showSnippet: boolean
  refetch: () => void
  onSelectionChange: (selectedRecommendations: number[]) => void
}

export const ControlForm = ({
  control,
  concern_id,
  selectedRecommendations,
  showSnippet,
  refetch,
  onSelectionChange,
}: ControlFormProps) => {
  const getInitialCheckedState = () => {
    const { implementations } = control
    if (
      implementations.every(
        (rec: ExternalImplementation) => rec.status === 'unknown'
      )
    ) {
      return true
    }
    return (
      implementations.some(
        (rec: ExternalImplementation) => rec.status === 'approved'
      ) ||
      implementations.every(
        (rec: ExternalImplementation) => rec.status !== 'dismissed'
      )
    )
  }

  const [collapsedControls, setCollapsedControls] = useState<
    Record<string, boolean>
  >({})

  const [checked, setChecked] = useState<boolean>(getInitialCheckedState())

  useEffect(() => {
    setChecked(
      control.implementations.every((recommendation: ExternalImplementation) =>
        selectedRecommendations.includes(recommendation.id)
      )
    )
  }, [selectedRecommendations, control.implementations])

  const handleControlCheckboxChange = () => {
    const newChecked = !checked
    setChecked(newChecked)
    if (newChecked) {
      const allRecommendations = control.implementations.map(
        (rec: ExternalImplementation) => rec.id
      )
      onSelectionChange(allRecommendations)
    } else {
      onSelectionChange([])
    }
  }

  const controlIdsTooltip = useMemo(() => {
    return 'control_names' in control
      ? control['control_names']
          ?.slice(2, control['control_names'].length)
          ?.join(', ')
      : ''
  }, [control])

  const handleToggleCollapse = (controlId: string) => {
    setCollapsedControls((prev) => ({
      ...prev,
      [controlId]: !prev[controlId],
    }))
  }

  return (
    <div data-testid="control-form" className="mt-6 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-start text-md text-slate-700 font-medium mb-2">
          <Checkbox
            checked={checked}
            className="mt-0.5 mr-2 shadow-none bg-gray-200 font-light text-gray-800 border-none data-[state=checked]:bg-gray-200 data-[state=checked]:text-gray-800"
            onClick={handleControlCheckboxChange}
          />
          {control.name}
        </div>
        {!showSnippet &&
          'control_names' in control &&
          control['control_names'] && (
            <div className="flex items-center text-sm font-light gap-1 text-slate-400">
              <div className="font-medium capitalize">{t('controls')}:</div>
              <div className="flex items-center gap-1">
                <span>{control['control_names']?.slice(0, 2)?.join(', ')}</span>
                {control['control_names']?.length > 2 && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="text-slate-500 border border-slate-500 rounded-full px-2">
                        {`+${control['control_names']?.length - 2} `}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <div className="w-[150px]">
                        <div className="mb-2 capitalize">{t('controls')}:</div>
                        <div>{controlIdsTooltip}</div>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                )}
              </div>
            </div>
          )}
      </div>

      <div className="mb-4 ml-4 mt-2 font-light text-sm text-slate-500">
        {control.description}
      </div>
      <div className="ml-4">
        {showSnippet ? (
          <Collapsible
            className="border border-slate-200 rounded-lg py-4 px-10 bg-gray-50 "
            key={control.id}
            open={collapsedControls[control.id]}
          >
            <div
              onClick={() => handleToggleCollapse(control.id)}
              className="flex items-center cursor-pointer"
            >
              <h1 className="font-medium">{t('implementationGuidance')}</h1>
              <div
                className={`text-sm rounded-2xl shadow-none bg-transparent text-gray-700 font-light capitalize
                hover:bg-transparent transition-transform duration-300 ease-in-out ${
                  collapsedControls[control.id] ? 'rotate-0' : 'rotate-90'
                }`}
              >
                <ChevronUp />
              </div>
            </div>

            <CollapsibleContent>
              <RecommendationsForm
                recommendations={control.implementations}
                selectedRecommendations={selectedRecommendations}
                showSnippet={showSnippet}
                onSelectionChange={onSelectionChange}
              />
            </CollapsibleContent>
          </Collapsible>
        ) : (
          <RecommendationsForm
            recommendations={control.implementations}
            selectedRecommendations={selectedRecommendations}
            showSnippet={showSnippet}
            onSelectionChange={onSelectionChange}
          />
        )}
      </div>
      <div className="mt-6 ml-4">
        <CustomRecommendationSelector
          control={control}
          concern_id={concern_id}
          selectedRecommendations={selectedRecommendations}
          refetch={refetch}
        />
      </div>
    </div>
  )
}
