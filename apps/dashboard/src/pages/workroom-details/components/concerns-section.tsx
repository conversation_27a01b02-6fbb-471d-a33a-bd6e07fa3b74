import { useState } from 'react'
import type {
  ExternalControl,
  CaseStatus,
  ImplementationStatusUpdate,
} from 'prime-front-service-client'
import { SecurityFramework as SecurityFrameworkInput } from 'prime-front-service-client'
import {
  Button,
  Collapsible,
  CollapsibleContent,
  LinddunIcon,
  MitreIcon,
  Separator,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@libs/ui'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { DoneCaseConcerns } from './done-case-concerns'
import { cn } from '@libs/common'
import { ControlForm } from './control-form'
import { t } from 'i18next'
import { Link } from 'react-router'
import { useGetConfig } from '../../../api/use-config-api'

import { ReviewConcernsDialog } from './review-concerns-dialog'
import { useAtomValue } from 'jotai'
import { selectedConcernTypeAtom } from '../hooks'
import { ConcernsToolbar } from './concerns-toolbar'
import type { ExternalPrimeConcern } from 'prime-front-service-client/src/models/ExternalPrimeConcern'
import type { ExternalFrameworkConcern } from 'prime-front-service-client/src/models/ExternalFrameworkConcern'
import type { PrimeRecommendation } from 'prime-front-service-client/src/models/PrimeRecommendation'
import { ConcernType } from 'prime-front-service-client/src/models/ConcernType'
import { GenerateRecommendationsDialog } from './generate-recommendations-dialog'
import { useConcerns } from '../hooks/use-concerns'

interface ConcernsSectionProps {
  caseDone: boolean
  frameworkConcerns: { [key: string]: Array<ExternalFrameworkConcern> }
  primeConcerns: ExternalPrimeConcern[]
  summary: boolean
  onUpdateRecommendations: (
    implementations: ImplementationStatusUpdate[],
    customUpdate: boolean
  ) => void
  refetch: () => void
  updateCaseStatus: (status: CaseStatus) => Promise<void>
}

export const ConcernsSection = ({
  caseDone,
  frameworkConcerns,
  primeConcerns,
  summary,
  onUpdateRecommendations,
  refetch,
  updateCaseStatus,
}: ConcernsSectionProps) => {
  const { data: config } = useGetConfig()
  const [openConfirm, setOpenConfirm] = useState(false)
  const selectedConcernType = useAtomValue(selectedConcernTypeAtom)

  const {
    filteredConcerns,
    collapsedConcerns,
    showSnippet,
    concernsByFramework,
    selectedRecommendations,
    doneConcerns,
    getConcernByType,
    handleControlSelectionChange,
    handleToggleCollapse,
    getConcernSelectedRecs,
    getConcernFilteredRecs,
    getConcernTotalRecommendations,
  } = useConcerns({
    frameworkConcerns,
    primeConcerns,
    onUpdateRecommendations,
  })

  return (
    <div className="concerns-wrapper mt-6 flex flex-col">
      <ConcernsToolbar
        caseDone={caseDone}
        concerns={concernsByFramework}
        showSelectFramework={
          !!frameworkConcerns[
            config?.security_framework ?? SecurityFrameworkInput.NIST
          ]?.length
        }
        refetch={refetch}
      />
      {!caseDone ? (
        <div className="concerns-section flex-1 flex flex-col">
          <div className="concern-items px-9 mb-10 flex-1">
            {filteredConcerns.map((concern) => (
              <div className="concern-item" key={concern.id}>
                <Collapsible
                  open={collapsedConcerns[concern.id]}
                  className="p-4 my-3 rounded-lg bg-white border border-gray-200"
                >
                  <div
                    onClick={() => handleToggleCollapse(concern.id)}
                    className="flex justify-between items-center gap-3 cursor-pointer"
                  >
                    <div>
                      <h1
                        data-testid="concern-header"
                        className={cn('font-medium')}
                      >
                        {concern.short_description}
                      </h1>
                      <div className="text-xs text-gray-400 mt-2 font-light">
                        {concern.long_description}
                      </div>
                      <div className="flex items-center gap-2 text-xs font-medium text-gray-500 mt-2 h-4">
                        <span>
                          {concern.methodology.type === ConcernType.Mitre ? (
                            <MitreIcon className="h-4 w-24" />
                          ) : (
                            <LinddunIcon className="h-3 w-20" />
                          )}
                        </span>
                        <Separator orientation="vertical" />
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="underline">
                              {concern.methodology.category}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="w-[150px]">
                              {t(
                                `${selectedConcernType}.${concern.methodology.category}`
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </div>
                    {!!getConcernByType(concern) &&
                      !!getConcernByType(concern).length && (
                        <div className="flex items-center gap-3 ml-6">
                          <div>
                            <div className="flex items-center justify-center text-slate-500 font-light mb-2 capitalize">
                              {t('recommendations')}
                            </div>
                            <div className="h-9 px-4 py-2 text-sm capitalize text-gray-800 font-light hover:bg-white ml-4 flex items-center gap-4">
                              <div className="flex-col items-center text-center text-slate-700 font-semibold">
                                {getConcernTotalRecommendations(concern.id)}
                                <div className="text-gray-400 text-sm font-light">
                                  {t('total')}
                                </div>
                              </div>
                              <div className="flex-col items-center text-center text-slate-700 font-semibold">
                                {getConcernSelectedRecs(concern.id)}
                                <div className="text-gray-400 text-sm font-light">
                                  {t('selected')}
                                </div>
                              </div>
                              <div className="flex-col items-center text-center text-slate-700 font-semibold">
                                {getConcernFilteredRecs(concern.id)}
                                <div className="text-gray-400 text-sm font-light">
                                  {t('filtered')}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="text-sm rounded-2xl shadow-none bg-transparent text-gray-700 font-light capitalize hover:bg-transparent">
                            {collapsedConcerns[concern.id] ? (
                              <div className="flex items-center gap-1">
                                <ChevronUp />
                              </div>
                            ) : (
                              <div className="flex items-center gap-1">
                                <ChevronDown />
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                  </div>
                  <CollapsibleContent>
                    {getConcernByType(concern).map(
                      (control: PrimeRecommendation | ExternalControl) => {
                        if (!control) return null
                        return (
                          <ControlForm
                            key={control.id}
                            showSnippet={showSnippet}
                            control={control}
                            concern_id={concern.id}
                            selectedRecommendations={
                              selectedRecommendations[concern.id]?.[
                                control.id
                              ] || []
                            }
                            onSelectionChange={(selected) =>
                              handleControlSelectionChange(
                                concern.id,
                                control.id,
                                selected
                              )
                            }
                            refetch={refetch}
                          />
                        )
                      }
                    )}
                  </CollapsibleContent>
                </Collapsible>
              </div>
            ))}
          </div>
          <div className="concerns-footer flex items-center justify-between sticky bottom-0 bg-white px-9 py-5 shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1),0_-2px_4px_-1px_rgba(0,0,0,0.06)]">
            <GenerateRecommendationsDialog
              selectedConcernType={selectedConcernType}
              concernsByFramework={concernsByFramework}
            />
            <ReviewConcernsDialog
              summary={summary}
              openConfirm={openConfirm}
              setOpenConfirm={setOpenConfirm}
              refetch={refetch}
              reviewedConcerns={doneConcerns}
              updateCaseStatus={updateCaseStatus}
            />
          </div>
        </div>
      ) : (
        <div>
          {caseDone ? (
            <div className="px-9">
              <DoneCaseConcerns concerns={doneConcerns} summary={summary} />
            </div>
          ) : (
            <div className="flex-col justify-center text-center mt-12">
              <div>{t('emptyRecommendationsMessage')}</div>
              <div>{t('changeFrameworkMessage')}</div>
              <Link to="/settings/security-frameworks">
                <Button
                  variant="outline"
                  className="capitalize mt-4"
                  dataTestId="change-framework-button"
                >
                  {t('changeFramework')}
                </Button>
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
