import type {
  ExternalControl,
  ExternalImplementation,
} from 'prime-front-service-client'
import { Separator } from '@libs/ui'
import { ControlCard } from './control-card'
import { useTranslation } from 'react-i18next'
import type { ExternalPrimeConcern } from 'prime-front-service-client/src/models/ExternalPrimeConcern'
import type { ExternalFrameworkConcern } from 'prime-front-service-client/src/models/ExternalFrameworkConcern'
import { useAtom } from 'jotai/index'
import type { SecurityFramework as SecurityFrameworkInput } from 'prime-front-service-client'
import { selectedFrameworkAtom } from '../hooks'
import type { PrimeRecommendation } from 'prime-front-service-client/src/models/PrimeRecommendation'
import { getConcernByType } from './recommendations-util'

interface DoneCaseConcernProps {
  summary: boolean
  concerns: (ExternalPrimeConcern | ExternalFrameworkConcern)[]
}
export const DoneCaseConcerns = ({
  concerns,
  summary,
}: DoneCaseConcernProps) => {
  const { t } = useTranslation()
  const [selectedFramework] = useAtom<SecurityFrameworkInput>(
    selectedFrameworkAtom
  )

  return (
    <div>
      {concerns ? (
        concerns.map((concern) => (
          <div key={concern.id}>
            <h1 className="text-md text-gray-700 font-semibold">
              {summary ? concern.short_description : concern.long_description}
            </h1>
            <div className="mt-6">
              {getConcernByType(concern, selectedFramework) &&
                getConcernByType(concern, selectedFramework)
                  ?.filter((control: PrimeRecommendation | ExternalControl) =>
                    control.implementations.some(
                      (rec: ExternalImplementation) => rec.status === 'approved'
                    )
                  )
                  .map(
                    (control: PrimeRecommendation | ExternalControl) =>
                      control && (
                        <ControlCard key={control.id} control={control} />
                      )
                  )}
            </div>
            <Separator className="mb-6" />
          </div>
        ))
      ) : (
        <h1 className="text-md capitalize">{t('noApprovedConcerns')}</h1>
      )}
    </div>
  )
}
