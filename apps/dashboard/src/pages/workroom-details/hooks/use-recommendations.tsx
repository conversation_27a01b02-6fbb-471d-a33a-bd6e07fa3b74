import { useState } from 'react'
import { atom, useAtom } from 'jotai'
import {
  ImplementationStatus,
  SecurityFramework as SecurityFrameworkInput,
} from 'prime-front-service-client'
import type {
  ExternalControl,
  ExternalImplementation,
} from 'prime-front-service-client'
import { produce } from 'immer'
import type { ExternalPrimeConcern } from 'prime-front-service-client/src/models/ExternalPrimeConcern'
import type { ExternalFrameworkConcern } from 'prime-front-service-client/src/models/ExternalFrameworkConcern'
import type { PrimeRecommendation } from 'prime-front-service-client/src/models/PrimeRecommendation'
import { ConcernType } from 'prime-front-service-client/src/models/ConcernType'
import { getConcernByType } from '../components/recommendations-util'

export const selectedFrameworkAtom = atom<SecurityFrameworkInput>(
  SecurityFrameworkInput.PRIME
)

export const selectedRaciAtom = atom<string[]>([])
export const selectedConcernTypeAtom = atom<ConcernType>(ConcernType.Mitre)
export const useRecommendations = () => {
  const [selectedFramework] = useAtom(selectedFrameworkAtom)
  const [selectedRecommendations, setSelectedRecommendations] = useState<
    Record<number, Record<string, number[]>>
  >({})

  const setInitialSelections = (
    concerns: (ExternalPrimeConcern | ExternalFrameworkConcern)[]
  ) => {
    const initialSelections: Record<number, Record<string, number[]>> = {}
    concerns.forEach((concern) => {
      if (getConcernByType(concern, selectedFramework)) {
        initialSelections[concern.id] = {}
        getConcernByType(concern, selectedFramework).forEach(
          (control: PrimeRecommendation | ExternalControl) => {
            initialSelections[concern.id][control.id] = control.implementations
              .filter(
                (rec: ExternalImplementation) => rec.status === 'approved'
              )
              .map((rec: ExternalImplementation) => rec.id)
          }
        )
      }
    })
    setSelectedRecommendations(initialSelections)
  }

  const mappedRecommendations = (
    implementations: number[],
    control: PrimeRecommendation | ExternalControl,
    concernId: number
  ) => {
    return control.implementations.map((rec) => ({
      id: rec.id,
      status: implementations.includes(rec.id)
        ? ImplementationStatus.approved
        : ImplementationStatus.dismissed,
      ...(rec.raci?.includes('custom') && {
        control_id: control.id,
        concern_id: concernId,
      }),
    }))
  }

  const updateSelected = (
    recommendations: number[],
    controlId: string,
    concernId: number
  ) => {
    setSelectedRecommendations((prev) =>
      produce(prev, (draft) => {
        if (!draft[concernId]) {
          draft[concernId] = {}
        }
        if (!draft[concernId][controlId]) {
          draft[concernId][controlId] = []
        }
        draft[concernId][controlId] = recommendations
      })
    )
  }

  const concernSelectedRecommendations = (
    concern: ExternalPrimeConcern | ExternalFrameworkConcern
  ) => {
    return getConcernByType(concern, selectedFramework)
      ? getConcernByType(concern, selectedFramework)
          .flatMap(
            (control: PrimeRecommendation | ExternalControl) =>
              control['implementations']
          )
          .filter((recommendation: ExternalImplementation) =>
            selectedRecommendations[recommendation.concern_id]?.[
              recommendation.control_id
            ]?.includes(recommendation.id)
          ).length
      : 0
  }

  const primeRaciFilters = (concerns: ExternalPrimeConcern[]) => {
    return Array.from(
      new Set<string>(
        concerns.flatMap((concern) =>
          concern.recommendations.flatMap((control) =>
            control.implementations.flatMap(
              (implementation) => implementation.raci || []
            )
          )
        )
      )
    ).sort((a, b) => {
      if (a === 'Dev') return -1
      if (b === 'Dev') return 1
      return 0
    })
  }

  const frameworkRaciFilters = (concerns: ExternalFrameworkConcern[]) => {
    return Array.from(
      new Set<string>(
        concerns.flatMap((concern) =>
          concern.controls.flatMap((control) =>
            control.implementations.flatMap(
              (implementation) => implementation.raci || []
            )
          )
        )
      )
    ).sort((a, b) => {
      if (a === 'Dev') return -1
      if (b === 'Dev') return 1
      return 0
    })
  }

  return {
    selectedRecommendations,
    setInitialSelections,
    mappedRecommendations,
    updateSelected,
    concernSelectedRecommendations,
    primeRaciFilters,
    frameworkRaciFilters,
  }
}
