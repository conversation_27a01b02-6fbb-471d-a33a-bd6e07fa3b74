import { useCallback, useEffect, useMemo, useState } from 'react'
import { use<PERSON>tom, useAtomValue } from 'jotai'
import type {
  ExternalControl,
  ImplementationStatusUpdate,
} from 'prime-front-service-client'
import { SecurityFramework as SecurityFrameworkInput } from 'prime-front-service-client'
import type { ExternalPrimeConcern } from 'prime-front-service-client/src/models/ExternalPrimeConcern'
import type { ExternalFrameworkConcern } from 'prime-front-service-client/src/models/ExternalFrameworkConcern'
import type { PrimeRecommendation } from 'prime-front-service-client/src/models/PrimeRecommendation'
import {
  selectedConcernType<PERSON>tom,
  selected<PERSON>ramework<PERSON>tom,
  selectedRaci<PERSON>tom,
  useRecommendations,
} from './use-recommendations'
import {
  concernFilteredRecommendations,
  concernTotalRecommendations,
  filterByRaci,
} from '../components/recommendations-util'
import { useGetConfig } from '../../../api/use-config-api'

interface UseConcernsProps {
  frameworkConcerns: { [key: string]: Array<ExternalFrameworkConcern> }
  primeConcerns: ExternalPrimeConcern[]
  onUpdateRecommendations: (
    implementations: ImplementationStatusUpdate[],
    customUpdate: boolean
  ) => void
}

export const useConcerns = ({
  frameworkConcerns,
  primeConcerns,
  onUpdateRecommendations,
}: UseConcernsProps) => {
  const { data: config } = useGetConfig()
  const {
    selectedRecommendations,
    setInitialSelections,
    mappedRecommendations,
    updateSelected,
    concernSelectedRecommendations,
  } = useRecommendations()

  const [selectedFramework, setSelectedFramework] = useAtom(
    selectedFrameworkAtom
  )
  const selectedConcernType = useAtomValue(selectedConcernTypeAtom)
  const [selectedRaci] = useAtom(selectedRaciAtom)

  const [collapsedConcerns, setCollapsedConcerns] = useState<
    Record<number, boolean>
  >({})
  const [filteredConcerns, setFilteredConcerns] =
    useState<(ExternalPrimeConcern | ExternalFrameworkConcern)[]>(primeConcerns)
  const [showSnippet, setShowSnippet] = useState<boolean>(
    selectedFramework === SecurityFrameworkInput.PRIME
  )

  const concernsByFramework = useMemo(() => {
    if (!selectedFramework) {
      return []
    }

    if (selectedFramework === SecurityFrameworkInput.PRIME) {
      return primeConcerns.filter(
        (concern) => concern.methodology.type === selectedConcernType
      )
    } else {
      return frameworkConcerns[selectedFramework].filter(
        (concern) => concern.methodology.type === selectedConcernType
      )
    }
  }, [primeConcerns, frameworkConcerns, selectedFramework, selectedConcernType])

  const getConcernByType = useCallback(
    (concern: ExternalPrimeConcern | ExternalFrameworkConcern) => {
      if (
        selectedFramework === SecurityFrameworkInput.PRIME &&
        'recommendations' in concern
      ) {
        return concern.recommendations as PrimeRecommendation[]
      }

      if ('controls' in concern) {
        return concern.controls as ExternalControl[]
      }

      return []
    },
    [selectedFramework]
  )

  const hasControls = useMemo(() => {
    return concernsByFramework.some(
      (concern) => getConcernByType(concern) && getConcernByType(concern).length
    )
  }, [concernsByFramework, getConcernByType])

  const doneConcerns = useMemo(() => {
    return concernsByFramework.filter((concern) => {
      const controlsOrRecommendations =
        (concern as ExternalFrameworkConcern).controls ??
        (concern as ExternalPrimeConcern).recommendations

      return controlsOrRecommendations?.some((control) =>
        control.implementations.some((rec) => rec.status === 'approved')
      )
    })
  }, [frameworkConcerns, primeConcerns])

  const handleControlSelectionChange = (
    concernId: number,
    controlId: string,
    implementations: number[]
  ) => {
    const concern = filteredConcerns.find((c) => c.id === concernId)
    if (!concern) return

    const controlsOrRecommendations =
      (concern as ExternalFrameworkConcern).controls ??
      (concern as ExternalPrimeConcern).recommendations

    const control = controlsOrRecommendations?.find(
      (ctrl) => ctrl.id === controlId
    )
    if (!control) return

    const updatedRecommendations: ImplementationStatusUpdate[] =
      mappedRecommendations(implementations, control, concernId)

    const hasCustomRecommendations = updatedRecommendations.some(
      (rec) => rec.control_id && rec.concern_id
    )

    onUpdateRecommendations(updatedRecommendations, hasCustomRecommendations)
    updateSelected(implementations, controlId, concernId)
  }

  const handleToggleCollapse = (concernId: number) => {
    if (hasControls) {
      setCollapsedConcerns((prev) => ({
        ...prev,
        [concernId]: !prev[concernId],
      }))
    }
  }

  const getConcernSelectedRecs = useCallback(
    (concernId: number) => {
      const concern = filteredConcerns.find(
        (concern) => concern.id === concernId
      )
      return concernSelectedRecommendations(
        concern as ExternalPrimeConcern | ExternalFrameworkConcern
      )
    },
    [selectedRecommendations, filteredConcerns, selectedFramework]
  )

  const getConcernFilteredRecs = useCallback(
    (concernId: number) => {
      const concern = filteredConcerns.find(
        (concern) => concern.id === concernId
      )
      return concernFilteredRecommendations(
        concern as ExternalPrimeConcern | ExternalFrameworkConcern,
        selectedFramework,
        selectedRaci
      )
    },
    [selectedRaci, filteredConcerns, selectedFramework]
  )

  const getConcernTotalRecommendations = useCallback(
    (concernId: number) => {
      const concern = concernsByFramework.find(
        (concern) => concern.id === concernId
      )
      return concernTotalRecommendations(
        concern as ExternalPrimeConcern | ExternalFrameworkConcern,
        selectedFramework
      )
    },
    [primeConcerns, frameworkConcerns, selectedFramework, selectedConcernType]
  )

  useEffect(() => {
    if (!concernsByFramework.length) {
      setSelectedFramework(config?.security_framework as SecurityFrameworkInput)
    }
  }, [frameworkConcerns, primeConcerns])

  useEffect(() => {
    if (selectedFramework === SecurityFrameworkInput.PRIME) {
      setShowSnippet(true)
    } else {
      setShowSnippet(false)
    }
    selectedRaci.length
      ? setFilteredConcerns(
          filterByRaci(concernsByFramework, selectedRaci) as (
            | ExternalFrameworkConcern
            | ExternalFrameworkConcern
          )[]
        )
      : setFilteredConcerns(concernsByFramework)
  }, [
    frameworkConcerns,
    primeConcerns,
    selectedRaci,
    selectedFramework,
    selectedConcernType,
  ])

  useEffect(() => {
    setInitialSelections(concernsByFramework)
  }, [frameworkConcerns, primeConcerns, selectedFramework])

  return {
    filteredConcerns,
    collapsedConcerns,
    showSnippet,
    concernsByFramework,
    selectedRecommendations,
    doneConcerns,
    getConcernByType,
    handleControlSelectionChange,
    handleToggleCollapse,
    getConcernSelectedRecs,
    getConcernFilteredRecs,
    getConcernTotalRecommendations,
  }
}
