import { t } from 'i18next'

export const primeFilterTitles = [
  'Issue id',
  'title',
  'Risk Score',
  'Status',
  'Confidence Level',
  'Risk Score Category',
  'Confidentiality Level',
  'Integrity Level',
  'Availability Level',
  'MITRE ATT&CK',
  'LINDDUN',
  'Is Automated',
  'Is Security Enhancement',
  'Labels',
  t('tableColumns.fire_summary'),
]

export enum ALLOWED_FILTERS {
  status = 'status',
  classification = 'classification',
  source_id = 'source_id',
  confidentiality = 'confidentiality',
  integrity = 'integrity',
  availability = 'availability',
  third_party_management = 'third_party_management',
  compliance = 'compliance',
  risk_score_category = 'risk_score_category',
  confidence_level = 'confidence_level',
  confidentiality_level = 'confidentiality_level',
  integrity_level = 'integrity_level',
  availability_level = 'availability_level',
  is_automated = 'is_automated',
  is_security_enhancement = 'is_security_enhancement',
  labels = 'labels',
  parent_id = 'parent_id',
  mitre_categories = 'mitre_categories',
  linddun_categories = 'linddun_categories',
  fire_summary = 'fire_summary',
  container = 'container',
}
