import { useEffect } from 'react'
import { PageTitle, useUrlState } from '@libs/ui'

import { t } from 'i18next'
import { DataTable, staticColumns } from './components'
import {
  defaultFilterParams,
  defaultSortParam,
  getParsedArrayOfObjects,
} from '../../router/router'
import { useAtomValue } from 'jotai'
import { defaultViewAtom } from '../../config/store'
import { RiskScoreCategory } from 'prime-front-service-client'
import { ViewsSelector } from './components/views-selector'
import { useSearchParams } from 'react-router'
import { serializeWorkroom } from '../../router/router-utils'

type RiskScoreCategoryType =
  (typeof RiskScoreCategory)[keyof typeof RiskScoreCategory]

export const WorkroomPage = () => {
  const [searchParams, setSearchParams] = useSearchParams()

  const [urlState, setUrlState] = useUrlState()

  const defaultView = useAtomValue(defaultViewAtom)

  useEffect(() => {
    if (searchParams.size === 0) {
      const serializedWorkroomSearch = serializeWorkroom({
        f: defaultView?.f
          ? getParsedArrayOfObjects(defaultView.f)
          : defaultFilterParams,
        s: defaultView?.s
          ? getParsedArrayOfObjects(defaultView.s)
          : [defaultSortParam],
        selected_columns: defaultView?.selected_columns || staticColumns,
      })
      setSearchParams(serializedWorkroomSearch, { replace: true })
      return
    }
  }, [
    defaultView?.f,
    defaultView?.s,
    defaultView?.selected_columns,
    searchParams.size,
    setSearchParams,
  ])

  useEffect(() => {
    const riskScoreCategory =
      urlState.risk_score_category as RiskScoreCategoryType | null

    if (
      riskScoreCategory &&
      Object.values(RiskScoreCategory).includes(riskScoreCategory)
    ) {
      const filter = [
        {
          field: 'risk_score_category',
          op: 'eq',
          value: riskScoreCategory,
        },
      ]

      const mergeState = {
        s: [defaultSortParam],
        f: filter,
        selected_columns: defaultView?.selected_columns || staticColumns,
      }

      const filtersQueryString = JSON.stringify(mergeState.f)
      const sortQueryString = JSON.stringify(mergeState.s)
      const selectedColumnsQueryString = JSON.stringify(
        mergeState.selected_columns
      )

      setUrlState({
        f: filtersQueryString,
        s: sortQueryString,
        selected_columns: selectedColumnsQueryString,
      })
    }
  }, [urlState, setUrlState, defaultView?.selected_columns])

  return (
    <div>
      <div className="workroom-page bg-white border shadow rounded-2xl m-4">
        <div className="flex items-center justify-between border-b px-6">
          <PageTitle title={t('workroom')} />
          <ViewsSelector />
        </div>
        <div className="pt-4 px-6">
          <DataTable />
        </div>
      </div>
    </div>
  )
}
