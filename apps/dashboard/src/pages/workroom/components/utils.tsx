import type { Header } from '@tanstack/react-table'
import { flexRender } from '@tanstack/react-table'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Button, TableHead } from '@libs/ui'
import type { ExternalCaseWorkroom } from 'prime-front-service-client'
import { GripVerticalIcon } from 'lucide-react'
import type { CSSProperties } from 'react'

export const DraggableTableHeader = ({
  header,
}: {
  header: Header<ExternalCaseWorkroom, unknown>
}) => {
  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useSortable({
      id: header.column.id,
    })

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: 'relative',
    transform: CSS.Translate.toString(transform),
    transition: 'width transform 0.2s ease-in-out',
    whiteSpace: 'nowrap',
    minWidth: header.column.getSize(),
    zIndex: isDragging ? 1 : 0,
  }

  return (
    <TableHead ref={setNodeRef} style={style} className="py-4 px-2 capitalize">
      <div className="flex gap-1 items-center font-bold">
        {header.isPlaceholder
          ? null
          : flexRender(header.column.columnDef.header, header.getContext())}

        {header.id !== 'select' && (
          <Button
            className="h-5 w-5 p-0.5 text-slate-600 active:bg-gray-200 hover:bg-gray-200 cursor-grab active:cursor-grabbing"
            variant="ghost"
            dataTestId="drag-handle"
            {...attributes}
            {...listeners}
          >
            <GripVerticalIcon className="h-4 w-4" />
          </Button>
        )}
      </div>
    </TableHead>
  )
}
