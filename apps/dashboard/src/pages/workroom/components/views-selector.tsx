import {
  Button,
  <PERSON>alog,
  <PERSON>alogClose,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectTrigger,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@libs/ui'
import { z } from 'zod'
import { usePresets } from '../hooks'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { CirclePlusIcon, Diamond, Loader2, Save } from 'lucide-react'
import { t } from 'i18next'
import { useEffect, useRef, useState } from 'react'
import { ViewActions } from './view-actions'
import { cn } from '@libs/common'
import { atom, useAtom } from 'jotai'

const formSchema = z.object({
  view: z.string().min(2, {
    message: 'View title must be at least 2 characters long',
  }),
})

export const editModePresetAtom = atom<string | null>(null)

export const ViewsSelector = () => {
  const {
    presets,
    currentView,
    currentPreset,
    updatePreset,
    isUpdatePresetPending,
    addPreset,
    switchView,
  } = usePresets()

  const [openAddViewDialog, setOpenAddViewDialog] = useState<boolean>(false)
  const [openViewSelector, setOpenViewSelector] = useState<boolean>(false)

  const textRef = useRef<HTMLSpanElement>(null)
  const [isOverflowing, setIsOverflowing] = useState(false)

  const displayText = currentPreset?.name || t('defaultView')

  const [editMode] = useAtom(editModePresetAtom)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      view: '',
    },
  })

  useEffect(() => {
    const el = textRef.current
    if (el) {
      setIsOverflowing(el.scrollWidth > el.clientWidth)
    }
  }, [displayText])

  function onSubmit(values: z.infer<typeof formSchema>) {
    addPreset(values.view)
    setOpenViewSelector(false)
    form.reset()
  }

  return (
    <div className="flex items-center gap-2">
      <div className="w-full flex items-center justify-between gap-2">
        <Select
          open={openViewSelector}
          onOpenChange={setOpenViewSelector}
          value={currentView || 'default-view'}
          onValueChange={switchView}
        >
          <SelectTrigger className="w-[180px]">
            <Tooltip>
              <TooltipTrigger
                data-testid="view-selector-trigger"
                className="truncate"
              >
                <span
                  ref={textRef}
                  className="truncate block w-full capitalize"
                >
                  {displayText}
                </span>
              </TooltipTrigger>
              {isOverflowing && (
                <TooltipContent side="bottom">{displayText}</TooltipContent>
              )}
            </Tooltip>
          </SelectTrigger>
          <SelectContent className="w-[300px]">
            <div className="space-y-2">
              <div
                onClick={() => {
                  switchView('default-view')
                  setOpenViewSelector(false)
                }}
                className={cn(
                  'text-sm flex justify-between items-center cursor-pointer px-2 py-2 hover:bg-gray-50 relative group',
                  currentView === undefined ? 'bg-gray-200' : ''
                )}
              >
                <div className="flex gap-2 items-center">
                  {currentView === undefined && <Diamond size={12} />}
                  {t('defaultView')}
                </div>
              </div>
              {presets.map((preset) => (
                <div
                  key={preset.query_id}
                  className={cn(
                    'text-sm flex justify-between items-center cursor-pointer px-2 hover:bg-gray-50 relative group',
                    currentView === preset.query_id ? 'bg-gray-200' : ''
                  )}
                  onClick={() => {
                    switchView(preset.query_id)
                    setOpenViewSelector(false)
                  }}
                >
                  <div className="flex gap-2 items-center">
                    {currentView === preset.query_id && (
                      <Diamond className="min-w-4" size={12} />
                    )}
                    {editMode !== preset.query_id && (
                      <span className="truncate max-w-[100px]">
                        {preset.name}
                      </span>
                    )}
                  </div>
                  <div
                    className={cn(
                      'flex gap-1',
                      editMode !== preset.query_id
                        ? 'opacity-0 group-hover:opacity-100 transition-opacity duration-200'
                        : ''
                    )}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <ViewActions preset={preset} />
                  </div>
                </div>
              ))}
            </div>
            <Button
              variant="ghost"
              className="gap-2 w-full text-sm justify-start px-1"
              dataTestId="add-view-button"
              onClick={() => setOpenAddViewDialog(true)}
            >
              <CirclePlusIcon size={20} /> {t('addAView')}
            </Button>
          </SelectContent>
        </Select>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="icon"
              onClick={() => updatePreset()}
              disabled={isUpdatePresetPending}
              className="h-8 capitalize"
              dataTestId="toolbar-save-to-view-button"
            >
              {isUpdatePresetPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              <Save />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <div>{t('saveToView')}</div>
          </TooltipContent>
        </Tooltip>
      </div>
      <div className="flex items-center">
        <Dialog open={openAddViewDialog} onOpenChange={setOpenAddViewDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="mb-6">{t('addAView')}</DialogTitle>
              <div>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-8"
                  >
                    <FormField
                      control={form.control}
                      name="view"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('viewTitle')}</FormLabel>
                          <FormControl>
                            <Input placeholder="" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <DialogClose asChild>
                      <Button
                        type="submit"
                        className="capitalize"
                        dataTestId="apply-button"
                      >
                        {t('apply')}
                      </Button>
                    </DialogClose>
                  </form>
                </Form>
              </div>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
