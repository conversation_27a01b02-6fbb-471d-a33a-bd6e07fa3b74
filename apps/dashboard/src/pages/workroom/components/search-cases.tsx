import { useDebounce, InputSearch } from '@libs/ui'
import { useSearchCases } from '../../../api/use-cases-api'
import { useEffect, useRef, useState } from 'react'
import type { SearchResponse } from 'prime-front-service-client'
import { t } from 'i18next'
import { cn } from '@libs/common'
import { serializeWorkroom } from '../../../router/router-utils'
import {
  getParsedArrayOfObjects,
  defaultContainersSortParam,
  defaultSortParam,
} from '../../../router/router'
import { containersStaticColumns } from '../../containers/components/container-columns'
import { useNavigate } from 'react-router'
import { useAtomValue } from 'jotai'
import { defaultContainersViewAtom } from '../../../config/store'
import { staticColumns } from './workroom-columns'

interface AutocompleteInputProps {
  placeholder?: string
  onChange?: (value: string) => void
}

export function SearchCases({
  placeholder = 'Search...',
}: AutocompleteInputProps) {
  const navigate = useNavigate()
  const defaultContainersView = useAtomValue(defaultContainersViewAtom)

  const [showDropdown, setShowDropdown] = useState(false)
  const [value, setValue] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const debouncedSearchTerm = useDebounce(value, 300)

  const {
    data: searchResults,
    refetch,
    isLoading,
  } = useSearchCases({
    value: debouncedSearchTerm,
    limit: 10,
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setValue(newValue)

    if (newValue.length > 0) {
      setShowDropdown(true)
    } else {
      setShowDropdown(false)
    }
  }

  const handleSelect = (selectedItem: SearchResponse) => {
    setShowDropdown(false)
    inputRef.current?.focus()

    if (selectedItem.is_container) {
      navigate({
        pathname: '/containers',
        search: serializeWorkroom({
          f: defaultContainersView?.f
            ? getParsedArrayOfObjects(defaultContainersView.f)
            : [
                {
                  field: 'provider_fields.issuetype',
                  op: 'eq',
                  value: ['Epic'],
                },
              ],
          s: defaultContainersView?.s
            ? getParsedArrayOfObjects(defaultContainersView.s)
            : [defaultContainersSortParam],
          selected_columns:
            defaultContainersView?.selected_columns || containersStaticColumns,
          opened_item: {
            issue_id: selectedItem.issue_id,
            source_id: selectedItem.source_id,
          },
        }),
      })
    } else {
      navigate({
        pathname: '/workroom',
        search: serializeWorkroom({
          f: [
            {
              field: 'issue_id',
              op: 'eq',
              value: [selectedItem.issue_id],
            },
          ],
          s: [defaultSortParam],
          selected_columns: staticColumns,
        }),
      })
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showDropdown) return

    if (e.key === 'Escape') {
      setShowDropdown(false)
    }
  }

  useEffect(() => {
    if (debouncedSearchTerm) {
      refetch()
    }
  }, [debouncedSearchTerm, refetch])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="relative">
      <InputSearch
        ref={inputRef}
        placeholder={placeholder}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onClear={() => {
          setValue('')
          setShowDropdown(false)
          inputRef.current?.focus()
        }}
      />

      {showDropdown && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full min-w-[300px] rounded-md border bg-popover shadow-md mt-1 py-1 animate-in fade-in-0 zoom-in-95"
        >
          {isLoading ? (
            <p className="text-center py-2 text-sm text-muted-foreground">
              Loading...
            </p>
          ) : searchResults?.length ? (
            <ul className="max-h-60 overflow-auto">
              {searchResults.map((item, index) => (
                <li
                  key={item.issue_id}
                  onClick={() => handleSelect(item)}
                  className={cn(
                    'relative gap-2 justify-around cursor-pointer select-none items-center  px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
                    index !== searchResults.length - 1 &&
                      'border-b border-slate-200'
                  )}
                >
                  <div className="text-slate-500"> {item.issue_id}</div>

                  <div className="text-slate-400 text-sm">{item.title}</div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-center py-2 text-sm text-muted-foreground">
              {t('noResultsFound')}
            </p>
          )}
        </div>
      )}
    </div>
  )
}
