/*eslint-disable max-lines */
import React, { useCallback, useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  Drawer<PERSON><PERSON><PERSON>,
  Drawer<PERSON>ooter,
  DrawerTrigger,
  RadioGroup,
  RadioGroupItem,
  Label,
  Textarea,
  PriorityLabel,
  StateBadge,
} from '@libs/ui'
import { Pencil, XIcon } from 'lucide-react'
import { t } from 'i18next'
import { toast } from 'sonner'
import type { Table } from '@tanstack/react-table'
import type {
  BulkUpdateCasesRequest,
  ExternalCaseWorkroom,
  RiskScoreCategory,
} from 'prime-front-service-client'
import type { CaseStatus } from 'prime-front-service-client/src/models/CaseStatus'
import { BulkLabels } from './bulk-labels'
import { useSetAtom } from 'jotai/index'
import {
  refetchCasesAtom,
  refetchLabelsAtom,
  useBulkUpdateCases,
} from '../../../api/use-cases-api'
import { useWorkroomTable } from '../hooks/use-table'

interface WorkroomBulkEditProps {
  table: Table<ExternalCaseWorkroom>
}
export const WorkroomBulkEdit = ({ table }: WorkroomBulkEditProps) => {
  const { currentPage } = useWorkroomTable()
  const setRefetchCases = useSetAtom(refetchCasesAtom)
  const setRefetchLabels = useSetAtom(refetchLabelsAtom)
  const [isOpen, onOpenChange] = useState(false)
  const [selectedRows, setSelectedRows] = useState<ExternalCaseWorkroom[]>([])
  const [status, setStatus] = useState<CaseStatus | null>(null)
  const [dismissRadioValue, setDismissRadioValue] = useState('')
  const [dismissReason, setDismissReason] = useState<string | null>(null)
  const [selectedLabels, setSelectedLabels] = useState<string[]>([])
  const [riskScoreCategory, setRiskScoreCategory] =
    useState<RiskScoreCategory | null>(null)

  const bulkUpdateCasesMutation = useBulkUpdateCases()

  const clear = () => {
    setStatus(null)
    setRiskScoreCategory(null)
    setSelectedLabels([])
  }

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      clear()
    }
    onOpenChange(open)
  }

  useEffect(() => {
    setSelectedRows(
      table
        ?.getSelectedRowModel()
        ?.rows?.filter((row) => row.getIsSelected())
        .map((row) => row.original)
    )
  }, [table?.getSelectedRowModel()?.rows, currentPage])

  const handleDismissRadioChange = useCallback(
    (value: string) => {
      setDismissRadioValue(value)
      if (value !== 'other') {
        setDismissReason(value)
      } else {
        setDismissReason('')
      }
    },
    [status, selectedRows]
  )

  const handleStatusChange = useCallback(
    (value: CaseStatus) => {
      setStatus(value)
      setDismissRadioValue('')
      setDismissReason('')
    },
    [status, selectedRows]
  )

  const handleRiskScoreChange = useCallback(
    (value: RiskScoreCategory) => {
      setRiskScoreCategory(value as RiskScoreCategory)
    },
    [riskScoreCategory, selectedRows]
  )

  const updateBulk = async () => {
    if (!selectedRows.length) return

    const groupedBySource = selectedRows.reduce((acc, row) => {
      if (!acc[row.source_id]) {
        acc[row.source_id] = []
      }
      acc[row.source_id].push(row)
      return acc
    }, {} as Record<number, Array<ExternalCaseWorkroom>>)

    try {
      const promises = Object.entries(groupedBySource).map(
        async ([sourceId, rows]) => {
          const uniqueIssuesIds = rows.flatMap((row) => row.issue_id)
          const bulkUpdateCasesRequest = {
            ...(status && { status }),
            ...(dismissReason && { dismissed_reason: dismissReason }),
            ...(selectedLabels.length > 0 && { labels: selectedLabels }),
            ...(riskScoreCategory && {
              risk_score_category: riskScoreCategory,
            }),
            issues_ids: uniqueIssuesIds as string[],
          } as BulkUpdateCasesRequest

          return bulkUpdateCasesMutation.mutateAsync({
            source_id: Number(sourceId),
            bulkUpdateCasesRequest,
          })
        }
      )
      await Promise.all(promises)
      setRefetchCases((prev) => prev + 1)
      setRefetchLabels((prev) => prev + 1)
      table.resetRowSelection()
      clear()
      onOpenChange(false)
      toast.success(t('bulkUpdateSuccessful'))
    } catch (error) {
      console.error('Bulk update failed:', error)
      toast.error(t('errors.bulkUpdateFailed'))
    }
  }

  return (
    <Drawer direction="right" open={isOpen} onOpenChange={handleOpenChange}>
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          className="capitalize h-8"
          disabled={!selectedRows?.length}
          dataTestId="bulk-edit-button"
        >
          <Pencil className="pr-2" />
          {t('bulkEdit')}
        </Button>
      </DrawerTrigger>
      <DrawerContent className="fixed top-0 right-0 h-full w-[400px] bg-white shadow-lg overflow-y-auto overflow-x-hidden">
        <DrawerHeader className="p-8">
          <DrawerTitle>
            <div className="w-full flex items-center justify-between mx-0 text-slate-800 text-xl capitalize">
              {t('bulkEdit')}
              <DrawerClose>
                <XIcon />
              </DrawerClose>
            </div>
          </DrawerTitle>
          <DrawerDescription></DrawerDescription>
        </DrawerHeader>
        <div className="flex-col px-8 py-4 h-full">
          <div className="border-b pb-4">
            <div className="capitalize font-medium mb-4">
              {t('statusReview')}
            </div>
            <RadioGroup
              value={status || ''}
              onValueChange={(value) => handleStatusChange(value as CaseStatus)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="open" id="r1" />
                <Label htmlFor="r1">
                  <StateBadge state="open"></StateBadge>
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="done" id="r2" />
                <Label htmlFor="r2">
                  <StateBadge state="done"></StateBadge>
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="dismissed" id="r3" />
                <Label htmlFor="r3">
                  <StateBadge state="dismissed"></StateBadge>
                </Label>
              </div>
              {status === 'dismissed' && (
                <div className="mt-2 ml-4">
                  <div className="font-medium">{t('whyWasItDismissed')}</div>
                  <RadioGroup
                    value={dismissRadioValue}
                    onValueChange={handleDismissRadioChange}
                  >
                    <div className="flex items-center space-x-2 my-3">
                      <RadioGroupItem
                        value="Known issue/Already addressed"
                        id="known-issue"
                      />
                      <Label htmlFor="known-issue">{t('knownIssue')}</Label>
                    </div>
                    <div className="flex items-center space-x-2 mb-3">
                      <RadioGroupItem
                        value="No security implications"
                        id="no-security-implications"
                      />
                      <Label htmlFor="no-security-implications">
                        {t('noSecurityImplications')}
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 mb-3">
                      <RadioGroupItem
                        value="Opened by Security - Ignore"
                        id="opened-by-security"
                      />
                      <Label htmlFor="opened-by-security">
                        {t('openedBySecurity')}
                      </Label>
                    </div>
                    <div className="flex items-start space-x-2 mb-3">
                      <RadioGroupItem value="other" id="other" />
                      <Label htmlFor="other">
                        Other
                        {dismissRadioValue === 'other' && (
                          <Textarea
                            className="my-4 p-4 w-full"
                            id="other-dismiss-reason"
                            placeholder={t('type')}
                            onChange={(e) => setDismissReason(e.target.value)}
                            disabled={dismissRadioValue !== 'other'}
                          />
                        )}
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              )}
            </RadioGroup>
          </div>
          <div className="border-b py-6">
            <div className="capitalize font-medium mb-2">{t('labels')}</div>
            <BulkLabels
              selectedLabels={selectedLabels}
              setSelectedLabels={setSelectedLabels}
            />
          </div>
          <div className="border-b py-6">
            <div className="capitalize font-medium mb-2">
              {t('tableColumns.riskScoreCategory')}
            </div>
            <RadioGroup
              value={riskScoreCategory || ''}
              onValueChange={(value) =>
                handleRiskScoreChange(value as RiskScoreCategory)
              }
            >
              <div className="flex items-center space-x-2 my-3">
                <RadioGroupItem value="monitor" id="monitor" />
                <PriorityLabel priority="monitor"></PriorityLabel>
              </div>
              <div className="flex items-center space-x-2 mb-3">
                <RadioGroupItem value="analyze" id="analyze" />
                <PriorityLabel priority="analyze"></PriorityLabel>
              </div>
              <div className="flex items-center space-x-2 mb-3">
                <RadioGroupItem value="intervene" id="intervene" />
                <PriorityLabel priority="intervene"></PriorityLabel>
              </div>
            </RadioGroup>
          </div>
        </div>
        <DrawerFooter className="flex justify-end items-end w-full px-8 py-4 m-0 sticky bottom-0 bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1),0_-2px_4px_-1px_rgba(0,0,0,0.06)]">
          <div className="flex justify-end items-end w-full gap-2">
            <Button
              variant="outline"
              onClick={() => clear()}
              className="capitalize"
              dataTestId="clear-bulk-update"
            >
              {t('clear')}
            </Button>

            <div className="flex items-center ">
              <Button
                disabled={status === 'dismissed' && !dismissReason}
                onClick={async () => await updateBulk()}
                dataTestId="apply-bulk-update"
                className="capitalize"
              >
                {t('apply')}
              </Button>
            </div>
          </div>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}
