/* eslint-disable max-lines */
import type { Table } from '@tanstack/react-table'
import type {
  ExternalCaseWorkroom,
  ProviderFieldInfoOptions,
} from 'prime-front-service-client'
import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  <PERSON><PERSON>,
  Drawer,
  DrawerClose,
  Drawer<PERSON>ontent,
  DrawerDescription,
  DrawerHeader,
  DrawerT<PERSON>le,
  Drawer<PERSON>ooter,
  DrawerTrigger,
  Popover,
  PopoverTrigger,
  PopoverContent,
  FilterSearchInput,
  FilterDateRangePicker,
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
  Logo,
  JiraIcn,
  InputSearch,
} from '@libs/ui'
import { ChevronDownIcon, Loader2, XIcon } from 'lucide-react'
import { FilterCheckboxCollapse } from '../components'
import { useFilters, usePresets } from '../hooks'
import { t } from 'i18next'
import { useGetLabels } from '../../../api/use-cases-api'
import { FilterRadioCollapse } from './filter-radio-collapse'
import { useFlagsWrapper } from '../../../hooks/use-flags-wrapper'
import { ALLOWED_FILTERS, primeFilterTitles } from '../utils'

interface FilterDrawerProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  table: Table<ExternalCaseWorkroom>
  workroomFields: ProviderFieldInfoOptions[]
  isLoading?: boolean
}

export const FilterDrawer = ({
  isOpen,
  onOpenChange,
  table,
  workroomFields = [],
  isLoading,
}: FilterDrawerProps) => {
  const {
    filters,
    pendingFilters,
    getPendingFilter,
    updatePendingFilters,
    clearPendingFilters,
    applyPendingFilters,
    setPendingFilters,
  } = useFilters()

  const { updatePreset, isUpdatePresetPending } = usePresets()
  const { primeAbstract } = useFlagsWrapper()

  const allColumnsItems = table
    .getAllColumns()
    .filter((column) => column.getCanHide())

  const allWorkroomFields = useMemo(
    () =>
      workroomFields.filter((field) => {
        return allColumnsItems.find((column) => column.id === field.id)
      }),
    [workroomFields, allColumnsItems]
  )

  const { data } = useGetLabels()

  const [filterSearch, setFilterSearch] = useState('')
  const [openAccordion, setOpenAccordion] = useState<string | null>(null)

  const isFilterVisible = useCallback(
    (title: string) => {
      if (!filterSearch) return true
      return title.toLowerCase().includes(filterSearch.toLowerCase())
    },
    [filterSearch]
  )

  useEffect(() => {
    if (!filterSearch) {
      setOpenAccordion(null)
      return
    }

    const hasPrimeTitleMatch = primeFilterTitles.some((filter) =>
      filter.toLowerCase().includes(filterSearch.toLowerCase())
    )

    const hasAllowedFiltersMatch = Object.values(ALLOWED_FILTERS).some(
      (filterKey) =>
        filterKey.toLowerCase().includes(filterSearch.toLowerCase())
    )

    if (hasPrimeTitleMatch || hasAllowedFiltersMatch) {
      setOpenAccordion('item-1')
      return
    }

    const hasJiraMatch = allWorkroomFields.some((field) =>
      field.name.toLowerCase().includes(filterSearch.toLowerCase())
    )

    if (hasJiraMatch) {
      setOpenAccordion('item-2')
    }
  }, [filterSearch, allWorkroomFields])

  const handleApply = async () => {
    applyPendingFilters()
    onOpenChange(false)
  }

  const handleApplyAndSave = useCallback(async () => {
    await updatePreset(pendingFilters)

    applyPendingFilters()
    onOpenChange(false)
  }, [applyPendingFilters, onOpenChange, pendingFilters, updatePreset])

  useEffect(() => {
    if (isOpen) {
      setPendingFilters(filters)
    } else {
      clearPendingFilters()
    }
  }, [clearPendingFilters, filters, isOpen, setPendingFilters])

  return (
    <Drawer direction="right" open={isOpen} onOpenChange={onOpenChange}>
      <DrawerTrigger asChild>
        <Button
          className="capitalize h-8"
          dataTestId="filters-drawer-button"
          disabled={isLoading}
        >
          {t('filters')}
        </Button>
      </DrawerTrigger>
      <DrawerContent className="fixed top-0 right-0 h-full w-[400px] bg-white shadow-lg overflow-y-auto overflow-x-hidden">
        <DrawerHeader className="p-8">
          <DrawerTitle>
            <div className="w-full flex items-center justify-between mx-0 text-slate-800 text-xl capitalize">
              {t('filters')}
              <DrawerClose>
                <XIcon />
              </DrawerClose>
            </div>
          </DrawerTitle>
          <DrawerDescription>
            <div className="mt-4">
              <InputSearch
                placeholder="Search filters..."
                value={filterSearch}
                onChange={(e) => setFilterSearch(e.target.value)}
                onClear={() => setFilterSearch('')}
                data-testid="filter-search-input"
              />
            </div>
          </DrawerDescription>
        </DrawerHeader>

        <div className="flex-col p-8 pt-0 flex-1">
          {/* static filters */}
          <Accordion
            type="single"
            collapsible
            value={openAccordion === 'item-1' ? 'item-1' : undefined}
            onValueChange={setOpenAccordion}
          >
            <AccordionItem value="item-1" className="border-none">
              <AccordionTrigger className="bg-slate-50 rounded-md px-3 mb-4 shadow-sm justify-between text-xl">
                <div className="flex items-center gap-2">
                  <Logo className="w-5 h-5" /> Prime
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-3">
                {
                  <>
                    {primeAbstract &&
                      isFilterVisible(t('tableColumns.fire_summary')) && (
                        <FilterSearchInput
                          title={t('tableColumns.fire_summary')}
                          filterKey="fire_summary"
                          savedFilter={getPendingFilter('fire_summary')}
                          onFilterChange={updatePendingFilters}
                        />
                      )}
                    {isFilterVisible('title') && (
                      <FilterSearchInput
                        title="title"
                        filterKey="title"
                        savedFilter={getPendingFilter('title')}
                        onFilterChange={updatePendingFilters}
                      />
                    )}
                    {isFilterVisible('Issue id') && (
                      <FilterSearchInput
                        title="Issue id"
                        filterKey="issue_id"
                        savedFilter={getPendingFilter('issue_id')}
                        onFilterChange={updatePendingFilters}
                      />
                    )}

                    {isFilterVisible('Status') && (
                      <FilterCheckboxCollapse
                        title="Status"
                        id={ALLOWED_FILTERS.status}
                        savedFilter={getPendingFilter(ALLOWED_FILTERS.status)}
                        options={['open', 'done', 'dismissed']}
                        onFilterChange={updatePendingFilters}
                      />
                    )}

                    {isFilterVisible('Confidence Level') && (
                      <FilterCheckboxCollapse
                        title="Confidence Level"
                        id={ALLOWED_FILTERS.confidence_level}
                        savedFilter={getPendingFilter(
                          ALLOWED_FILTERS.confidence_level
                        )}
                        options={['high', 'medium', 'low']}
                        onFilterChange={updatePendingFilters}
                      />
                    )}
                    {isFilterVisible('Risk Score Category') && (
                      <FilterCheckboxCollapse
                        title="Risk Score Category"
                        id={ALLOWED_FILTERS.risk_score_category}
                        savedFilter={getPendingFilter(
                          ALLOWED_FILTERS.risk_score_category
                        )}
                        options={['intervene', 'analyze', 'monitor']}
                        onFilterChange={updatePendingFilters}
                      />
                    )}

                    {isFilterVisible('Confidentiality Level') && (
                      <FilterCheckboxCollapse
                        title="Confidentiality Level"
                        id={ALLOWED_FILTERS.confidentiality_level}
                        options={['high', 'medium', 'low']}
                        savedFilter={getPendingFilter(
                          ALLOWED_FILTERS.confidentiality_level
                        )}
                        onFilterChange={updatePendingFilters}
                      />
                    )}

                    {isFilterVisible('Integrity Level') && (
                      <FilterCheckboxCollapse
                        title="Integrity Level"
                        id={ALLOWED_FILTERS.integrity_level}
                        options={['high', 'medium', 'low']}
                        savedFilter={getPendingFilter(
                          ALLOWED_FILTERS.integrity_level
                        )}
                        onFilterChange={updatePendingFilters}
                      />
                    )}

                    {isFilterVisible('Availability Level') && (
                      <FilterCheckboxCollapse
                        title="Availability Level"
                        id={ALLOWED_FILTERS.availability_level}
                        options={['high', 'medium', 'low']}
                        savedFilter={getPendingFilter(
                          ALLOWED_FILTERS.availability_level
                        )}
                        onFilterChange={updatePendingFilters}
                      />
                    )}

                    {isFilterVisible('MITRE ATT&CK') && (
                      <FilterCheckboxCollapse
                        title="MITRE ATT&CK"
                        id={ALLOWED_FILTERS.mitre_categories}
                        options={[
                          'Collection',
                          'Command and Control',
                          'Credential Access',
                          'Defense Evasion',
                          'Discovery',
                          'Execution',
                          'Exfiltration',
                          'Impact',
                          'Initial Access',
                          'Lateral Movement',
                          'Persistence',
                          'Privilege Escalation',
                          'Reconnaissance',
                          'Resource Development',
                        ]}
                        savedFilter={getPendingFilter(
                          ALLOWED_FILTERS.mitre_categories
                        )}
                        onFilterChange={updatePendingFilters}
                      />
                    )}

                    {isFilterVisible('LINDDUN') && (
                      <FilterCheckboxCollapse
                        title="LINDDUN"
                        id={ALLOWED_FILTERS.linddun_categories}
                        options={[
                          'Linkability',
                          'Identifiability',
                          'Non-repudiation',
                          'Detectability',
                          'Disclosure of information',
                          'Unawareness',
                          'Non-compliance',
                        ]}
                        savedFilter={getPendingFilter(
                          ALLOWED_FILTERS.linddun_categories
                        )}
                        onFilterChange={updatePendingFilters}
                      />
                    )}

                    {isFilterVisible('Is Automated') && (
                      <FilterRadioCollapse
                        title="Is Automated"
                        id={ALLOWED_FILTERS.is_automated}
                        savedFilter={getPendingFilter(
                          ALLOWED_FILTERS.is_automated
                        )}
                        onFilterChange={updatePendingFilters}
                      />
                    )}

                    {isFilterVisible('Is Security Enhancement') && (
                      <FilterRadioCollapse
                        title="Is Security Enhancement"
                        id={ALLOWED_FILTERS.is_security_enhancement}
                        savedFilter={getPendingFilter(
                          ALLOWED_FILTERS.is_security_enhancement
                        )}
                        onFilterChange={updatePendingFilters}
                      />
                    )}
                  </>
                }

                {data?.labels && isFilterVisible('Labels') && (
                  <FilterCheckboxCollapse
                    title="Labels"
                    id={ALLOWED_FILTERS.labels}
                    savedFilter={getPendingFilter(ALLOWED_FILTERS.labels)}
                    options={data?.labels || []}
                    withHasValueToggle
                    onFilterChange={updatePendingFilters}
                  />
                )}

                <FilterCheckboxCollapse
                  title="Container"
                  id={ALLOWED_FILTERS.parent_id}
                  savedFilter={getPendingFilter(ALLOWED_FILTERS.parent_id)}
                  options={[]}
                  withHasValueToggle
                  onFilterChange={updatePendingFilters}
                  showOptions={false}
                />
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* dynamic filters */}
          <Accordion
            type="single"
            collapsible
            value={openAccordion === 'item-2' ? 'item-2' : undefined}
            onValueChange={setOpenAccordion}
          >
            <AccordionItem value="item-2" className="border-none">
              <AccordionTrigger className="bg-slate-50 rounded-md px-3 mb-4 shadow-sm justify-between text-xl">
                <div className="flex items-center gap-2">
                  <JiraIcn className="w-5 h-5 text-jira" /> Jira
                </div>
              </AccordionTrigger>
              <AccordionContent>
                {allWorkroomFields.map((item) => {
                  if (!isFilterVisible(item.name)) return null

                  if (item.type === 'date') {
                    return (
                      <FilterDateRangePicker
                        key={item.id}
                        filterKey={item.id}
                        title={item.name}
                        savedFilter={getPendingFilter(item.id)}
                        onFilterChange={updatePendingFilters}
                      />
                    )
                  }
                  if (item.type === 'enum' || item.type === 'array') {
                    return (
                      <FilterCheckboxCollapse
                        key={item.id}
                        id={item.id}
                        title={item.name}
                        showOperator
                        options={item.options || []}
                        savedFilter={getPendingFilter(item.id)}
                        onFilterChange={updatePendingFilters}
                        withSearch
                        withHasValueToggle
                      />
                    )
                  }
                  return (
                    <FilterSearchInput
                      key={item.id}
                      filterKey={item.id}
                      title={item.name}
                      savedFilter={getPendingFilter(item.id)}
                      onFilterChange={updatePendingFilters}
                    />
                  )
                })}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
        <DrawerFooter className="flex justify-end items-end w-full px-8 py-4 m-0 sticky bottom-0 bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1),0_-2px_4px_-1px_rgba(0,0,0,0.06)]">
          <div className="flex justify-end items-end w-full gap-2">
            <Button
              variant="outline"
              onClick={clearPendingFilters}
              className="capitalize"
              dataTestId="clear-filters-button"
            >
              {t('clear')}
            </Button>

            <div className="flex items-center rounded-full overflow-hidden text-secondary-foreground bg-teal-800">
              <Button
                className="shadow-none capitalize rounded-r-none"
                onClick={handleApply}
                dataTestId="apply-button"
              >
                {t('apply')}
              </Button>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    dataTestId="apply-popover-trigger"
                    className="rounded-l-none"
                  >
                    <ChevronDownIcon className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-auto p-0"
                  side="top"
                  align="end"
                  sideOffset={10}
                  asChild
                >
                  <Button
                    onClick={handleApplyAndSave}
                    disabled={isUpdatePresetPending}
                    dataTestId="apply-save-to-view-button"
                    className="p-3 border-slate-400 text-sm hover:bg-muted disabled:opacity-90"
                  >
                    {isUpdatePresetPending && (
                      <div className="flex ml-2">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      </div>
                    )}
                    {t('applySaveToView')}
                  </Button>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}
