import type { QueryView } from 'prime-front-service-client'
import { usePresets } from '../hooks'
import {
  useAddOrUpdateQueryCasesView,
  useDeleteCasesView,
} from '../../../api/use-config-api'
import { useEffect, useRef, useState } from 'react'
import { toast } from 'sonner'
import { t } from 'i18next'
import { Button, Input } from '@libs/ui'
import { Edit, Loader2, Trash2 } from 'lucide-react'
import { useAtom } from 'jotai'
import { editModePresetAtom } from './views-selector'

export const ViewActions = ({ preset }: { preset: QueryView }) => {
  const { refetchPresets, switchView } = usePresets()
  const useAddOrUpdateQueryCasesViewMutation = useAddOrUpdateQueryCasesView()
  const deleteCasesViewMutation = useDeleteCasesView()
  const [isEditing, setIsEditing] = useState(false)
  const inputRef = useRef<HTMLInputElement | null>(null)

  const [editMode, setEditMode] = useAtom(editModePresetAtom)

  useEffect(() => {
    if (editMode !== preset.query_id) {
      setIsEditing(false)
    }
  }, [editMode])

  const handleDelete = (queryId: string) => {
    deleteCasesViewMutation.mutate(
      { query_id: queryId, view_type: 'cases' },
      {
        onSuccess: async () => {
          await refetchPresets()
          toast.success(t('presetDeletedSuccessfully'))
          switchView('default-view')
        },
        onError: () => {
          toast.error(t('errors.failedToDeletePreset'))
        },
      }
    )
  }

  const handleEdit = (query_id: string, name: string) => {
    useAddOrUpdateQueryCasesViewMutation.mutate(
      {
        queryView: {
          name,
          query_id,
          query_list: preset.query_list,
          selected_columns: preset.selected_columns,
          sort_list: preset.sort_list,
        },
        view_type: 'cases',
      },
      {
        onSuccess: async () => {
          toast.success(t('filtersSavedSuccessfully'))
          await refetchPresets()
        },
        onError: () => {
          toast.error(t('errors.failedToSaveFilters'))
        },
        onSettled: () => {
          setIsEditing(false)
          setEditMode(null)
        },
      }
    )
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditMode(null)

    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  return (
    <div className="flex items-center justify-between px-2 py-1 gap-2">
      {isEditing && editMode === preset.query_id && (
        <Input ref={inputRef} type="text" defaultValue={preset.name} />
      )}

      <div className="flex">
        {isEditing ? (
          <div className="flex gap-2">
            <Button
              onClick={() =>
                handleEdit(preset.query_id, inputRef?.current?.value || '')
              }
              size="sm"
              dataTestId="save-preset-button"
            >
              {useAddOrUpdateQueryCasesViewMutation.isPending && (
                <Loader2
                  data-testid="edit-pending"
                  className="h-4 w-4 animate-spin"
                />
              )}
              {t('save')}
            </Button>
            <Button
              onClick={() => handleCancel()}
              variant="secondary"
              size="sm"
              dataTestId="cancel-preset-button"
            >
              {t('cancel')}
            </Button>
          </div>
        ) : (
          <div>
            <Button
              onClick={() => {
                setIsEditing(true)
                setEditMode(preset.query_id)
              }}
              variant="icon"
              dataTestId="edit-preset-button"
            >
              <Edit className="h-4 w-4" />
              <span className="sr-only">{t('edit')}</span>
            </Button>

            <Button
              onClick={() => handleDelete(preset.query_id)}
              variant="icon"
              dataTestId="delete-preset-button"
            >
              {deleteCasesViewMutation.isPending ? (
                <Loader2
                  data-testid="delete-pending"
                  className="h-4 w-4 animate-spin"
                />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
              <span className="sr-only">{t('delete')}</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
