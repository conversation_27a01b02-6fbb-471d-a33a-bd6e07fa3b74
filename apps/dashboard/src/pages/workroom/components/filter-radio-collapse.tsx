import type { FilterItem, FilterOperator, FilterValue } from '@libs/ui'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Label,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@libs/ui'
import { ChevronDown, ChevronUp, EqualIcon, EqualNotIcon } from 'lucide-react'
import { cn } from '@libs/common'
import { t } from 'i18next'
import { useEffect, useState } from 'react'

interface FilterRadioCollapseProps {
  id: string
  title: string
  savedFilter?: FilterItem
  showOperator?: boolean
  onFilterChange: (filter: FilterItem) => void
}

const defaultValue = 'true'

export function FilterRadioCollapse({
  id,
  title,
  savedFilter,
  onFilterChange,
  showOperator = false,
}: FilterRadioCollapseProps) {
  const value = savedFilter?.value?.[0] || undefined
  const [initialValue, setInitialValue] = useState<string | undefined>(value)
  const [isOpen, setIsOpen] = useState(false)

  const [filterOperator, setFilterOperator] = useState(savedFilter?.op || 'eq')

  useEffect(() => {
    if (!savedFilter) {
      setInitialValue(undefined)
      setFilterOperator('eq')
    } else {
      setInitialValue(savedFilter.value[0] || defaultValue)
      setFilterOperator(savedFilter.op || 'eq')
    }
  }, [savedFilter])

  const onOperatorChange = (op: FilterOperator) => {
    setFilterOperator(op)
    if (savedFilter) {
      onFilterChange({
        field: id,
        op,
        value: value as FilterValue,
      })
    }
  }

  const onChange = (value: string) => {
    setInitialValue(value)
    onFilterChange({
      field: id,
      op: filterOperator,
      value: [value],
    })
  }

  return (
    <Collapsible
      className={cn(
        'mb-4 w-full',
        isOpen ? 'border-b border-gray-200 mb-2 pb-2' : ''
      )}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <CollapsibleTrigger asChild>
        <div
          className={cn(
            'flex justify-between items-center gap-3 cursor-pointer capitalize mb-3 font-medium pb-2',
            isOpen ? '' : 'border-b border-gray-200'
          )}
        >
          <div className="flex gap-2 text-lg">
            <span>{title}</span>
            {savedFilter && <span>({savedFilter.value.length})</span>}
          </div>
          <div className="flex items-center gap-1">
            {isOpen ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
          </div>
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent className="w-[300px] p-0">
        {showOperator && (
          <div onClick={(e) => e.stopPropagation()} className="p-2">
            <Select
              defaultValue={filterOperator}
              onValueChange={(value) =>
                onOperatorChange(value as FilterOperator)
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent align="center">
                <SelectItem value="eq">
                  <div className="flex gap-1 items-center">
                    <EqualIcon size={12} /> {t('eq')}
                  </div>
                </SelectItem>
                <SelectItem value="ne">
                  <div className="flex gap-1 items-center">
                    <EqualNotIcon size={12} /> {t('ne')}
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <RadioGroup
          onValueChange={onChange}
          defaultValue={initialValue}
          className="p-2 space-y-2"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="true" id="true" />
            <Label htmlFor="true">True</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="false" id="false" />
            <Label htmlFor="false">False</Label>
          </div>
        </RadioGroup>
      </CollapsibleContent>
    </Collapsible>
  )
}
