/* eslint-disable max-lines */

import type { FilterItem, FilterOperator, FilterValue } from '@libs/ui'
import {
  Button,
  Checkbox,
  ClipLoader,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@libs/ui'
import { ChevronDown, ChevronUp, EqualIcon, EqualNotIcon } from 'lucide-react'
import { cn } from '@libs/common'
import { t } from 'i18next'
import { useEffect, useMemo, useState, useRef } from 'react'
import { FilterOption } from './filter-option'
import { useGetCasesSearchAutocomplete } from '../../../api/use-cases-api'

interface DataTableFacetedFilterProps {
  id: string
  title: string
  options: string[]
  savedFilter?: FilterItem
  showOperator?: boolean
  onFilterChange: (filter: FilterItem) => void
  withSearch?: boolean
  withHasValueToggle?: boolean
  showOptions?: boolean
}

type hasValueToggle = 'any' | 'hasValue' | 'noValue'
export function FilterCheckboxCollapse({
  id,
  title,
  options,
  savedFilter,
  onFilterChange,
  withSearch = false,
  withHasValueToggle = false,
  showOperator = false,
  showOptions = true,
}: DataTableFacetedFilterProps) {
  const [initialOptions, setInitialOptions] = useState<string[]>(options)
  const sortOrderRef = useRef<string[]>([])
  const { value } = savedFilter || {}
  const [isOpen, setIsOpen] = useState(false)
  const [isOpenCombo, setIsOpenCombo] = useState(false)
  const [selectedOptions, setSelectedOptions] = useState(
    savedFilter?.value || []
  )
  const [filterOperator, setFilterOperator] = useState(savedFilter?.op || 'eq')

  const [hasValueToggle, setHasValueToggle] = useState<hasValueToggle>('any')

  const [searchQuery, setSearchQuery] = useState('')
  const {
    refetch,
    data: searchData,
    isLoading,
  } = useGetCasesSearchAutocomplete({
    value: searchQuery,
    field: id,
  })

  const onSelect = (option: string) => {
    setSelectedOptions((prev: any) => {
      const isSelected = prev.includes(option)
      const newSelectedOptions = isSelected
        ? prev.filter((selected: string) => selected !== option)
        : [...prev, option]

      onFilterChange({
        field: id,
        op: filterOperator,
        value: newSelectedOptions,
      })

      return newSelectedOptions
    })
  }
  const onOperatorChange = (op: FilterOperator) => {
    setFilterOperator(op)
    if (savedFilter) {
      onFilterChange({
        field: id,
        op,
        value: value as FilterValue,
      })
    }
  }

  const handleHasValueToggle = () => {
    setHasValueToggle((prev) => {
      const toggleSequence: hasValueToggle[] = ['any', 'noValue', 'hasValue']
      const currentIndex = toggleSequence.indexOf(prev)
      const newToggle =
        toggleSequence[(currentIndex + 1) % toggleSequence.length]

      if (newToggle === 'hasValue') {
        onFilterChange({
          field: id,
          op: 'exist',
          value: 'true',
        })
      } else if (newToggle === 'noValue') {
        onFilterChange({
          field: id,
          op: 'exist',
          value: 'false',
        })
      } else {
        onFilterChange({
          field: id,
          op: filterOperator,
          value: selectedOptions,
        })
      }

      return newToggle
    })
  }

  useEffect(() => {
    if (searchQuery) {
      refetch()
    }
  }, [refetch, searchQuery])

  useEffect(() => {
    if (savedFilter) {
      const { op, value } = savedFilter

      if (op === 'exist') {
        setHasValueToggle(value === 'true' ? 'hasValue' : 'noValue')
        setSelectedOptions([])
      } else {
        setHasValueToggle('any')
        setFilterOperator(op || 'eq')
        setSelectedOptions(Array.isArray(value) ? value : [value])
        setInitialOptions((prevOptions) => {
          const newOptions = new Set(prevOptions)

          if (Array.isArray(value)) {
            value.forEach((val) => newOptions.add(val))
          } else {
            newOptions.add(value)
          }
          return Array.from(newOptions)
        })
      }
    } else {
      setSelectedOptions([])
      setFilterOperator('eq')
      setHasValueToggle('any')
    }
  }, [savedFilter])

  const searchResults = searchData?.options || []

  const sortedOptions = useMemo(() => {
    const currentSelected = Array.isArray(selectedOptions)
      ? selectedOptions
      : []

    if (
      sortOrderRef.current.length === 0 ||
      !initialOptions.every((option) => sortOrderRef.current.includes(option))
    ) {
      const sorted = [...initialOptions].sort((a, b) => {
        const aSelected = currentSelected.includes(a)
        const bSelected = currentSelected.includes(b)

        if (aSelected && !bSelected) return -1
        if (!aSelected && bSelected) return 1
        return a.localeCompare(b)
      })

      sortOrderRef.current = sorted
      return sorted
    }

    return sortOrderRef.current.filter((option) =>
      initialOptions.includes(option)
    )
  }, [initialOptions, selectedOptions])

  return (
    <Collapsible
      className={cn(
        'mb-4 w-full',
        isOpen ? 'border-b border-gray-200 mb-2 pb-2' : ''
      )}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <CollapsibleTrigger asChild>
        <div
          className={cn(
            'flex justify-between items-center gap-3 cursor-pointer capitalize mb-3 font-medium pb-2',
            isOpen ? '' : 'border-b border-gray-200'
          )}
        >
          <div className="flex gap-2 text-lg">
            <span>{title}</span>
            {savedFilter && (
              <span>
                (
                {typeof savedFilter.value === 'string'
                  ? savedFilter.value === 'true'
                    ? t('hasValue')
                    : savedFilter.value === 'false'
                    ? t('noValue')
                    : 1
                  : savedFilter.value.length}
                )
              </span>
            )}
          </div>
          <div className="flex items-center gap-1">
            {isOpen ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
          </div>
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent className="p-0">
        <div
          onClick={(e) => e.stopPropagation()}
          className="flex justify-between items-center gap-2 mb-4"
        >
          {showOperator && (
            <Select
              defaultValue={filterOperator}
              onValueChange={(value) =>
                onOperatorChange(value as FilterOperator)
              }
              disabled={hasValueToggle !== 'any'}
            >
              <SelectTrigger className="max-w-36">
                <SelectValue />
              </SelectTrigger>
              <SelectContent align="center">
                <SelectItem value="eq">
                  <div className="flex gap-1 items-center">
                    <EqualIcon size={12} /> {t('eq')}
                  </div>
                </SelectItem>
                <SelectItem value="ne">
                  <div className="flex gap-1 items-center">
                    <EqualNotIcon size={12} /> {t('ne')}
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          )}

          {withHasValueToggle && (
            <Button
              variant={
                hasValueToggle === 'noValue'
                  ? 'destructive'
                  : hasValueToggle === 'hasValue'
                  ? 'default'
                  : 'outline'
              }
              dataTestId="has-value-toggle"
              onClick={handleHasValueToggle}
            >
              {t(hasValueToggle)}
            </Button>
          )}
        </div>

        {withSearch && (
          <div className="">
            <Popover open={isOpenCombo} onOpenChange={setIsOpenCombo}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  dataTestId="search-combo"
                  className="w-full justify-start text-slate-400 border-slate-400 mb-4"
                  disabled={hasValueToggle !== 'any'}
                >
                  {t('search')}...
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[200px] p-0" align="start">
                <Command shouldFilter={false}>
                  <CommandInput
                    placeholder="Search..."
                    value={searchQuery}
                    onValueChange={setSearchQuery}
                  />
                  <CommandList>
                    <CommandEmpty>
                      {isLoading ? (
                        <ClipLoader />
                      ) : searchQuery === '' ? (
                        <span className="text-xs">
                          Start typing to load results
                        </span>
                      ) : (
                        'No results found.'
                      )}
                    </CommandEmpty>
                    <CommandGroup>
                      {searchResults.map((option) => (
                        <CommandItem
                          key={crypto.randomUUID()}
                          value={option}
                          onSelect={(value) => {
                            setInitialOptions((prev) => {
                              if (!prev.includes(value)) {
                                return [...prev, value]
                              }
                              return prev
                            })

                            onSelect(value)
                            setIsOpenCombo(false)
                          }}
                        >
                          {option}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
        )}

        {showOptions && (
          <Command>
            <CommandList>
              <CommandEmpty>{t('noResultsFound')}</CommandEmpty>
              <CommandGroup className="filter-command-group">
                {sortedOptions.map((option) => {
                  const isSelected = selectedOptions.includes(option)
                  return (
                    <CommandItem
                      key={crypto.randomUUID()}
                      onSelect={() => onSelect(option)}
                      disabled={hasValueToggle !== 'any'}
                      className="gap-2"
                    >
                      <Checkbox checked={isSelected} />

                      <FilterOption option={option} id={id || ''} />
                    </CommandItem>
                  )
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        )}
      </CollapsibleContent>
    </Collapsible>
  )
}
