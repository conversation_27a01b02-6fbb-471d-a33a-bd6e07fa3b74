import type { ExternalCaseWorkroom } from 'prime-front-service-client'
import { useFlagsWrapper } from '../../../../hooks/use-flags-wrapper'
import { Link } from 'react-router'
import { Sparkles } from 'lucide-react'
import React from 'react'

export const TitleCell = ({ caseInfo }: { caseInfo: ExternalCaseWorkroom }) => {
  const { primeAbstract } = useFlagsWrapper()
  return (
    <div>
      <Link
        to={`/workroom/${caseInfo.source_id}/${caseInfo.issue_id}`}
        className="mb-2  font-medium hover:text-primary hover:underline"
      >
        <div className="flex items-center gap-3">{caseInfo.title}</div>
      </Link>
      {primeAbstract && caseInfo.issue_analysis?.fire_summary && (
        <div className="mt-2 flex items-center gap-1 text-s font-light text-primary">
          <Sparkles size={16} />
          {caseInfo.issue_analysis.fire_summary}
        </div>
      )}
    </div>
  )
}
