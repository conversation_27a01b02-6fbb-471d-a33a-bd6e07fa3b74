import type { ExternalCaseWorkroom } from 'prime-front-service-client'
import { useSetAtom } from 'jotai'
import {
  refetchCasesAtom,
  useUpdateCaseStatus,
} from '../../../../api/use-cases-api'
import { CaseStatus } from 'prime-front-service-client/src/models/CaseStatus'
import { toast } from 'sonner'
import { t } from 'i18next'
import {
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  Label,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  StateBadge,
  stateToClass,
  Textarea,
} from '@libs/ui'
import { useState } from 'react'
import { cn } from '@libs/common'

export const StatusUpdateCell = ({
  caseInfo,
}: {
  caseInfo: ExternalCaseWorkroom
}) => {
  const setRefetchCases = useSetAtom(refetchCasesAtom)
  const [openDialog, setOpenDialog] = useState<boolean>(false)
  const [radioValue, setRadioValue] = useState('')
  const [dismissReason, setDismissReason] = useState('')
  const { mutate: updateCaseStatusMutation } = useUpdateCaseStatus()

  const updateStatus = async (newStatus: CaseStatus) => {
    if (newStatus === CaseStatus.dismissed) {
      setOpenDialog(true)
    } else {
      await handleUpdateStatus(newStatus)
    }
  }

  const handleRadioChange = (value: string) => {
    setRadioValue(value)
    if (value !== 'other') {
      setDismissReason(value)
    } else {
      setDismissReason('')
    }
  }
  const handleUpdateStatus = async (newStatus: CaseStatus) => {
    updateCaseStatusMutation(
      {
        source_id: caseInfo.source_id,
        issue_id: caseInfo.issue_id,
        status: newStatus,
        dismissed_reason: dismissReason,
      },
      {
        onSuccess: () => {
          toast.success(t('statusUpdatedSuccessfully'))
          setRefetchCases((prev) => prev + 1)
        },
        onError: () => {
          toast.error(t('errors.failedToUpdateStatus'))
        },
      }
    )
  }
  return (
    <div>
      <Select
        onValueChange={async (value) => {
          await updateStatus(value as CaseStatus)
        }}
        value={caseInfo.status}
      >
        <SelectTrigger
          className={cn(
            'flex items-center h-8 w-28',
            caseInfo.status === 'open'
              ? stateToClass[CaseStatus.open]
              : caseInfo.status === 'done'
              ? stateToClass[CaseStatus.done]
              : stateToClass[CaseStatus.dismissed]
          )}
        >
          <div className="flex items-center gap-2">
            <div>{caseInfo.status}</div>
          </div>
        </SelectTrigger>
        <SelectContent className="capitalize w-[150px]">
          <SelectItem value={CaseStatus.open}>
            <StateBadge state={CaseStatus.open} />
          </SelectItem>
          <SelectItem value={CaseStatus.done}>
            <StateBadge state={CaseStatus.done} />
          </SelectItem>
          <SelectItem value={CaseStatus.dismissed}>
            <StateBadge state={CaseStatus.dismissed} />
          </SelectItem>
        </SelectContent>
      </Select>
      <Dialog
        open={openDialog}
        onOpenChange={(isOpen) => {
          setOpenDialog(isOpen)
          if (!isOpen) {
            setDismissReason('')
            setRadioValue('')
          }
        }}
      >
        <DialogContent className="sm:max-w-[425px] font-medium text-sm">
          <DialogTitle>{t('whyWasItDismissed')}</DialogTitle>
          <RadioGroup value={radioValue} onValueChange={handleRadioChange}>
            <div className="flex items-center space-x-2 my-3">
              <RadioGroupItem
                value="Known issue/Already addressed"
                id="known-issue"
              />
              <Label htmlFor="known-issue">{t('knownIssue')}</Label>
            </div>
            <div className="flex items-center space-x-2 mb-3">
              <RadioGroupItem
                value="No security implications"
                id="no-security-implications"
              />
              <Label htmlFor="no-security-implications">
                {t('noSecurityImplications')}
              </Label>
            </div>
            <div className="flex items-center space-x-2 mb-3">
              <RadioGroupItem
                value="Opened by Security - Ignore"
                id="opened-by-security"
              />
              <Label htmlFor="opened-by-security">
                {t('openedBySecurity')}
              </Label>
            </div>
            <div className="flex items-start space-x-2 mb-3">
              <RadioGroupItem value="other" id="other" />
              <Label htmlFor="other">
                Other
                {radioValue === 'other' && (
                  <Textarea
                    className="my-4 p-4 w-full"
                    id="other-dismiss-reason"
                    placeholder={t('type')}
                    onChange={(e) => setDismissReason(e.target.value)}
                    disabled={radioValue !== 'other'}
                  />
                )}
              </Label>
            </div>
          </RadioGroup>
          <Button
            onClick={async () => {
              await handleUpdateStatus(CaseStatus.dismissed)
              setOpenDialog(false)
            }}
            disabled={!dismissReason}
            dataTestId="submit-dismiss-button"
          >
            {t('submit')}
          </Button>
        </DialogContent>
      </Dialog>
    </div>
  )
}
