import { RiskScoreCategory } from 'prime-front-service-client'
import type { ExternalCaseWorkroom } from 'prime-front-service-client'
import {
  refetchCasesAtom,
  useUpdateRiskScoreCategory,
} from '../../../../api/use-cases-api'
import { useSetAtom } from 'jotai/index'
import { toast } from 'sonner'
import {
  riskScoreCategoryToClass,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@libs/ui'
import { CaseStatus } from 'prime-front-service-client/src/models/CaseStatus'
import { cn } from '@libs/common'

export const RiskScoreCell = ({
  caseInfo,
}: {
  caseInfo: ExternalCaseWorkroom
}) => {
  const mutation = useUpdateRiskScoreCategory()
  const setRefetchCases = useSetAtom(refetchCasesAtom)
  const handleUpdateRiskScoreCategory = async (value: RiskScoreCategory) => {
    mutation.mutate(
      {
        source_id: caseInfo.source_id,
        issue_id: caseInfo.issue_id,
        risk_score_category: value as RiskScoreCategory,
      },
      {
        onSuccess: () => {
          toast.success('Risk score category updated successfully')
          setRefetchCases((prev) => prev + 1)
        },
        onError: () => {
          toast.error('Failed to update risk score category')
        },
      }
    )
  }
  const risk_score_category = caseInfo.issue_analysis.risk_score_category
  return (
    <Select
      disabled={caseInfo.status === CaseStatus.done}
      onValueChange={async (value) => {
        await handleUpdateRiskScoreCategory(value as RiskScoreCategory)
      }}
      value={risk_score_category}
    >
      <SelectTrigger
        className={cn(
          'flex items-center h-8 w-28',
          risk_score_category === 'intervene'
            ? riskScoreCategoryToClass[RiskScoreCategory.intervene]
            : risk_score_category === 'analyze'
            ? riskScoreCategoryToClass[RiskScoreCategory.analyze]
            : riskScoreCategoryToClass[RiskScoreCategory.monitor]
        )}
      >
        <div className="flex items-center gap-2">
          <div>{risk_score_category as RiskScoreCategory}</div>
        </div>
      </SelectTrigger>
      <SelectContent className="capitalize w-[180px]">
        <SelectItem value={RiskScoreCategory.intervene}>
          <div
            className={cn(
              'flex items-center justify-around h-8 w-24',
              riskScoreCategoryToClass[RiskScoreCategory.intervene]
            )}
          >
            {RiskScoreCategory.intervene}
          </div>
        </SelectItem>
        <SelectItem className="flex" value={RiskScoreCategory.analyze}>
          <div
            className={cn(
              'flex items-center justify-around h-8 w-24',
              riskScoreCategoryToClass[RiskScoreCategory.analyze]
            )}
          >
            {RiskScoreCategory.analyze}
          </div>
        </SelectItem>
        <SelectItem value={RiskScoreCategory.monitor}>
          <div
            className={cn(
              'flex items-center justify-around h-8 w-24',
              riskScoreCategoryToClass[RiskScoreCategory.monitor]
            )}
          >
            {RiskScoreCategory.monitor}
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  )
}
