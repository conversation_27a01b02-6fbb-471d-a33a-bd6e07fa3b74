import type { ExternalCaseWorkroom } from 'prime-front-service-client'
import { parseAsJson, useQueryState } from 'nuqs'
import { containerOpenedItemSchema } from '../../../../router/router-utils'

export const ContainerCell = ({
  caseInfo,
}: {
  caseInfo: ExternalCaseWorkroom
}) => {
  const [_, setOpenedContainer] = useQueryState(
    'opened_item',
    parseAs<PERSON>son(containerOpenedItemSchema.parse)
  )
  return (
    <div
      className="hover:underline max-w-64 w-max cursor-pointer pl-2"
      onClick={() => {
        setOpenedContainer({
          issue_id: caseInfo.parents?.length
            ? caseInfo.parents[0]
            : caseInfo.issue_id,
          source_id: caseInfo.source_id,
        })
      }}
    >
      {!!caseInfo.parents?.length && <span>{caseInfo.parents[0]}</span>}
    </div>
  )
}
