import { flexRender } from '@tanstack/react-table'
import { t } from 'i18next'
import { DndContext, closestCenter } from '@dnd-kit/core'
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers'
import {
  SortableContext,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  Button,
  Pagination,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
  TableSkeleton,
} from '@libs/ui'
import { Toolbar } from './toolbar'
import { DraggableTableHeader } from './utils'
import { columnsWithStaticOrder } from './workroom-columns'
import { useWorkroomTable } from '../hooks/use-table'
import { useLocation } from 'react-router'
import { useEffect } from 'react'
import { usePresets } from '../hooks'
import { ContainersDrawer } from '../../containers/components/containers-drawer'

const fallbackContentClassName =
  'absolute left-0 top-0 z-10 flex justify-center items-center gap-2 w-full h-full bg-white text-muted-foreground'

export function DataTable() {
  const { currentPreset } = usePresets()
  const {
    table,
    tableRef,
    workroomFields,
    isPending,
    isError,
    currentPage,
    totalPages,
    totalItems,
    handlePageChange,
    clearFilters,
    handleDragEnd,
    sensors,
    columnOrder,
    LIMIT,
  } = useWorkroomTable()

  const location = useLocation()

  useEffect(() => {
    handlePageChange(0)
  }, [location.pathname])

  useEffect(() => {
    if (currentPreset?.name) {
      table.resetRowSelection()
    }
  }, [currentPreset?.name, table])

  return (
    <>
      <Toolbar
        table={table}
        workroomFields={workroomFields || []}
        isLoading={isPending}
      />

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        modifiers={[restrictToHorizontalAxis]}
        onDragEnd={handleDragEnd}
        cancelDrop={(event) => {
          return columnsWithStaticOrder.includes(event.over?.id as string)
        }}
      >
        <div
          ref={tableRef}
          className="table-wrapper mb-4 relative h-[calc(100vh_-_16rem)] overflow-auto border rounded-md shadow-sm bg-white"
        >
          {isPending ? (
            <TableSkeleton />
          ) : isError ? (
            <div className={fallbackContentClassName}>
              <span>{t('errors.errorFetchingData')}</span>
            </div>
          ) : !table.getRowModel().rows.length ? (
            <div className={fallbackContentClassName}>
              <div className="text-center">
                <div className="div">{t('noResultsFound')}</div>
                <div className="mb-2">{t('tryDifferentFilter')}</div>
                <Button
                  onClick={() => clearFilters()}
                  dataTestId="clear-filters"
                >
                  {t('clearFilters')}
                </Button>
              </div>
            </div>
          ) : (
            <div>
              <Table>
                <TableHeader className="border-0 text-sm bg-white sticky top-0 z-10 shadow-[0_1px_0_rgba(0,0,0,1)]">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={crypto.randomUUID()} className="border-b-0">
                      <SortableContext
                        items={columnOrder}
                        strategy={horizontalListSortingStrategy}
                      >
                        {headerGroup.headers.map((header) => (
                          <DraggableTableHeader
                            key={crypto.randomUUID()}
                            header={header}
                          />
                        ))}
                      </SortableContext>
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.column.id} className="px-2 py-4">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </DndContext>
      {isPending ? (
        <Skeleton className="h-4 w-full" />
      ) : (
        <Pagination
          selected={table?.getSelectedRowModel()?.rows?.length}
          showSelected
          currentPage={currentPage}
          limit={LIMIT}
          total={totalItems}
          totalPages={totalPages}
          handlePageChange={handlePageChange}
        />
      )}
      <ContainersDrawer />
    </>
  )
}
