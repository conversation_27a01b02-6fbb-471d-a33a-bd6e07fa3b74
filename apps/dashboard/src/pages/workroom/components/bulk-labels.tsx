import * as React from 'react'
import { Plus } from 'lucide-react'
import {
  <PERSON>ge,
  Button,
  Checkbox,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@libs/ui'
import { useGetLabels } from '../../../api/use-cases-api'
import { useEffect } from 'react'
import { t } from 'i18next'

interface BulkLabelsProps {
  selectedLabels: string[]
  setSelectedLabels: React.Dispatch<React.SetStateAction<string[]>>
}
export const BulkLabels = ({
  selectedLabels,
  setSelectedLabels,
}: BulkLabelsProps) => {
  const { data } = useGetLabels()
  const [accountLabels, setAccountLabels] = React.useState<string[]>(
    data?.labels || []
  )
  const [search, setSearch] = React.useState('')
  const [commandInputError, setCommandInputError] = React.useState('')

  const handleSelect = (currentValue: string) => {
    setSelectedLabels((prev) => {
      return prev.includes(currentValue)
        ? prev.filter((item) => item !== currentValue)
        : [...prev, currentValue]
    })
  }

  const handleCreateNew = () => {
    const newLabel = search.trim()
    if (newLabel.includes(' ')) {
      setCommandInputError(t('labelCannotContainSpaces'))
      return
    }
    setAccountLabels((prev) => [...prev, newLabel])
    setSelectedLabels((prev) => [...prev, newLabel])
    setSearch('')
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      handleCreateNew()
    }
  }

  useEffect(() => {
    if (data?.labels) {
      setAccountLabels(data?.labels || [])
    }
  }, [data?.labels])

  return (
    <div className="flex flex-wrap">
      <Command
        filter={(value, search) => {
          return value.toLocaleLowerCase().includes(search.toLowerCase())
            ? 1
            : 0
        }}
      >
        <CommandInput
          placeholder={t('searchLabels')}
          value={search}
          onValueChange={(value) => {
            setSearch(value)
            setCommandInputError('')
          }}
          onKeyDown={handleKeyDown}
        />
        {commandInputError && (
          <span className="p-2 text-destructive-foreground text-xs">
            {commandInputError}
          </span>
        )}
        <CommandEmpty>
          <Button
            variant="outline"
            size="sm"
            className="w-full justify-start"
            dataTestId="create-new-label"
            onClick={handleCreateNew}
          >
            <Plus className="mr-2 h-4 w-4" />
            <span className="capitalize mr-1">{t('create')}</span> "{search}"
          </Button>
        </CommandEmpty>
        <CommandGroup className="h-40 overflow-auto">
          {accountLabels.map((label) => (
            <CommandItem key={label} onSelect={() => handleSelect(label)}>
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2">
                  <Checkbox checked={selectedLabels.includes(label)} />
                  <Badge
                    key={crypto.randomUUID()}
                    variant="secondary"
                    className="rounded-md text-gray-400 border-gray-400 h-6 bg-white font-light gap-2"
                  >
                    {label}
                  </Badge>
                </div>
              </div>
            </CommandItem>
          ))}
        </CommandGroup>
      </Command>
    </div>
  )
}
