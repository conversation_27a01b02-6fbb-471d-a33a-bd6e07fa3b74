import {
  Button,
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  ScrollArea,
  ActiveFilterBadge,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@libs/ui'
import { Columns3 } from 'lucide-react'
import { t } from 'i18next'
import { useMemo, useState } from 'react'
import type { Table } from '@tanstack/react-table'
import type {
  ExternalCaseWorkroom,
  ProviderFieldInfoOptions,
} from 'prime-front-service-client'
import { columnOrderAtom, useFilters } from '../hooks'
import { useSelectedColumns } from '../hooks/use-selected-columns'
import { FilterDrawer } from './filter-drawer'
import { WorkroomBulkEdit } from './workroom-bulk-edit'
import { useAtom } from 'jotai'
import { ALLOWED_FILTERS } from '../utils'
import { FileDownloader } from './file-downloader'
import { SearchCases } from './search-cases'

interface ToolbarProps {
  table: Table<ExternalCaseWorkroom>
  workroomFields: ProviderFieldInfoOptions[]
  isLoading?: boolean
}

export const Toolbar = ({
  table,
  workroomFields = [],
  isLoading,
}: ToolbarProps) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)

  const { toggleSelectedColumn } = useSelectedColumns()

  const [columnOrder, setColumnOrder] = useAtom(columnOrderAtom)

  const { getFilter, deleteFilter } = useFilters()

  const allColumns = table
    .getAllColumns()
    .filter((column) => column.getCanHide())
    .map((column) => column.id)

  const allColumnsItems = table
    .getAllColumns()
    .filter((column) => column.getCanHide())

  const allWorkroomFields = useMemo(
    () =>
      workroomFields.filter((field) => {
        return allColumnsItems.find((column) => column.id === field.id)
      }),
    [workroomFields, allColumnsItems]
  )

  return (
    <div className="toolbar flex items-center justify-between mb-4 w-full">
      <div className="mr-4">
        <SearchCases />
      </div>
      <div className="flex items-start justify-between w-full gap-2">
        <div className="flex gap-3 flex-wrap">
          <ActiveFilterBadge
            title={t('tableColumns.fire_summary')}
            id={'fire_summary'}
            getFilter={getFilter}
            deleteFilter={deleteFilter}
          />

          {allColumns.includes('title') && (
            <ActiveFilterBadge
              title="title"
              id={'title'}
              getFilter={getFilter}
              deleteFilter={deleteFilter}
            />
          )}

          {allColumns.includes('issue_id') && (
            <ActiveFilterBadge
              title="issue id"
              id={'issue_id'}
              getFilter={getFilter}
              deleteFilter={deleteFilter}
            />
          )}

          {allColumns.includes('status') && (
            <ActiveFilterBadge
              title="Status"
              id={ALLOWED_FILTERS.status}
              getFilter={getFilter}
              deleteFilter={deleteFilter}
            />
          )}

          {allColumns.includes('risk_score') && (
            <ActiveFilterBadge
              title="risk score"
              id="risk_score"
              getFilter={getFilter}
              deleteFilter={deleteFilter}
            />
          )}

          <ActiveFilterBadge
            title="Confidence Level"
            id={ALLOWED_FILTERS.confidence_level}
            getFilter={getFilter}
            deleteFilter={deleteFilter}
          />

          {allColumns.includes('risk_score_category') && (
            <ActiveFilterBadge
              title="Risk Score Category"
              id={ALLOWED_FILTERS.risk_score_category}
              getFilter={getFilter}
              deleteFilter={deleteFilter}
            />
          )}

          {allColumns.includes('mitre_categories') && (
            <ActiveFilterBadge
              title="MITRE ATT&CK"
              id={'mitre_categories'}
              getFilter={getFilter}
              deleteFilter={deleteFilter}
            />
          )}

          {allColumns.includes('linddun_categories') && (
            <ActiveFilterBadge
              title="LINDDUN"
              id={'linddun_categories'}
              getFilter={getFilter}
              deleteFilter={deleteFilter}
            />
          )}

          <ActiveFilterBadge
            title="Confidentiality Level"
            id={ALLOWED_FILTERS.confidentiality_level}
            getFilter={getFilter}
            deleteFilter={deleteFilter}
          />

          <ActiveFilterBadge
            title="Integrity Level"
            id={ALLOWED_FILTERS.integrity_level}
            getFilter={getFilter}
            deleteFilter={deleteFilter}
          />

          <ActiveFilterBadge
            title="Availability Level"
            id={ALLOWED_FILTERS.availability_level}
            getFilter={getFilter}
            deleteFilter={deleteFilter}
          />

          <ActiveFilterBadge
            title="Is Automated"
            id={ALLOWED_FILTERS.is_automated}
            getFilter={getFilter}
            deleteFilter={deleteFilter}
          />

          <ActiveFilterBadge
            title="Is Security Enhancement"
            id={ALLOWED_FILTERS.is_security_enhancement}
            getFilter={getFilter}
            deleteFilter={deleteFilter}
          />

          <ActiveFilterBadge
            title="Labels"
            id={ALLOWED_FILTERS.labels}
            getFilter={getFilter}
            deleteFilter={deleteFilter}
          />

          <ActiveFilterBadge
            title="Parent ID"
            id={ALLOWED_FILTERS.parent_id}
            getFilter={getFilter}
            deleteFilter={deleteFilter}
          />

          {allWorkroomFields.map((item) => {
            return (
              <ActiveFilterBadge
                key={item.id}
                title={item.name}
                id={item.id}
                isDate={item.type === 'date'}
                getFilter={getFilter}
                deleteFilter={deleteFilter}
              />
            )
          })}
        </div>

        <div className="flex items-center">
          <FileDownloader pathname="/cases/export" />
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="view-dropdown">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="icon"
                      dataTestId="view-dropdown"
                      disabled={isLoading}
                    >
                      <Columns3 />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <ScrollArea className="h-[400px]">
                      {allColumnsItems
                        .filter((column) => column.id !== 'select')
                        .map((column) => {
                          return (
                            <DropdownMenuCheckboxItem
                              key={column.id}
                              className="capitalize"
                              checked={column.getIsVisible()}
                              onCheckedChange={(value) => {
                                column.toggleVisibility(!!value)
                                toggleSelectedColumn(column.id, value)
                                setColumnOrder(
                                  columnOrder.filter((col) => col !== column.id)
                                )
                              }}
                            >
                              {(column.columnDef.meta as any)?.isDynamic
                                ? (column.columnDef.meta as any)?.title
                                : t(`tableColumns.${column.id}`)}
                            </DropdownMenuCheckboxItem>
                          )
                        })}
                    </ScrollArea>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="capitalize">{t('columns')}</div>
            </TooltipContent>
          </Tooltip>

          <div className="mr-2">
            <WorkroomBulkEdit table={table} />
          </div>
          <FilterDrawer
            isOpen={isDrawerOpen}
            onOpenChange={setIsDrawerOpen}
            table={table}
            workroomFields={workroomFields}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  )
}
