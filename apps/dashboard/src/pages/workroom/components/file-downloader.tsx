import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  TooltipTrigger,
  useUrlState,
} from '@libs/ui'
import { transformedSorting, useSorting, useFilters } from '../hooks'
import { toast } from 'sonner'
import { FileSpreadsheetIcon, Loader2 } from 'lucide-react'
import type { UrlStateProps } from '../hooks/use-selected-columns'
import { useUrlStateDefaultOptions } from '../hooks/use-selected-columns'
import { apiConfig } from '@libs/common'
import { forwardRef, useState } from 'react'
import { t } from 'i18next'

const { basePath } = apiConfig

const fakeColumns = ['select', 'is_automated']

interface FileDownloaderProps {
  pathname: string
}

export const FileDownloader = forwardRef<
  HTMLButtonElement,
  FileDownloaderProps
>(({ pathname }, ref) => {
  const [isLoading, setIsLoading] = useState(false)

  const { filters } = useFilters()
  const { sortPayload } = useSorting()
  const [urlState] = useUrlState<UrlStateProps>({}, useUrlStateDefaultOptions)

  const handleDownload = async () => {
    setIsLoading(true)

    toast.info('Downloading file...The operation may take some time')

    const sort = transformedSorting(sortPayload)

    const baseUrl = `${basePath}${pathname}`
    const params = new URLSearchParams()

    filters.forEach((filter) => {
      params.append('f', JSON.stringify(filter))
    })

    sort.forEach((s) => {
      params.append('s', s)
    })

    const { selected_columns } = urlState

    selected_columns.forEach((column: string) => {
      if (!fakeColumns.includes(column)) {
        params.append('sc', column)
      }
    })

    try {
      const response = await fetch(`${baseUrl}?${params.toString()}`, {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const blob = await response.blob()

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      const contentDisposition = response.headers.get('content-disposition')
      const filename = contentDisposition
        ? contentDisposition.split('filename=')[1].replace(/"/g, '')
        : 'export.csv'

      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Download failed:', error)
      toast.error('Failed to download file. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          ref={ref}
          onClick={handleDownload}
          variant="icon"
          disabled={isLoading}
          dataTestId="download-file-button"
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <FileSpreadsheetIcon />
          )}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <div>{t('exportToCsv')}</div>
      </TooltipContent>
    </Tooltip>
  )
})
