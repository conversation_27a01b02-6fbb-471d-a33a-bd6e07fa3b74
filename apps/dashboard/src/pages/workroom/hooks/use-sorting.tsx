import { useState, useCallback, useMemo, useEffect } from 'react'
import type { OnChangeFn, SortingState } from '@tanstack/react-table'
import { staticColumns } from '../components'
import { useUrlState } from '@libs/ui'
import type { UrlStateProps } from './use-selected-columns'
import { useUrlStateDefaultOptions } from './use-selected-columns'

export interface SortItem {
  field: string
  direction: 'asc' | 'desc'
}

export const transformedSorting = (sortPayload: SortItem[]) => {
  return sortPayload?.map((sort) => {
    if (!staticColumns.includes(sort.field)) {
      return JSON.stringify({
        ...sort,
        field: sort.field,
      })
    }

    if (sort.field === 'risk_score_category') {
      return JSON.stringify({
        ...sort,
        // TODO: Change to risk_score_category when the backend is ready
        field: 'risk_score',
      })
    } else if (sort.field === 'confidence_level') {
      return JSON.stringify({
        ...sort,
        // TODO: Change to confidence_level when the backend is ready
        field: 'confidence',
      })
    }
    return JSON.stringify(sort)
  })
}

export const useSorting = () => {
  const [state, setState] = useUrlState<UrlStateProps>(
    {},
    useUrlStateDefaultOptions
  )

  const sortPayload = useMemo((): SortItem[] => {
    return state.s || []
  }, [state.s])

  const nextSorting = useMemo(() => {
    return state.s
      ? state.s.map((item: SortItem) => ({
          id: item.field,
          desc: item.direction === 'desc',
        }))
      : []
  }, [state.s])

  const [sorting, setSorting] = useState<SortingState>(nextSorting)

  const updateSorting = useCallback(
    (newSortingFn: () => SortingState) => {
      const newSorting = newSortingFn()

      setSorting(newSorting)
      setState((prevState) => {
        const sortingToUpdate = newSorting.map((sort) => ({
          field: sort.id,
          direction: sort.desc ? 'desc' : 'asc',
        }))
        return {
          ...prevState,
          s: sortingToUpdate,
        }
      })
    },
    [setState]
  )

  useEffect(() => {
    setSorting(nextSorting)
  }, [nextSorting])

  return {
    sorting,
    setSorting: updateSorting as OnChangeFn<SortingState>,
    sortPayload,
  }
}
