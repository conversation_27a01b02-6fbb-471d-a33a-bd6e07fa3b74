import { useSearchParams } from 'react-router'

type QueryParams = {
  [key: string]: string | string[]
}

export const useQueryParams = () => {
  const [searchParams, setSearchParams] = useSearchParams()

  const getQueryParam = (key: string): string[] | string | null => {
    const param = searchParams.get(key)
    if (!param) return null
    return param.includes(',') ? param.split(',') : param
  }

  const setQueryParam = (key: string, value: string | string[]): void => {
    const newParams = new URLSearchParams(searchParams)
    if (Array.isArray(value)) {
      newParams.set(key, value.join(','))
    } else {
      newParams.set(key, value)
    }
    setSearchParams(newParams)
  }

  const deleteQueryParam = (key: string): void => {
    const newParams = new URLSearchParams(searchParams)
    newParams.delete(key)

    setSearchParams(newParams)
  }

  const deleteAllQueryParams = (): void => {
    const newParams = new URLSearchParams()
    setSearchParams(newParams)
  }

  const getAllQueryParams = (): QueryParams => {
    const params: QueryParams = {}
    for (const [key, value] of searchParams.entries()) {
      params[key] = value.includes(',') ? value.split(',') : [value]
    }
    return params
  }

  return {
    getQueryParam,
    setQueryParam,
    deleteQueryParam,
    deleteAllQueryParams,
    getAllQueryParams,
    searchParams,
    setSearchParams,
  }
}
