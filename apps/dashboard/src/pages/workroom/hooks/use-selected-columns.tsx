import type { FilterItem, UseUrlStateOptions } from '@libs/ui'
import { useUrlState } from '@libs/ui'
import type { SortItem } from './use-sorting'

export interface UrlStateProps {
  f?: FilterItem[]
  s?: SortItem[]
  view?: string
  selected_columns?: string[]
}

export const useUrlStateDefaultOptions = {
  parseOptions: {
    arrayFormat: 'bracket-separator',
  },
  stringifyOptions: {
    arrayFormat: 'bracket-separator',
  },
} as UseUrlStateOptions

export const useSelectedColumns = () => {
  const [state, setState] = useUrlState<UrlStateProps>(
    {},
    useUrlStateDefaultOptions
  )

  const toggleSelectedColumn = (columnId: string, isVisible: boolean) => {
    setState((prevState: UrlStateProps) => {
      const selectedColumns = prevState?.selected_columns || []
      const set = new Set(selectedColumns)

      if (isVisible) {
        set.add(columnId)
      } else {
        set.delete(columnId)
      }

      const newSelectedColumns = Array.from(set)
      return {
        ...prevState,
        selected_columns: newSelectedColumns.length
          ? newSelectedColumns
          : undefined,
      }
    })
  }

  const addSelectedColumns = (columnIds: string[]) => {
    if (!columnIds.length) return

    setState((prevState: UrlStateProps) => {
      const selectedColumns = prevState.selected_columns || []
      const set = new Set(selectedColumns)

      columnIds.forEach((columnId) => {
        set.add(columnId)
      })

      const newSelectedColumns = Array.from(set)

      return {
        ...prevState,
        selected_columns: newSelectedColumns.length
          ? newSelectedColumns
          : undefined,
      }
    })
  }

  return {
    selected_columns: state?.selected_columns || [],
    addSelectedColumns,
    toggleSelectedColumn,
  }
}
