import { useInfiniteQuery } from '@tanstack/react-query'
import { casesAPI } from '../../../api/clients'
import { t } from 'i18next'
import { toast } from 'sonner'
import { useGetWorkroomFields } from '../../../api/use-config-api'
import {
  transformedSorting,
  useFilters,
  useSorting,
  useWorkroomColumns,
} from '../hooks'
import { refetchCasesAtom } from '../../../api/use-cases-api'
import { atom, useAtom, useAtomValue } from 'jotai'
import { useEffect, useMemo, useRef } from 'react'
import {
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import {
  useSensors,
  useSensor,
  MouseSensor,
  TouchSensor,
  KeyboardSensor,
} from '@dnd-kit/core'
import type { PaginationResponseExternalCaseWorkroom } from 'prime-front-service-client'

const LIMIT = 20
export const currentPageAtom = atom(0)

export const useWorkroomTable = () => {
  const tableRef = useRef<HTMLDivElement>(null)

  const { stringifiedFilters, clearFilters } = useFilters()
  const { sorting, setSorting, sortPayload } = useSorting()
  const refetchCasesValue = useAtomValue(refetchCasesAtom)
  const [currentPage, setCurrentPage] = useAtom(currentPageAtom)

  const { data: workroomFields } = useGetWorkroomFields(false)

  useEffect(() => {
    setCurrentPage(0)
  }, [stringifiedFilters])

  const { data, isPending, isError } =
    useInfiniteQuery<PaginationResponseExternalCaseWorkroom>({
      queryKey: [
        'getCasesForAccount',
        sortPayload,
        stringifiedFilters,
        refetchCasesValue,
        currentPage,
      ],
      queryFn: async () => {
        const transformedSortingPayload = transformedSorting(sortPayload)

        const filterParam = stringifiedFilters.length
          ? [...stringifiedFilters]
          : []

        try {
          return await casesAPI.getCasesForAccount({
            limit: LIMIT,
            offset: currentPage * LIMIT,
            s: transformedSortingPayload.length
              ? transformedSortingPayload
              : undefined,
            f: filterParam.length ? filterParam : undefined,
          })
        } catch (error) {
          toast.error(t('errors.failedToFetchCases'))
          throw new Error(t('errors.failedToFetchCases'))
        }
      },
      getNextPageParam: (lastPage, allPages) => {
        const totalFetched = allPages.length * LIMIT
        return totalFetched < (lastPage?.total ?? 0)
          ? allPages.length
          : undefined
      },
      initialPageParam: 0,
    })

  const {
    columns,
    columnVisibility,
    setColumnVisibility,
    columnOrder,
    setColumnOrder,
    handleDragEnd,
  } = useWorkroomColumns(workroomFields || [])

  const flatData = useMemo(
    () => data?.pages?.flatMap((page) => page.results) ?? [],
    [data]
  )

  const table = useReactTable({
    data: flatData,
    columns: columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    manualSorting: true,
    state: {
      sorting,
      columnVisibility,
      columnOrder,
    },
    onColumnOrderChange: setColumnOrder,
  })

  const sensors = useSensors(
    useSensor(MouseSensor, {}),
    useSensor(TouchSensor, {}),
    useSensor(KeyboardSensor, {})
  )
  const handlePageChange = (newPage: number) => {
    if (
      newPage >= 0 &&
      newPage < Math.ceil((data?.pages?.[0]?.total ?? 0) / LIMIT)
    ) {
      table.resetRowSelection()
      setCurrentPage(newPage)
    }
  }

  return {
    table,
    tableRef,
    workroomFields,
    isPending,
    isError,
    clearFilters,
    handleDragEnd,
    sensors,
    columnOrder,
    currentPage,
    totalPages: Math.ceil((data?.pages?.[0]?.total ?? 0) / LIMIT),
    totalItems: data?.pages?.[0]?.total ?? 0,
    handlePageChange,
    LIMIT,
  }
}
