import { useMemo } from 'react'
import {
  useAddFilterPresets,
  useGetFilterPresets,
  useAddOrUpdateQueryCasesView,
} from '../../../api/use-config-api'
import { toast } from 'sonner'
import { t } from 'i18next'
import {
  defaultFilterParams,
  defaultSortParam,
  defaultSortParamString,
  getParsedArrayOfObjects,
  getStringifiedArrayOfObjects,
} from '../../../router/router'
import { defaultVisibleColumns } from './use-workroom-columns'
import type { FilterItem } from '@libs/ui'
import { useUrlState } from '@libs/ui'
import type { UrlStateProps } from './use-selected-columns'
import { useUrlStateDefaultOptions } from './use-selected-columns'
import { staticColumns } from '../components'
import { useAtom } from 'jotai'
import { defaultViewAtom } from '../../../config/store'

export const usePresets = () => {
  const useAddFilterPresetsMutation = useAddFilterPresets()
  const useUpdateFilterPresetsMutation = useAddOrUpdateQueryCasesView()
  const { data, refetch: refetchPresets } = useGetFilterPresets()
  const [state, setState] = useUrlState<UrlStateProps>(
    {},
    useUrlStateDefaultOptions
  )
  const [defaultView, setDefaultView] = useAtom(defaultViewAtom)

  const { f: filters, s: sorting, view: currentView, selected_columns } = state

  const currentPreset = useMemo(() => {
    if (state?.view === undefined) {
      return {
        query_id: undefined,
        name: 'default view',
      }
    } else {
      return data?.find((p) => p.query_id === state.view)
    }
  }, [state.view, data])

  const isCurrentViewPreset = data?.some(
    (preset) => preset.query_id === currentView
  )

  const addPreset = (name: string) => {
    const query_id = crypto.randomUUID().split('-')[0]

    useAddFilterPresetsMutation.mutate(
      {
        queryView: {
          name,
          query_id,
          query_list:
            getStringifiedArrayOfObjects(defaultFilterParams as never[]) || [],
          sort_list: [defaultSortParamString],
          selected_columns: Object.keys(defaultVisibleColumns),
        },
      },
      {
        onSuccess: async () => {
          toast.success(t('presetCreatedSuccessfully'))
          switchView(query_id)
          setState({
            view: query_id,
            s: [defaultSortParam],
            f: defaultFilterParams,
            selected_columns: Object.keys(defaultVisibleColumns),
          })
          await refetchPresets()
        },
        onError: () => {
          toast.error(t('errors.failedToCreatePreset'))
        },
      }
    )
  }

  const updatePreset = async (updatedFilters?: FilterItem[]) => {
    try {
      const presetData = {
        f: getStringifiedArrayOfObjects(updatedFilters || filters) || [],
        s: getStringifiedArrayOfObjects(sorting) || [],
        selected_columns,
      }

      if (!currentView) {
        setDefaultView(presetData)
      } else {
        await useUpdateFilterPresetsMutation.mutateAsync({
          queryView: {
            name: currentPreset?.name || '',
            query_id: currentView,
            query_list: presetData.f,
            sort_list: presetData.s,
            selected_columns: presetData.selected_columns,
          },
        })
      }

      await refetchPresets()
      toast.success(t('filtersSavedSuccessfully'))
    } catch (error) {
      console.error('Failed to update preset:', error)
      toast.error(t('errors.failedToSaveFilters'))
    }
  }

  const switchView = (viewId: string) => {
    if (viewId === 'default-view') {
      if (currentView) {
        setState((prevState) => {
          if (!defaultView) {
            return {
              ...prevState,
              f: defaultFilterParams,
              s: [defaultSortParam],
              selected_columns: staticColumns,
              view: undefined,
            }
          } else {
            return {
              ...prevState,
              f: getParsedArrayOfObjects(defaultView.f),
              s: getParsedArrayOfObjects(defaultView.s),
              selected_columns: defaultView.selected_columns,
              view: undefined,
            }
          }
        })
      }
    } else {
      const nextPreset = data?.find((p) => p.query_id === viewId)
      setState({
        f: getParsedArrayOfObjects(nextPreset?.query_list),
        s: getParsedArrayOfObjects(nextPreset?.sort_list ?? undefined),
        selected_columns: nextPreset?.selected_columns?.length
          ? nextPreset?.selected_columns
          : undefined,
        view: viewId,
      })
    }
  }

  return {
    presets: data || [],
    refetchPresets,
    currentView,
    currentPreset,
    isCurrentViewPreset,
    addPreset,
    updatePreset,
    isUpdatePresetPending: useUpdateFilterPresetsMutation.isPending,
    switchView,
  }
}
