/* eslint-disable max-lines */
import type { DesignDocResponse } from 'prime-front-service-client'
import { t } from 'i18next'
import {
  Link2Icon,
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  ExternalLink,
} from 'lucide-react'
import {
  Badge,
  Button,
  JiraIcn,
  Logo,
  PdfIcon,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@libs/ui'
import { Link, useNavigate } from 'react-router'
import { serializeWorkroom } from '../../../router/router-utils'
import { containersStaticColumns } from '../../containers/components/container-columns'
import { toast } from 'sonner'
import { apiConfig } from '@libs/common'
import { RulesManagement } from './rules-management'

export type SortField =
  | 'title'
  | 'created_by'
  | 'created_at'
  | 'updated_at'
  | 'issue_id'
export type SortDirection = 'asc' | 'desc'

const { basePath } = apiConfig

interface SortableHeaderProps {
  field: SortField
  label: string
  currentSort: { field: SortField; direction: SortDirection }
  onSort: (field: SortField) => void
}

const SortableHeader = ({
  field,
  label,
  currentSort,
  onSort,
}: SortableHeaderProps) => {
  const isActive = currentSort.field === field

  return (
    <div className="flex items-center">
      <span>{label}</span>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => onSort(field)}
        dataTestId={`sort-${field}`}
      >
        {isActive ? (
          currentSort.direction === 'asc' ? (
            <ArrowUp className="h-4 w-4 text-teal-600" />
          ) : (
            <ArrowDown className="h-4 w-4 text-teal-600" />
          )
        ) : (
          <ArrowUpDown className="h-4 w-4 text-gray-400" />
        )}
      </Button>
    </div>
  )
}

interface DesignReviewsTableProps {
  designDocs: DesignDocResponse[] | undefined
  isPending: boolean
  sortConfig: { field: SortField; direction: SortDirection }
  onSort: (field: SortField) => void
  selectedView: string
  isJiraProjects?: boolean
}

export const DesignReviewsTable = ({
  designDocs,
  isPending,
  sortConfig,
  onSort,
  selectedView,
  isJiraProjects = false,
}: DesignReviewsTableProps) => {
  const navigate = useNavigate()

  const handleDesignReviewSource = async (document: DesignDocResponse) => {
    if (document.url) {
      window.open(document.url, '_blank')
    } else if (document.issue_id) {
      navigate({
        pathname: '/containers',
        search: serializeWorkroom({
          view: selectedView,
          selected_columns: containersStaticColumns,
          opened_item: {
            issue_id: document.issue_id,
            source_id: document?.source_id || 0,
          },
        }),
      })
    } else if (document.file_origin_id) {
      const baseUrl = `${basePath}/design-docs/download/${document.id}`
      try {
        window.open(baseUrl, '_blank')
      } catch (error) {
        toast.error('Failed to fetch file. Please try again.')
      }
    }
  }

  return (
    <div
      className="table-wrapper overflow-x-auto"
      data-testid="design-reviews-table"
    >
      <Table>
        <TableHeader className="shadow-[0_1px_0_rgba(0,0,0,1)]">
          <TableRow className="text-sm">
            <TableHead className="py-3.5 px-6 font-bold capitalize">
              <SortableHeader
                field="title"
                label={t('securityReview')}
                currentSort={sortConfig}
                onSort={onSort}
              />
            </TableHead>
            <TableHead className="py-3.5 px-6 font-bold capitalize">
              {t('designReviewType')}
            </TableHead>
            <TableHead className="py-3.5 px-6 font-bold capitalize">
              {t('source')}
            </TableHead>
            <TableHead className="py-3.5 px-6 font-bold capitalize">
              {t('status')}
            </TableHead>
            <TableHead className="py-3.5 px-6 font-bold">
              <SortableHeader
                field="created_by"
                label="Upload By"
                currentSort={sortConfig}
                onSort={onSort}
              />
            </TableHead>
            {isJiraProjects && (
              <TableHead className="py-3.5 px-6 font-bold">
                <SortableHeader
                  field="issue_id"
                  label="Jira ID"
                  currentSort={sortConfig}
                  onSort={onSort}
                />
              </TableHead>
            )}

            <TableHead className="py-3.5 px-6 font-bold">
              <SortableHeader
                field="created_at"
                label="Created"
                currentSort={sortConfig}
                onSort={onSort}
              />
            </TableHead>
            <TableHead className="py-3.5 px-6 font-bold">
              <SortableHeader
                field="updated_at"
                label={t('lastUpdated')}
                currentSort={sortConfig}
                onSort={onSort}
              />
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isPending ? (
            [1, 2, 3, 4, 5].map((index) => (
              <TableRow key={index}>
                <TableCell className="py-6 pl-4">
                  <Skeleton className="h-5 w-32" />
                </TableCell>
                <TableCell className="p-6">
                  <Skeleton className="h-5 w-20" />
                </TableCell>
                <TableCell className="p-6">
                  <Skeleton className="h-5 w-20" />
                </TableCell>
                <TableCell className="p-6">
                  <Skeleton className="h-5 w-20" />
                </TableCell>
                <TableCell className="p-6">
                  <Skeleton className="h-5 w-20" />
                </TableCell>
                <TableCell className="p-6">
                  <Skeleton className="h-5 w-20" />
                </TableCell>
              </TableRow>
            ))
          ) : designDocs?.length ? (
            designDocs?.map((document, index) => (
              <TableRow key={index} className="text-slate-500 text-sm">
                <TableCell className="p-6">
                  <Link
                    to={`/design-reviews/${document.id}`}
                    className="hover:underline"
                    state={{ document }}
                  >
                    {document.title}
                  </Link>
                </TableCell>
                <TableCell className="p-6">
                  {document.doc_source_type === 'reference' ? (
                    <div className="flex items-center gap-1 uppercase">
                      <JiraIcn className="text-jira h-5 w-5" />
                      {t('jira')}
                    </div>
                  ) : document.issue_id ? (
                    <div className="flex items-center gap-1">
                      <Logo className="h-5 w-5" />
                      {t('container')}
                    </div>
                  ) : document.url ? (
                    <div className="flex items-center gap-1 capitalize">
                      <Link2Icon className="h-5 w-5" />
                      {t('link')}
                    </div>
                  ) : (
                    <div className="flex items-center gap-1 uppercase">
                      <PdfIcon className="h-5 w-5" />
                      {t('pdf')}
                    </div>
                  )}
                </TableCell>
                <TableCell className="px-1">
                  <Button
                    dataTestId="design-review-source"
                    className="flex items-center gap-1 capitalize"
                    variant="ghost"
                    onClick={() => handleDesignReviewSource(document)}
                  >
                    <ExternalLink className="w-5 h-5" />
                    {t('source')}
                  </Button>
                </TableCell>
                <TableCell className="p-6">
                  <Badge className="capitalize border border-teal-700 text-teal-700 py-2 px-3 bg-gray-50 text-sm hover:bg-gray-100">
                    {Number(
                      document?.processing_status?.processing_progress_percent
                    ) < 100
                      ? t('processing')
                      : t('ready')}
                  </Badge>
                </TableCell>
                <TableCell className="p-6">{document.created_by}</TableCell>
                {isJiraProjects && (
                  <TableCell className="p-6">
                    <a
                      href={document.jira_issue_url || ''}
                      target="_blank"
                      rel="noreferrer"
                      className="underline"
                    >
                      {document.issue_id}
                    </a>
                  </TableCell>
                )}
                <TableCell className="p-6">
                  {document.created_at.toLocaleDateString()}
                </TableCell>
                <TableCell className="p-6">
                  {document.updated_at.toLocaleDateString()}
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-6">
                <div className="flex flex-col justify-center items-center p-4 min-h-[500px] text-muted-foreground">
                  {isJiraProjects ? (
                    <div>
                      <div className="mb-4">
                        <p className="">
                          {t('No Jira Project connected yet.')}
                        </p>
                        <p>{t('Start by adding your first project.')}</p>
                      </div>
                      <RulesManagement />
                    </div>
                  ) : (
                    <div>
                      <p className="">{t('No design reviews found.')}</p>
                      <p>{t('Start by creating your first design review.')}</p>
                    </div>
                  )}
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
