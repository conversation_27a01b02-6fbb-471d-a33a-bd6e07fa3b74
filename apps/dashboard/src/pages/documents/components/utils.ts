import type { FilterItem } from '@libs/ui'

export function mergeDuplicateFilters(filters: FilterItem[]): FilterItem[] {
  const mergedMap = new Map<string, FilterItem>()

  for (const filter of filters) {
    const key = `${filter.field}__${filter.op}`

    if (!mergedMap.has(key)) {
      mergedMap.set(key, { ...filter })
    } else {
      const existing = mergedMap.get(key)!
      const isArrayExistingValue = Array.isArray(existing.value)
      const isArrayFilterValue = Array.isArray(filter.value)
      if (isArrayExistingValue && isArrayFilterValue) {
        existing.value = Array.from(
          new Set([...existing.value, ...filter.value])
        )
      } else if (Array.isArray(existing.value)) {
        existing.value = Array.from(
          new Set([...existing.value, ...filter.value])
        )
      } else if (Array.isArray(filter.value)) {
        existing.value = Array.from(new Set([existing.value, ...filter.value]))
      } else {
        existing.value = Array.from(new Set([existing.value, filter.value]))
      }
    }
  }

  return Array.from(mergedMap.values())
}
