import {
  <PERSON><PERSON>,
  <PERSON>,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Skeleton,
} from '@libs/ui'
import { PlusIcon, SettingsIcon } from 'lucide-react'
import { t } from 'i18next'
import { useEffect, useState } from 'react'
import { useGetFilterPresets } from '../../../api/use-config-api'
import { RuleItem } from './rule-item'
import { atom } from 'jotai/index'
import { useAtom } from 'jotai'
import type { QueryView } from 'prime-front-service-client'
import { RuleForm } from './rule-form'

export const ruleToEditAtom = atom<QueryView | null>(null)
export const RulesManagement = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [ruleToEdit, setRuleToEdit] = useAtom(ruleToEditAtom)
  const [editMode, setEditMode] = useState(false)

  const { data, refetch, isPending } = useGetFilterPresets('design_reviews')

  useEffect(() => {
    if (ruleToEdit) {
      setEditMode(true)
    }
  }, [ruleToEdit])

  useEffect(() => {
    setEditMode(false)
    setRuleToEdit(null)
  }, [isOpen])

  const updateSuccess = () => {
    setRuleToEdit(null)
    setEditMode(false)
    refetch()
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          dataTestId="manage-projects-button"
          className="flex items-center gap-2 capitalize"
          variant="ghost"
        >
          <SettingsIcon className="w-4 h-4" />
          {t('advancedMonitoring')}
        </Button>
      </DialogTrigger>
      <DialogContent className="w-[750px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold capitalize">
            {t('advancedMonitoringBoard')}
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            {t('advancedMonitoringBoardDescription')}
          </DialogDescription>
        </DialogHeader>
        {editMode ? (
          <RuleForm
            rule={ruleToEdit}
            updateSuccess={updateSuccess}
            back={() => {
              setEditMode(false)
              setRuleToEdit(null)
            }}
          />
        ) : (
          <div>
            <div className="flex items-center justify-start">
              <Button
                dataTestId="add-rule"
                className="flex items-center gap-2 capitalize"
                onClick={() => setEditMode(true)}
              >
                <PlusIcon className="w-4 h-4" />
                {t('addRule')}
              </Button>
            </div>
            {!!data?.length && (
              <div className="grid grid-flow-col grid-cols-[repeat(2,_minmax(0,_1fr))_70px] gap-4 items-center justify-between p-4">
                <div className="capitalize font-medium">{t('ruleName')}</div>
                <div className="capitalize font-medium">{t('projects')}</div>
                <div></div>
                <div></div>
              </div>
            )}
            <div className="space-y-4">
              {isPending ? (
                <Card className="p-4 rounded-[20px]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Skeleton className="w-5 h-5" />
                      <Skeleton className="h-4 w-12" />
                      <Skeleton className="h-6 w-20 rounded-full" />
                    </div>
                    <Skeleton className="h-9 w-40" />
                  </div>
                </Card>
              ) : (
                data?.map((rule) => (
                  <RuleItem
                    key={crypto.randomUUID()}
                    ruleItem={rule}
                    updateSuccess={updateSuccess}
                  />
                ))
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
