/* eslint-disable max-lines */
import { cn } from '@libs/common'
import { Button, Checkbox } from '@libs/ui'
import { t } from 'i18next'
import { Loader2, X } from 'lucide-react'
import type React from 'react'
import { useEffect, useRef, useState } from 'react'
import { toast } from 'sonner'

interface DocumentUploadProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onUpload: (files: File[], processAsOne?: boolean) => void
  isPending?: boolean
  description?: string
  acceptedFileTypes?: string
  maxFileSize?: number
  maxFiles?: number
  showAnalyzeAsOne?: boolean
}

export const DocumentUpload = ({
  open,
  onOpenChange,
  onUpload,
  isPending = false,
  description = t('uploadPDFDocument'),
  acceptedFileTypes = '.pdf',
  maxFileSize = 24,
  maxFiles,
  showAnalyzeAsOne = false,
}: DocumentUploadProps) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [fileSizeError, setFileSizeError] = useState<string | null>(null)
  const [processAsOne, setProcessAsOne] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (open) {
      setFileSizeError(null)
    }
  }, [open])

  const validateFiles = (files: File[]) => {
    const validFiles: File[] = []
    const maxBytes = maxFileSize * 1024 * 1024

    for (const file of files) {
      if (file.size > maxBytes) {
        setFileSizeError(
          t('fileTooLarge', {
            name: file.name,
            size: `${maxFileSize}MB`,
          })
        )
      } else {
        validFiles.push(file)
      }
    }
    return validFiles
  }

  const checkDuplicate = (file: File) => {
    const isDuplicate = selectedFiles.some(
      (existingFile) => existingFile.name === file.name
    )
    if (isDuplicate) {
      toast.error(`File "${file.name}" already exists`)
      return true
    }
    return false
  }

  const handleUpload = () => {
    if (selectedFiles.length === 0) return
    onUpload(selectedFiles, processAsOne)
    setSelectedFiles([])
    setProcessAsOne(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    setFileSizeError(null)

    e.preventDefault()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files).filter((file) => {
      const fileType = acceptedFileTypes.split(',')
      return fileType.some((type) =>
        type.startsWith('.')
          ? file.name.toLowerCase().endsWith(type.toLowerCase())
          : file.type === type
      )
    })

    const validDroppedFiles = validateFiles(files)

    if (files.length > 0) {
      const newFiles = maxFiles
        ? validDroppedFiles.slice(0, maxFiles - selectedFiles.length)
        : validDroppedFiles

      const uniqueFiles = newFiles.filter((file) => !checkDuplicate(file))

      if (uniqueFiles.length > 0) {
        setSelectedFiles((prev) => [...prev, ...uniqueFiles])

        if (fileInputRef.current) {
          const dataTransfer = new DataTransfer()

          selectedFiles.forEach((file) => {
            dataTransfer.items.add(file)
          })

          uniqueFiles.forEach((file) => {
            dataTransfer.items.add(file)
          })

          fileInputRef.current.files = dataTransfer.files
        }
      }
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFileSizeError(null)

    const files = Array.from(e.target.files || [])
    if (files.length === 0) return

    const validFiles = validateFiles(files)

    const newFiles = maxFiles
      ? validFiles.slice(0, maxFiles - selectedFiles.length)
      : validFiles

    const uniqueFiles = newFiles.filter((file) => !checkDuplicate(file))

    if (uniqueFiles.length > 0) {
      setSelectedFiles((prev) => [...prev, ...uniqueFiles])

      // Update file input so the internal FileList matches the selected files
      if (fileInputRef.current) {
        const dataTransfer = new DataTransfer()
        selectedFiles.forEach((file) => dataTransfer.items.add(file))
        uniqueFiles.forEach((file) => dataTransfer.items.add(file))
        fileInputRef.current.files = dataTransfer.files
      }
    }
  }

  const removeFile = (indexToRemove: number) => {
    setSelectedFiles((prev) => {
      const updated = prev.filter((_, index) => index !== indexToRemove)

      if (fileInputRef.current) {
        const dataTransfer = new DataTransfer()
        updated.forEach((file) => {
          dataTransfer.items.add(file)
        })
        fileInputRef.current.files = dataTransfer.files
      }

      return updated
    })
  }

  const isMaxFilesReached = maxFiles ? selectedFiles.length >= maxFiles : false

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  return (
    <div className="flex flex-col gap-4">
      <p className="text-sm text-muted-foreground">{description}</p>
      <div
        className={cn(
          'relative flex flex-col items-center justify-center rounded-lg border border-dashed p-12',
          isDragging && 'border-purple-500 bg-purple-50',
          'cursor-pointer hover:bg-accent/50',
          isMaxFilesReached && 'opacity-50 cursor-not-allowed'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          className="absolute inset-0 opacity-0 cursor-pointer"
          accept={acceptedFileTypes}
          ref={fileInputRef}
          onChange={handleFileSelect}
          multiple={!maxFiles || maxFiles > 1}
          disabled={isMaxFilesReached}
        />
        <Button
          dataTestId="browse-file-button"
          variant="outline"
          className="mb-2"
          disabled={isMaxFilesReached}
        >
          {t('browseFiles')}
        </Button>

        <p className="text-sm text-muted-foreground mb-1">
          {t('orDragThemHere')}
        </p>
        <p className="text-xs text-muted-foreground">
          {t('supportsFileTypes', { types: acceptedFileTypes })}{' '}
          {t('maxFileSize', { maxSize: maxFileSize })}
        </p>
        {maxFiles && (
          <p className="text-xs text-muted-foreground mt-1">
            {t('maxFiles', { count: maxFiles })}
          </p>
        )}
      </div>

      {fileSizeError && (
        <p className="text-sm text-red-500 px-2">{fileSizeError}</p>
      )}

      {selectedFiles.length > 0 && (
        <div className="mt-2 space-y-2">
          <p className="text-sm font-medium px-2">
            {selectedFiles.length} file(s) selected:
          </p>
          <div className="max-h-40 overflow-y-auto space-y-1">
            {selectedFiles.map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="flex items-center justify-between py-1 px-2 bg-accent/30 rounded"
              >
                <span className="text-sm truncate max-w-[240px]">
                  {file.name}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  dataTestId="remove-file-button"
                  onClick={() => removeFile(index)}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">{t('Remove')}</span>
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {showAnalyzeAsOne && (
        <div className="flex items-center space-x-2">
          <Checkbox
            id="process-as-one"
            checked={processAsOne}
            onCheckedChange={(checked) => setProcessAsOne(!!checked)}
            disabled={selectedFiles.length <= 1}
          />
          <label
            htmlFor="process-as-one"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {t('analyzeAsOne')}
          </label>
        </div>
      )}

      <div className="flex justify-end gap-2">
        <Button
          dataTestId="cancel-upload-button"
          variant="outline"
          onClick={() => {
            setSelectedFiles([])
            onOpenChange(false)
          }}
        >
          {t('cancel')}
        </Button>
        <Button
          dataTestId="confirm-upload-button"
          disabled={selectedFiles.length === 0 || isPending}
          onClick={handleUpload}
        >
          {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {t('upload')}{' '}
          {selectedFiles.length > 0 && `(${selectedFiles.length})`}
        </Button>
      </div>
    </div>
  )
}
