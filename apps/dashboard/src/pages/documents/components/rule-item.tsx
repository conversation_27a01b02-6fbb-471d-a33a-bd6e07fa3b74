import { use<PERSON>et<PERSON><PERSON> } from 'jotai/index'
import { toast } from 'sonner'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Badge,
  Button,
  Card,
  ClipLoader,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@libs/ui'
import { t } from 'i18next'
import { PencilIcon, Trash2 } from 'lucide-react'
import type { QueryView } from 'prime-front-service-client'
import { useDeleteCasesView } from '../../../api/use-config-api'
import { ruleToEditAtom } from './rules-management'
import { getParsedArrayOfObjects } from '../../../router/router'
import { useMemo } from 'react'

interface ListWithOverflowProps {
  items: string[]
  maxVisible?: number
  renderItem?: (item: string) => React.ReactNode
}

export const ListWithOverflow = ({
  items,
  maxVisible = 1,
  renderItem = (item) => item,
}: ListWithOverflowProps) => {
  if (!items.length) return null

  if (items.length <= maxVisible) {
    return <>{renderItem(items[0])}</>
  }

  return (
    <div className="flex items-center gap-1">
      <span className="text-sm"> {renderItem(items[0])}</span>

      <Tooltip>
        <TooltipTrigger disabled>
          <Badge variant="secondary" className="cursor-pointer">
            +{items.length - maxVisible}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          {items.slice(maxVisible).map((item) => (
            <div key={crypto.randomUUID()}>{renderItem(item)}</div>
          ))}
        </TooltipContent>
      </Tooltip>
    </div>
  )
}
interface RuleItemProps {
  ruleItem: QueryView
  updateSuccess: () => void
}
export const RuleItem = ({ ruleItem, updateSuccess }: RuleItemProps) => {
  const setRuleToEdit = useSetAtom(ruleToEditAtom)
  const { mutate: mutateDelete, isPending: pendingDelete } =
    useDeleteCasesView()

  const handleDelete = () => {
    mutateDelete(
      {
        query_id: ruleItem.query_id,
        view_type: 'design_reviews',
      },
      {
        onSuccess: () => {
          updateSuccess()
          toast.success('Deleted rule successfully')
        },
        onError: () => {
          toast.error('Failed to delete rule')
        },
      }
    )
  }

  const ruleProjects = useMemo(() => {
    return (
      getParsedArrayOfObjects(ruleItem.query_list)?.find(
        (field) => field?.field === 'provider_fields.project'
      )?.value || ''
    )
  }, [ruleItem])

  const handleEdit = () => {
    setRuleToEdit(ruleItem)
  }

  return (
    <Card className="p-4 rounded-[20px]">
      <div className="grid grid-flow-col grid-cols-[repeat(2,_minmax(0,_1fr))_70px] gap-4 items-center justify-between text-sm">
        <div>
          <h3 className="font-bold">{ruleItem.name}</h3>
        </div>

        <div className="grid items-center grid-flow-col gap-2 text-sm">
          {!!ruleProjects.length && <ListWithOverflow items={ruleProjects} />}
        </div>

        <div className="flex items-center">
          <Button
            onClick={handleEdit}
            dataTestId="edit-rule-button"
            variant="ghost"
            size="icon"
          >
            <PencilIcon className="w-5 h-5" />
          </Button>
          {
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  dataTestId="delete-rule-button"
                >
                  <Trash2 className="w-5 h-5 text-gray-500" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>
                    {t('areYouAbsolutelySure')}?
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    {t('thisActionCannotBeUndone')}.{' '}
                    {t('thisWillPermanentlyDeleteRule')}.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                  <AlertDialogAction asChild>
                    <Button
                      variant="destructive"
                      className="bg-destructive hover:bg-destructive/80 flex items-center gap-2"
                      dataTestId="delete-notification"
                      onClick={handleDelete}
                      disabled={pendingDelete}
                    >
                      {t('delete')}
                      {pendingDelete && (
                        <div className="flex ml-2">
                          <ClipLoader data-testid="loading-spinner" size={20} />
                        </div>
                      )}
                    </Button>
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          }
        </div>
      </div>
    </Card>
  )
}
