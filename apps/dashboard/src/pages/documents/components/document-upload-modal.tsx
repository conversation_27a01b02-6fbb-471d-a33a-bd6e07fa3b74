import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@libs/ui'
import { t } from 'i18next'
import { DocumentUpload } from './document-upload'

interface DocumentUploadModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onUpload: (files: File[], processAsOne?: boolean) => void
  isPending?: boolean
  title?: string
  description?: string
  acceptedFileTypes?: string
  maxFileSize?: number
  maxFiles?: number
}

export function DocumentUploadModal({
  open = false,
  onOpenChange,
  onUpload,
  isPending = false,
  title = t('addDocument'),
  description = t('uploadPDFDocument'),
  acceptedFileTypes = '.pdf',
  maxFileSize = 24,
  maxFiles,
}: DocumentUploadModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">{title}</DialogTitle>
        </DialogHeader>
        <DocumentUpload
          open={open}
          onOpenChange={onOpenChange}
          onUpload={onUpload}
          isPending={isPending}
          description={description}
          acceptedFileTypes={acceptedFileTypes}
          maxFileSize={maxFileSize}
          maxFiles={maxFiles}
        />
      </DialogContent>
    </Dialog>
  )
}
