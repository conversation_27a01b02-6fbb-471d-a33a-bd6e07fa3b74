/* eslint-disable max-lines */
import {
  <PERSON><PERSON>,
  Input<PERSON><PERSON>ch,
  PageTitle,
  <PERSON>bs,
  <PERSON>bsContent,
  <PERSON>bsList,
  <PERSON>bsTrigger,
} from '@libs/ui'
import { t } from 'i18next'
import { useDesignDocs } from '../../api/use-design-docs-api'
import { useState } from 'react'
import { NewDesignReview } from './components/new-design-review'
import { atom, useAtom } from 'jotai/index'
import { topContainersSelectedViewAtom } from '../../config/store'
import { DesignReviewsTable } from './components/design-reviews-table'
import type {
  SortDirection,
  SortField,
} from './components/design-reviews-table'
import { useQueryState } from 'nuqs'
import { useFlagsWrapper } from '../../hooks/use-flags-wrapper'
import { RulesManagement } from './components/rules-management'

const DEFAULT_SORT_FIELD = 'updated_at'
const DEFAULT_SORT_DIRECTION = 'desc'

type DesignDocsTab = 'default' | 'advanced-monitoring'

export const documentsModalAtom = atom<boolean>(false)

export const DocumentsPage = () => {
  const { jiraProjects } = useFlagsWrapper()

  const [selectedTab, setSelectedTab] = useQueryState('tab', {
    defaultValue: 'default' as DesignDocsTab,
  })
  const { data: designDocs, isPending } = useDesignDocs(
    selectedTab === 'advanced-monitoring' ? 'reference' : undefined
  )
  const [open, setOpen] = useAtom(documentsModalAtom)

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedView] = useAtom(topContainersSelectedViewAtom)

  const [sortConfig, setSortConfig] = useState<{
    field: SortField
    direction: SortDirection
  }>({
    field: DEFAULT_SORT_FIELD,
    direction: DEFAULT_SORT_DIRECTION,
  })

  const handleSort = (field: SortField) => {
    setSortConfig((prevConfig) => ({
      field,
      direction:
        prevConfig.field === field && prevConfig.direction === 'asc'
          ? 'desc'
          : 'asc',
    }))
  }

  const filteredDocs = designDocs?.results.filter((doc) => {
    const query = searchQuery.toLowerCase()
    return (
      doc.title.toLowerCase().includes(query) ||
      doc.created_by.toLowerCase().includes(query)
    )
  })

  const sortedDocs = filteredDocs?.slice().sort((a, b) => {
    const direction = sortConfig.direction === 'asc' ? 1 : -1

    switch (sortConfig.field) {
      case 'title':
        return direction * a.title.localeCompare(b.title)
      case 'created_by':
        return direction * a.created_by.localeCompare(b.created_by)
      case 'created_at':
        return direction * (a.created_at.getTime() - b.created_at.getTime())
      case 'updated_at':
        return direction * (a.updated_at.getTime() - b.updated_at.getTime())
      default:
        return 0
    }
  })

  return (
    <div className="bg-white border shadow rounded-2xl m-4">
      <div className="flex justify-between items-center px-6 border-b">
        <PageTitle title={t('securityReviews')} />

        <div className="flex items-center gap-4">
          <div className="relative">
            <InputSearch
              placeholder={t('Search documents...')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onClear={() => setSearchQuery('')}
              className="w-[200px]"
              data-testid="document-search-input"
            />
          </div>

          {selectedTab === 'default' && (
            <Button
              onClick={() => setOpen(true)}
              dataTestId="upload-document-button"
              className="flex items-center gap-2 capitalize"
            >
              {t('newSecurityReview')}
            </Button>
          )}
        </div>

        <NewDesignReview open={open} onOpenChange={setOpen} />
      </div>

      {jiraProjects ? (
        <Tabs
          defaultValue="default"
          className="p-6"
          onValueChange={(value) => setSelectedTab(value)}
          value={selectedTab}
        >
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="default">{t('onDemand')}</TabsTrigger>
              <TabsTrigger value="advanced-monitoring">
                Advanced Monitoring
              </TabsTrigger>
            </TabsList>
            {selectedTab === 'advanced-monitoring' && <RulesManagement />}
          </div>
          <TabsContent value="default">
            <div className="rounded-lg border bg-white mb-10 mt-4 ">
              <DesignReviewsTable
                designDocs={sortedDocs}
                isPending={isPending}
                sortConfig={sortConfig}
                onSort={handleSort}
                selectedView={selectedView}
              />
            </div>
          </TabsContent>
          <TabsContent value="advanced-monitoring">
            <div className="rounded-lg border bg-white mb-10 mt-4 ">
              <DesignReviewsTable
                designDocs={sortedDocs}
                isPending={isPending}
                sortConfig={sortConfig}
                onSort={handleSort}
                selectedView={selectedView}
                isJiraProjects
              />
            </div>
          </TabsContent>
        </Tabs>
      ) : (
        <div className="p-6">
          <div className="rounded-lg border bg-white mb-10 mt-4">
            <DesignReviewsTable
              designDocs={sortedDocs}
              isPending={isPending}
              sortConfig={sortConfig}
              onSort={handleSort}
              selectedView={selectedView}
            />
          </div>
        </div>
      )}
    </div>
  )
}
