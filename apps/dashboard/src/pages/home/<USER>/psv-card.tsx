import { t } from 'i18next'
import { usePsvCount } from '../../../api/use-psv-api'
import { cn } from '@libs/common'
import { ClipLoader } from '@libs/ui'
import React from 'react'
import { serializePsv } from '../../../router/router-utils'
import { defaultPsvFilterParams } from '../../../router/router'
import { NavLink } from 'react-router'

const baseClassName =
  'h-full border rounded-lg bg-white flex justify-center items-center'
export const PsvCard = () => {
  const { data, isPending, isError } = usePsvCount()

  if (isPending) {
    return (
      <div data-testid="view-shortcut-loader" className={cn(baseClassName)}>
        <ClipLoader />
      </div>
    )
  }

  if (isError) {
    return (
      <div className={cn(baseClassName)}>
        <h1 className="text-muted-foreground">
          {t('error')}: {t('somethingWentWrong')}
        </h1>
      </div>
    )
  }

  return (
    <div className="h-full overflow-y-auto border border-red-300  bg-white shadow-lg rounded-md py-6 px-8 flex flex-col">
      <h1 className="text-slate-950 font-bold text-lg mb-2">
        {t('securityViolations')}
      </h1>
      {data?.count && data?.by_type ? (
        <div className="flex-1 flex items-center justify-center text-center gap-x-8 mx-6 text-slate-600">
          <NavLink
            className="flex-col items-center justify-center"
            to={{
              pathname: '/psv',
              search: serializePsv({
                f: defaultPsvFilterParams,
              }),
            }}
          >
            <div className="text-6xl font-medium font-header">
              {data?.count}
            </div>
            <div className="font-light text-sm">Total Tickets</div>
          </NavLink>
          <div className="border border-zinc-200 flex items-center justify-around rounded-md py-4 px-3 flex-1">
            {data?.by_type &&
              Object.keys(data?.by_type).map((key) => {
                return (
                  key && (
                    <NavLink
                      to={{
                        pathname: '/psv',
                        search: serializePsv({
                          f: [
                            ...defaultPsvFilterParams,
                            {
                              field: 'type',
                              op: 'eq',
                              value: [key],
                            },
                          ],
                        }),
                      }}
                      key={crypto.randomUUID()}
                      className="flex-col items-center justify-center"
                    >
                      <div className="text-4xl font-header font-light">
                        {data?.by_type?.[key]}
                      </div>
                      <div className="font-light text-sm capitalize">
                        {key.replace(/_/g, ' ')}
                      </div>
                    </NavLink>
                  )
                )
              })}
          </div>
        </div>
      ) : (
        <div>
          <h1 className="text-muted-foreground mt-10">{t('noResultsFound')}</h1>
        </div>
      )}
    </div>
  )
}
