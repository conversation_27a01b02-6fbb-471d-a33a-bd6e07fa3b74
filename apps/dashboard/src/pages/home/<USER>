import { t } from 'i18next'
import {
  CasesChart,
  TopContainersCard,
  PsvCard,
  InsightsCard,
} from './components'
import {
  Button,
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
  ClipLoader,
  PageTitle,
} from '@libs/ui'
import { useGetAllSources } from '../../api/use-sources-api'
import { PlusCircle } from 'lucide-react'
import { NavLink } from 'react-router'

export const HomePage = () => {
  const { data, isPending } = useGetAllSources(1)

  if (isPending) {
    return (
      <div
        data-testid="loading-indicator"
        className="flex justify-center items-center min-h-96"
      >
        <ClipLoader />
      </div>
    )
  }

  return (
    <div className="h-full bg-white border shadow rounded-2xl m-4">
      <div className="border-b px-8">
        <PageTitle title={t('dashboard.title')} />
      </div>
      {data?.length ? (
        <div className="mx-auto mt-6 px-8 space-y-4 min-h-screen overflow-hidden max-w-[1400px]">
          <div className="grid w-full grid-cols-2 h-[600px] gap-4 mb-4 items-start max-w-[1500px]">
            <div className="h-[586px]">
              <TopContainersCard />
            </div>
            <div className="flex-col h-[580px] max-w-[700px]">
              <div className="h-[270px]">
                <PsvCard />
              </div>
              <div className="h-[300px] my-4">
                <CasesChart />
              </div>
            </div>
          </div>
          <div className="flex justify-center overflow-y-auto max-w-[1400px] h-[420px]">
            <InsightsCard />
          </div>
        </div>
      ) : (
        <div className="h-[calc(100vh_-_10rem)] flex justify-center items-center">
          <Card className="min-w-72 text-center">
            <CardHeader>
              <CardTitle className="capitalize">{t('noDataYet')}</CardTitle>
            </CardHeader>
            <CardContent className="text-muted-foreground">
              {t('connectASource')}
            </CardContent>
            <CardFooter className="flex justify-center items-center">
              <NavLink to="/settings/sources" className="cursor-pointer">
                <Button
                  dataTestId="add-source"
                  className="flex items-center gap-1"
                >
                  <PlusCircle />
                  {t('addSource')}
                </Button>
              </NavLink>
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  )
}
