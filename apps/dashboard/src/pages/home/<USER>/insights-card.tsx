import { t } from 'i18next'
import { InsightsTable } from './insights-table'
import {
  ClipLoader,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tabs,
  TabsList,
  TabsTrigger,
} from '@libs/ui'
import { cn } from '@libs/common'
import { useEffect } from 'react'
import { useGetFilterPresets } from '../../../api/use-config-api'
import {
  useGetCasesByLindunn,
  useGetCasesByMitre,
} from '../../../api/use-classifications-api'
import { useAtom } from 'jotai'
import {
  insightsSelectedTypeAtom,
  insightsSelectedViewAtom,
} from '../../../config/store'

const baseClassName =
  'border rounded-lg bg-white flex justify-center items-center'

export const InsightsCard = () => {
  const [type, setType] = useAtom(insightsSelectedTypeAtom)

  const { data: presets } = useGetFilterPresets('cases')

  const [selectedView, setSelectedView] = useAtom(insightsSelectedViewAtom)

  const {
    data: mitreCases,
    isFetching: isFetchingMitre,
    isPending: isPendingMitre,
    isError: isMitreError,
    refetch: refetchMitre,
  } = useGetCasesByMitre(selectedView !== 'default-view' ? selectedView : '')

  const {
    data: lindunnCases,
    isFetching: isFetchingLindunn,
    isPending: isPendingLindunn,
    isError: isLindunnError,
    refetch: refetchLindunn,
  } = useGetCasesByLindunn(selectedView !== 'default-view' ? selectedView : '')

  useEffect(() => {
    refetchMitre().then()
    refetchLindunn().then()
  }, [selectedView])

  if (
    isFetchingLindunn ||
    isFetchingMitre ||
    isPendingLindunn ||
    isPendingMitre
  ) {
    return (
      <div
        data-testid="insights-loader"
        className={cn(baseClassName, 'w-full')}
      >
        <ClipLoader />
      </div>
    )
  }

  if (isLindunnError || isMitreError) {
    return (
      <div className={cn(baseClassName)}>
        <h1 className="text-muted-foreground">
          {t('error')}: {t('somethingWentWrong')}
        </h1>
      </div>
    )
  }

  return (
    <div className="h-full w-full overflow-y-auto border border-slate-300  bg-white shadow-lg rounded-md py-6 px-6 flex flex-col">
      <h1 className="text-slate-950 font-bold text-lg mb-2 capitalize">
        {t('insights')}
      </h1>
      <div className="px-10">
        <div className="flex items-center justify-between my-2">
          <div className="flex items-center">
            <div className="flex flex-col gap-2">
              <Label className="ml-1 capitalize">{t('view')}</Label>
              <Select
                defaultValue={selectedView || 'default-view'}
                onValueChange={setSelectedView}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={'default-view'}>
                    {t('defaultView')}
                  </SelectItem>
                  {presets?.map((preset) => (
                    <SelectItem key={preset.query_id} value={preset.query_id}>
                      {preset.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <Tabs
            value={type}
            onValueChange={(value) => setType(value as 'mitre' | 'linddun')}
          >
            <TabsList>
              <TabsTrigger value="mitre" className="capitalize">
                {t('security')}
              </TabsTrigger>
              <TabsTrigger value="linddun" className="capitalize">
                {t('privacy')}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        {mitreCases?.categories && lindunnCases?.categories ? (
          <InsightsTable
            type={type}
            selectedPreset={presets?.find(
              (preset) => preset.query_id === selectedView
            )}
            categories={
              type === 'mitre' ? mitreCases.categories : lindunnCases.categories
            }
          />
        ) : (
          <div>
            <h1 className="text-muted-foreground mt-10">
              {t('noResultsFound')}
            </h1>
          </div>
        )}
      </div>
    </div>
  )
}
