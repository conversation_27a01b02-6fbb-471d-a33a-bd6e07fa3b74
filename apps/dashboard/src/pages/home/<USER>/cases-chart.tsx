import React, { useEffect } from 'react'
import colors from 'tailwindcss/colors'
import {
  <PERSON>lipLoader,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  TrendsChart,
} from '@libs/ui'
import { useGetCasesByStatus } from '../../../api/use-classifications-api'
import { ChevronDown } from 'lucide-react'
import { t } from 'i18next'
import { useAtom } from 'jotai'
import { caseChartDateRangeAtom } from '../../../config/store'

const wrapperClassName =
  'h-full flex justify-center items-center w-full rounded-md border bg-card shadow-sm capitalize'

export const CasesChart = () => {
  const [dateRange, setDateRange] = useAtom(caseChartDateRangeAtom)

  const getDateRange = (daysAgo: number) => {
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - daysAgo)
    return { startDate, endDate }
  }

  const keyToRangeMapping: Record<string, { startDate: Date; endDate: Date }> =
    {
      sevenDays: getDateRange(7),
      fourteenDays: getDateRange(14),
      thirtyDays: getDateRange(30),
    }

  const {
    data: trends,
    isFetching: isFetchingTrends,
    isError: isTrendsError,
    refetch: refetchTrends,
  } = useGetCasesByStatus(keyToRangeMapping[dateRange], 'total')

  useEffect(() => {
    refetchTrends().then()
  }, [dateRange, refetchTrends])

  const handleDateRangeChange = (rangeKey: string) => {
    setDateRange(rangeKey)
  }

  if (isFetchingTrends) {
    return (
      <div className={wrapperClassName}>
        <ClipLoader />
      </div>
    )
  }

  if (isTrendsError) {
    return (
      <div className={wrapperClassName}>
        <h1 className="text-muted-foreground">
          {t('error')}: {t('somethingWentWrong')}
        </h1>
      </div>
    )
  }

  return (
    <div className="py-6 px-10 w-full border border-slate-300 bg-white shadow-lg rounded-md capitalize h-[300px]">
      <div className="flex items-center justify-between">
        <h2 className="text-slate-950 font-bold text-lg">
          {t('opened')} <span className="lowercase">{t('and')}</span>{' '}
          {t('completed')} {t('cases')}
        </h2>
        <DropdownMenu>
          <DropdownMenuTrigger>
            <div className="flex items-center gap-1 capitalize font-semibold">
              <span>
                {t('last')} {t(dateRange)}
              </span>
              <ChevronDown />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem
              onSelect={() => handleDateRangeChange('sevenDays')}
            >
              {t('last')} {t('sevenDays')}
            </DropdownMenuItem>
            <DropdownMenuItem
              onSelect={() => handleDateRangeChange('fourteenDays')}
            >
              {t('last')} {t('fourteenDays')}
            </DropdownMenuItem>
            <DropdownMenuItem
              onSelect={() => handleDateRangeChange('thirtyDays')}
            >
              {t('last')} {t('thirtyDays')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div>
        <TrendsChart
          trends={[
            {
              data: trends?.close || [],
              id: 'completed',
              color: colors.teal['400'],
            },
            {
              data: trends?.identified || [],
              id: 'open',
              color: colors.rose['400'],
            },
          ]}
        />
      </div>
    </div>
  )
}
