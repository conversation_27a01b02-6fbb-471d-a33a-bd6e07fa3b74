import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@libs/ui'
import type { QueryView } from 'prime-front-service-client'
import { RiskScoreCategory } from 'prime-front-service-client'
import { cn } from '@libs/common'
import { NavLink } from 'react-router'
import { serializeWorkroom } from '../../../router/router-utils'
import {
  defaultFilterParams,
  getParsedArrayOfObjects,
} from '../../../router/router'
import { defaultViewAtom } from '../../../config/store'
import { useAtomValue } from 'jotai/index'
import { staticColumns } from '../../workroom/components'
import { useCallback } from 'react'
import { t } from 'i18next'

interface InsightsTableProps {
  type: 'mitre' | 'linddun'
  selectedPreset: QueryView | undefined
  categories: Record<string, { risk_scores: Record<string, number> }>
}

export const InsightsTable = ({
  type,
  categories,
  selectedPreset,
}: InsightsTableProps) => {
  const categoryNames = Object.keys(categories)

  const defaultView = useAtomValue(defaultViewAtom)

  const rowLabels = [
    RiskScoreCategory.intervene,
    RiskScoreCategory.analyze,
    RiskScoreCategory.monitor,
  ]

  const rowData = rowLabels.map((label) => {
    return categoryNames.map((category) => {
      const riskScores = categories[category]?.risk_scores || {}
      return riskScores[label as RiskScoreCategory] || 0
    })
  })

  const presetFilters = useCallback(
    (label: string, colIndex: number) => {
      const baseFilters = selectedPreset?.query_list?.length
        ? getParsedArrayOfObjects(selectedPreset?.query_list ?? []) || []
        : [...defaultFilterParams]

      const riskScoreFilter =
        label !== 'Total'
          ? [
              {
                field: 'risk_score_category',
                op: 'eq',
                value: [label],
              },
            ]
          : []

      const categoryFilter = {
        field: `${type}_categories`,
        op: 'eq',
        value: [categoryNames[colIndex]],
      }

      return [...baseFilters, ...riskScoreFilter, categoryFilter]
    },
    [selectedPreset, type, categoryNames]
  )

  return (
    <Table className="text-center my-2">
      <TableHeader>
        <TableRow className="border-none">
          {categoryNames.map((category) => (
            <TableHead
              key={category}
              className="capitalize text-center w-1/5 break-words text-slate-600 font-light"
              style={{ width: `${100 / categoryNames.length}%` }}
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="text-xs">{category}</span>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="w-[150px]">
                    {t(
                      `${
                        type.charAt(0).toUpperCase() + type.slice(1)
                      }.${category}`
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TableHead>
          ))}
          <TableHead className="w-[120px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {rowLabels.map((label, rowIndex) => (
          <TableRow className="border-white border-2" key={label}>
            {rowData[rowIndex].map((value, colIndex) => {
              let bgColor = ''
              if (rowIndex === 0) bgColor = 'bg-red-100'
              if (rowIndex === 1) bgColor = 'bg-orange-100'
              if (rowIndex === 2) bgColor = 'bg-yellow-50'

              return (
                <TableCell
                  key={colIndex}
                  className={cn(
                    bgColor,
                    'py-4 border-r-2 border-white text-xs font-semibold'
                  )}
                >
                  <NavLink
                    to={{
                      pathname: '/workroom',
                      search: serializeWorkroom({
                        ...(selectedPreset && {
                          view: selectedPreset?.query_id,
                        }),
                        f: presetFilters(label, colIndex),
                        selected_columns: selectedPreset
                          ? selectedPreset.selected_columns
                          : defaultView?.selected_columns || staticColumns,
                      }),
                    }}
                  >
                    {value}
                  </NavLink>
                </TableCell>
              )
            })}
            <TableCell className="capitalize text-slate-600 font-light text-xs">
              {label}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
