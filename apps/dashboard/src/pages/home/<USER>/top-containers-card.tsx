import { t } from 'i18next'
import {
  ClipLoader,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@libs/ui'
import { cn } from '@libs/common'
import {
  riskScoreClasses,
  containersStaticColumns,
} from '../../containers/components/container-columns'
import { useCallback, useEffect, useMemo } from 'react'
import type { ExternalCaseWorkroom } from 'prime-front-service-client'
import { useGetCasesForAccount } from '../../../api/use-cases-api'
import { useNavigate } from 'react-router'
import { serializeWorkroom } from '../../../router/router-utils'
import {
  getParsedArrayOfObjects,
  defaultContainersSortParam,
} from '../../../router/router'
import { useAtom, useAtomValue } from 'jotai'
import {
  topContainersSelectedViewAtom,
  defaultContainersViewAtom,
} from '../../../config/store'
import { usePresets } from '../../containers/hooks/use-presets'

const baseClassName =
  'h-full border rounded-lg bg-white flex justify-center items-center'

export const TopContainersCard = () => {
  const { presets, switchView } = usePresets()
  const defaultContainersView = useAtomValue(defaultContainersViewAtom)
  const [selectedView, setSelectedView] = useAtom(topContainersSelectedViewAtom)

  const defaultFilters = useMemo(
    () => [
      {
        field: 'provider_fields.issuetype',
        op: 'eq',
        value: ['Epic'],
      },
      {
        field: 'container',
        op: 'eq',
        value: ['True'],
      },
    ],
    []
  )

  const defaultSort = useMemo(() => [defaultContainersSortParam], [])

  const currentPresetFromStorage = presets?.find(
    (preset) => preset.query_id === selectedView
  )

  const filters = useMemo(() => {
    if (selectedView === 'default-view' || !selectedView) {
      return defaultContainersView?.f
        ? [
            ...(getParsedArrayOfObjects(defaultContainersView.f) || []),
            { field: 'container', op: 'eq', value: ['True'] },
          ]
        : defaultFilters
    }

    return currentPresetFromStorage?.query_list
      ? [
          ...(getParsedArrayOfObjects(currentPresetFromStorage.query_list) ||
            []),
          { field: 'container', op: 'eq', value: ['True'] },
        ]
      : defaultFilters
  }, [
    selectedView,
    currentPresetFromStorage,
    defaultContainersView,
    defaultFilters,
  ])

  const sort = useMemo(() => {
    if (selectedView === 'default-view' || !selectedView) {
      return defaultContainersView?.s
        ? getParsedArrayOfObjects(defaultContainersView.s)
        : defaultSort
    }

    return currentPresetFromStorage?.sort_list
      ? getParsedArrayOfObjects(currentPresetFromStorage.sort_list)
      : defaultSort
  }, [
    selectedView,
    currentPresetFromStorage,
    defaultContainersView,
    defaultSort,
  ])

  const {
    data: containers,
    isPending,
    isError,
  } = useGetCasesForAccount({
    limit: 10,
    offset: 0,
    filters: filters?.map((filter) => JSON.stringify(filter)),
    sort: sort?.map((sortItem) => JSON.stringify(sortItem)),
  })

  const getRiskScoreValue = useCallback(
    (container: ExternalCaseWorkroom) =>
      container.issue_analysis.risk_score ?? 0,
    []
  )

  const navigate = useNavigate()

  const handleViewChange = useCallback(
    (viewId: string) => {
      setSelectedView(viewId)
      switchView(viewId)
    },
    [setSelectedView, switchView]
  )

  useEffect(() => {
    if (!presets?.length) return

    const selectedPreset = presets.find(
      (preset) => preset.query_id === selectedView
    )

    if (!selectedPreset && selectedView !== 'default-view') {
      handleViewChange('default-view')
    }
  }, [handleViewChange, presets, selectedView])

  const handleContainersRedirect = useCallback(
    (container: ExternalCaseWorkroom) => {
      navigate({
        pathname: '/containers',
        search: serializeWorkroom({
          f: currentPresetFromStorage?.query_list
            ? getParsedArrayOfObjects(currentPresetFromStorage.query_list)
            : defaultContainersView?.f
            ? getParsedArrayOfObjects(defaultContainersView.f)
            : defaultFilters,
          s: currentPresetFromStorage?.sort_list
            ? getParsedArrayOfObjects(currentPresetFromStorage.sort_list)
            : defaultContainersView?.s
            ? getParsedArrayOfObjects(defaultContainersView.s)
            : defaultSort,
          selected_columns:
            currentPresetFromStorage?.selected_columns ||
            defaultContainersView?.selected_columns ||
            containersStaticColumns,
          opened_item: {
            issue_id: container.issue_id,
            source_id: container.source_id,
          },
          view: selectedView,
        }),
      })
    },
    [
      selectedView,
      currentPresetFromStorage,
      defaultContainersView,
      navigate,
      defaultFilters,
      defaultSort,
    ]
  )

  if (isPending) {
    return (
      <div data-testid="top-containers-loader" className={cn(baseClassName)}>
        <ClipLoader />
      </div>
    )
  }

  if (isError) {
    return (
      <div className={cn(baseClassName)}>
        <h1 className="text-muted-foreground">
          {t('error')}: {t('somethingWentWrong')}
        </h1>
      </div>
    )
  }

  return (
    <div className="h-full overflow-y-auto border border-slate-300 bg-white shadow-lg rounded-md pb-6 px-10">
      <div className="flex items-center justify-between">
        <h2 className="text-slate-950 font-bold text-lg mb-2 sticky top-0 bg-white z-10 pt-6 pb-4 -mx-2">
          {t('dashboard.containersTitle')}
        </h2>
        <Select value={selectedView} onValueChange={handleViewChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('defaultView')} />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="default-view">{t('defaultView')}</SelectItem>
              {presets?.map((preset) => (
                <SelectItem key={preset.query_id} value={preset.query_id}>
                  {preset.name}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
      <div data-testid="top-containers" className="space-y-3">
        {!!containers?.results?.length &&
          containers.results
            .filter((container) => !!container?.issue_analysis?.risk_score)
            .map((container) => (
              <div
                onClick={() => handleContainersRedirect(container)}
                data-testid="top-containers-button"
                key={container.issue_id}
                className="w-full py-2 cursor-pointer text-sm border border-slate-200 shadow-md flex items-center justify-between rounded-xl px-4 mb-2 gap-2 hover:underline"
              >
                <div className="text-slate-500 font-medium capitalize break-words">
                  {container.title}
                </div>
                {getRiskScoreValue(container) && (
                  <div
                    className={cn(
                      'border-2 font-bold rounded-full flex items-center justify-center w-8 h-8 flex-shrink-0',
                      riskScoreClasses(getRiskScoreValue(container))
                    )}
                  >
                    {getRiskScoreValue(container)}
                  </div>
                )}
              </div>
            ))}
      </div>
    </div>
  )
}
