import { t } from 'i18next'
import { Link } from 'react-router'
import { Button, ErrorSomethingWentWrong } from '@libs/ui'
import { UserRoundPlusIcon } from 'lucide-react'
import { UsersTable } from './components/users-table'
import { useGetUsers } from '../../../api/use-users-api'
import { UsersTableSkeleton } from './components/users-table-skeleton'

export const UsersManagementPage = () => {
  const { data, isPending, isRefetching, refetch, isError } = useGetUsers()

  if (isError) {
    return <ErrorSomethingWentWrong />
  }

  const isLoading = isPending || isRefetching

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <div className="page-title">
          <h1 className="text-2xl font-semibold capitalize">
            {t('usersManagement')}
          </h1>
          <p className="text-gray-400">
            {t('manageUsersAndTheirRolesInTheApplication')}
          </p>
        </div>

        <Link to="/settings/users-management/add-user">
          <Button
            variant="ghost"
            className="gap-2 capitalize"
            dataTestId="add-user-button"
            disabled={isLoading}
          >
            <UserRoundPlusIcon />
            {t('addTeamMembers')}
          </Button>
        </Link>
      </div>

      {isLoading ? (
        <UsersTableSkeleton />
      ) : (
        <UsersTable data={data} refetch={refetch} />
      )}
    </div>
  )
}
