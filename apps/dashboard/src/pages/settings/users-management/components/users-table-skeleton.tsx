import { Skeleton } from '@libs/ui'

export const UsersTableSkeleton = () => {
  return (
    <div className="rounded-md border">
      <div className="bg-gray-100/40 dark:bg-gray-800/40">
        <div className="grid grid-cols-[2fr_2fr_1fr_1fr_80px] px-4 py-5">
          <div>
            <Skeleton className="h-4 w-24" />
          </div>
          <div>
            <Skeleton className="h-4 w-24" />
          </div>
          <div>
            <Skeleton className="h-4 w-16" />
          </div>
          <div>
            <Skeleton className="h-4 w-20" />
          </div>
          <div>
            <Skeleton className="h-4 w-12" />
          </div>
        </div>
      </div>

      <div className="divide-y">
        {[1, 2, 3, 4].map((key) => (
          <div
            key={key}
            className="grid grid-cols-[2fr_2fr_1fr_1fr_80px] items-center px-4 py-6"
          >
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded-full" />
              <Skeleton className="h-4 w-32" />
            </div>
            <div>
              <Skeleton className="h-4 w-40" />
            </div>
            <div>
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>
            <div>
              <Skeleton className="h-4 w-24" />
            </div>
            <div>
              <Skeleton className="h-8 w-8 rounded-md" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
