import {
  useDeleteUser,
  useResetUserPassword,
} from '../../../../api/use-users-api'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@libs/ui'
import { KeyRound, Send, Trash2 } from 'lucide-react'
import { t } from 'i18next'
import { toast } from 'sonner'
import type { UserDataResponse } from 'prime-front-service-client'

interface UsersTableProps {
  data: UserDataResponse[]
  refetch: () => void
}

export const UsersTable = ({ data, refetch }: UsersTableProps) => {
  const deleteUserMutation = useDeleteUser()
  const resetUserPasswordMutation = useResetUserPassword()

  const handleDeleteUser = async (user_id: string) => {
    deleteUserMutation.mutate(
      { user_id },
      {
        onSuccess: () => {
          toast.success(t('userDeletedSuccessfully'))
          refetch()
        },
        onError: () => {
          toast.error(t('failedToDeleteUser'))
        },
      }
    )
  }

  const resetUserPassword = async (user_id: string) => {
    resetUserPasswordMutation.mutate(
      { user_id },
      {
        onSuccess: () => {
          toast.success(t('userResetSuccessfully'))
          refetch()
        },
        onError: () => {
          toast.error(t('failedToResetUser'))
        },
      }
    )
  }

  return (
    <div className="bg-white border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="p-4 capitalize">{t('fullName')}</TableHead>
            <TableHead className="p-4 capitalize">{t('email')}</TableHead>
            <TableHead className="p-4 "></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data &&
            data.map((user: UserDataResponse) => (
              <TableRow key={crypto.randomUUID()}>
                <TableCell className="font-medium p-4">
                  <div className="flex items-center capitalize">
                    {user.first_name} {user.last_name}
                  </div>
                </TableCell>
                <TableCell className="font-medium p-4">
                  {user.email_address}
                </TableCell>
                <TableCell className="p-4">
                  <div className="flex items-center justify-end gap-2">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          dataTestId="reset-password-button"
                        >
                          <KeyRound />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle className="capitalize">
                            {t('resetPassword')}
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            {t('resetPasswordDescription', {
                              email: `${user.email_address}`,
                            })}
                            .
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                          <AlertDialogAction
                            className="flex items-center gap-1"
                            onClick={() => resetUserPassword(user.user_id)}
                          >
                            <Send size={16} />
                            {t('sendResetLink')}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" dataTestId="delete-user-button">
                          <Trash2 />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            {t('areYouAbsolutelySure')}?
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            {t('thisActionCannotBeUndone')}.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                          <div>
                            <Button
                              onClick={() => handleDeleteUser(user.user_id)}
                              dataTestId="delete-user"
                              variant="destructive"
                            >
                              {t('delete')}
                            </Button>
                          </div>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </div>
  )
}
