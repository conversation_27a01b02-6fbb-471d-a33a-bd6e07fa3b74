import { t } from 'i18next'
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Input,
} from '@libs/ui'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Loader2, UserRoundPlusIcon } from 'lucide-react'
import React from 'react'
import { useInviteUser } from '../../../../api/use-users-api'
import { toast } from 'sonner'
import { useNavigate } from 'react-router'

const FormSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  emailAddress: z.string().email(),
})

export const AddUserPage = () => {
  const addUserMutation = useInviteUser()
  const navigate = useNavigate()

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      emailAddress: '',
    },
  })

  function onSubmit(data: z.infer<typeof FormSchema>) {
    addUserMutation.mutate(
      {
        email_address: data.emailAddress,
        first_name: data.firstName,
        last_name: data.lastName,
      },
      {
        onSuccess: () => {
          form.reset()
          toast.success('User added successfully')
          navigate('/settings/users-management')
        },
        onError: () => {
          toast.error('Failed to add user')
        },
      }
    )
  }

  return (
    <div className="max-w-[430px] mx-auto">
      <div className="bg-white p-8 mt-8 rounded-md ">
        <h2 className="text-3xl font-medium text-center mb-8">
          {t('inviteByEmail')}
        </h2>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="grid items-center gap-3 w-full mb-6"
          >
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      required
                      className="capitalize"
                      placeholder={t('enterFirstName')}
                      {...field}
                    />
                  </FormControl>

                  <FormMessage className="absolute" />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      required
                      className="capitalize"
                      placeholder={t('enterLastName')}
                      {...field}
                    />
                  </FormControl>

                  <FormMessage className="absolute" />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="emailAddress"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input required placeholder={t('enterEmail')} {...field} />
                  </FormControl>
                  <FormMessage className="absolute" />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              variant="default"
              className="capitalize gap-2 mt-4"
              dataTestId="invite-user-button"
              disabled={addUserMutation.isPending || !form.formState.isValid}
            >
              {addUserMutation.isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <UserRoundPlusIcon size={16} />
              )}
              {t('invite')}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  )
}
