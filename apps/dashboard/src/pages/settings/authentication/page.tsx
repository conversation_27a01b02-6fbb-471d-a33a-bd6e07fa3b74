import { t } from 'i18next'
import { SamlForm } from './saml-form/saml-form'

export const AuthenticationPage = () => {
  return (
    <div>
      <div className="page-title mb-8">
        <h1 className="text-2xl font-semibold mb-1 capitalize">
          {t('authentication')}
        </h1>
        <p className="text-gray-400">{t('setUpLoginPreferences')}</p>
      </div>
      <div className="flex-col items-center gap-2">
        <h1 className="text-lg text-slate-700 font-medium">
          {t('samlAuthentication')}
        </h1>
        <div className="text-gray-400 text-xs">
          {t('samlAuthenticationDescription')}
        </div>
      </div>
      <SamlForm />
    </div>
  )
}
