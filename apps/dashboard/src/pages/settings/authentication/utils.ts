export const convertFileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = (error) => reject(error)
  })
}

export const validateFileType = (fileList?: FileList) => {
  if (!fileList || fileList.length === 0) return true // No file uploaded, skip validation
  const file = fileList[0]
  const validTypes = [
    'application/x-x509-ca-cert',
    'application/pkix-cert',
    'application/x-pem-file',
  ]
  return (
    validTypes.includes(file.type) ||
    'Invalid certificate type. Please upload a .crt, .pem, or .cer file.'
  )
}

export const base64ToFile = (
  base64String: string,
  fileName: string,
  mimeType: string
): File => {
  const byteString = atob(base64String.split(',')[1]) // Decode the Base64 string
  const arrayBuffer = new ArrayBuffer(byteString.length)
  const uint8Array = new Uint8Array(arrayBuffer)

  for (let i = 0; i < byteString.length; i++) {
    uint8Array[i] = byteString.charCodeAt(i)
  }

  const blob = new Blob([arrayBuffer], { type: mimeType })
  return new File([blob], fileName, { type: mimeType })
}
