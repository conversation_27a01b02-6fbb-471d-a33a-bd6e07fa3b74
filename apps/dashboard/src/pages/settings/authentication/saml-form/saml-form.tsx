/*eslint-disable max-lines*/
import React, { useEffect, useState } from 'react'
import {
  useGetAccountSamlConfigurationDetails,
  useSetLoginSettings,
  useGetLoginSettings,
} from '../../../../api/use-config-api'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Button,
  Input,
  Label,
  Tabs,
  TabsList,
  TabsTrigger,
  Textarea,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  useCopy,
} from '@libs/ui'
import type { SubmitHandler } from 'react-hook-form'
import { useForm } from 'react-hook-form'
import { t } from 'i18next'
import { Check, Copy, Loader2 } from 'lucide-react'
import { toast } from 'sonner'
import type { SamlClientConfiguration } from 'prime-front-service-client/src/models/SamlClientConfiguration'
import { SamlFormSkeleton } from './saml-form-skeleton'

type SamlFormData = {
  acs_url: string
  entity_id: string
  signOnUrl: string
  issuer: string
  certificate?: string
  metaData?: string
}

export const SamlForm = () => {
  const { data, isPending, isError, error, isRefetching } =
    useGetLoginSettings()

  const setLoginSettings = useSetLoginSettings()
  const {
    register,
    setValue,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<SamlFormData>({ mode: 'onChange' })
  const { data: accountSamlDetails, refetch } =
    useGetAccountSamlConfigurationDetails()

  const { copyToClipboard: copyEntity, isCopied: isCopiedEntity } = useCopy()
  const { copyToClipboard: copyAcs, isCopied: isCopiedAcs } = useCopy()
  const { copyToClipboard: copyRelay, isCopied: isCopiedRelay } = useCopy()

  const [samlType, setSamlType] = useState<'simple' | 'xml'>(
    data?.saml?.auth_config?.certificate || !data?.saml?.auth_config
      ? 'simple'
      : 'xml'
  )
  const [xmlFileContent, setXmlFileContent] = useState<string | null>(null)

  useEffect(() => {
    if (data) {
      setValue('acs_url', data?.saml?.client_details?.acs_url || '')
      setValue('entity_id', data?.saml?.client_details?.entity_id || '')
      setValue('signOnUrl', data?.saml?.auth_config?.sign_on_url || '')
      setValue('issuer', data?.saml?.auth_config?.issuer || '')
      setValue('certificate', data?.saml?.auth_config?.certificate || '')
      setValue('metaData', data?.saml?.auth_config?.metadata || '')
    }
  }, [data, setValue])

  const onSubmit: SubmitHandler<SamlFormData> = async (formData) => {
    const formPayload = xmlFileContent
      ? ({ metadata: xmlFileContent } as SamlClientConfiguration)
      : ({
          sign_on_url: formData.signOnUrl,
          issuer: formData.issuer,
          certificate: formData.certificate,
        } as SamlClientConfiguration)

    await setLoginSettings.mutateAsync(
      {
        saml: {
          enabled: true,
          auth_config: formPayload,
        },
      },
      {
        onSuccess: async () => {
          toast.success(t('savedSamlLoginSuccessfully'))
          setSamlType('simple')
          await refetch()
        },
        onError: () => {
          toast.error(t('errors.failedSavingSamlLogin'))
        },
      }
    )
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = () => setXmlFileContent(reader.result as string)
      reader.readAsText(file)
    }
  }

  const deleteSaml = async () =>
    await setLoginSettings.mutateAsync(
      {
        saml: {
          enabled: false,
          auth_config: {
            sign_on_url: '',
            issuer: '',
            certificate: '',
          },
        },
      },
      {
        onSuccess: () => refetch(),
      }
    )

  if (isPending || isRefetching) {
    return <SamlFormSkeleton />
  }

  if (isError) {
    return <div>{error.message}</div>
  }

  return (
    <div className="mt-8 rounded-md mx-auto">
      <form onSubmit={handleSubmit(onSubmit)} className="w-2/3 space-y-6 mb-33">
        {accountSamlDetails && (
          <div>
            <div className="flex-col mb-3">
              <Label htmlFor="entity_id">{t('entityID')}</Label>
              <div className="relative">
                <Input
                  id="entity_id"
                  type="text"
                  placeholder={accountSamlDetails.entity_id || ''}
                  className="pr-10"
                  readOnly={!!accountSamlDetails.entity_id}
                />
                <Button
                  size="icon"
                  variant="ghost"
                  dataTestId="copy-entity-id"
                  onClick={async (e) => {
                    e.preventDefault()
                    await copyEntity(accountSamlDetails.entity_id)
                  }}
                  className="absolute right-0 top-0 h-full hover:bg-transparent"
                >
                  {isCopiedEntity ? (
                    <Check className="h- w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="flex-col">
              <Label htmlFor="acs_url">{t('acsURL')}</Label>
              <div className="relative">
                <Input
                  id="acs_url"
                  type="text"
                  placeholder={accountSamlDetails.acs_url || ''}
                  className="pr-10"
                  readOnly={!!accountSamlDetails.acs_url}
                />
                <Button
                  size="icon"
                  variant="ghost"
                  dataTestId="copy-acs-url"
                  onClick={async (e) => {
                    e.preventDefault()
                    await copyAcs(accountSamlDetails.acs_url)
                  }}
                  className="absolute right-0 top-0 h-full hover:bg-transparent"
                >
                  {isCopiedAcs ? (
                    <Check className="h- w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {data?.saml?.client_details?.relay_state && (
              <div className="flex-col mt-3">
                <Label>{t('relayState')}</Label>
                <div className="relative">
                  <Input
                    type="text"
                    placeholder={data?.saml?.client_details?.relay_state}
                    readOnly
                  />
                  <Button
                    size="icon"
                    variant="ghost"
                    dataTestId="copy-relay-state"
                    onClick={async (e) => {
                      e.preventDefault()
                      await copyRelay(
                        data?.saml?.client_details?.relay_state || ''
                      )
                    }}
                    className="absolute right-0 top-0 h-full hover:bg-transparent"
                  >
                    {isCopiedRelay ? (
                      <Check className="h- w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}

        <div className="flex mb-4">
          <Tabs
            value={samlType}
            onValueChange={(value) => {
              setSamlType(value as 'simple' | 'xml')
              setXmlFileContent(null)
            }}
          >
            <TabsList>
              <TabsTrigger value="simple" className="capitalize">
                {t('simple')} {t('view')}
              </TabsTrigger>
              <TabsTrigger value="xml">
                <span className="uppercase mr-1">{t('xml')}</span> {t('view')}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {samlType === 'simple' ? (
          <div>
            <div className="mb-2">
              <Tooltip>
                <TooltipTrigger>
                  <Label htmlFor="signOnUrl">{t('singleSignOn')}</Label>
                </TooltipTrigger>
                <TooltipContent>{t('ssoDescription')}</TooltipContent>
              </Tooltip>

              <Input
                id="signOnUrl"
                type="text"
                {...register('signOnUrl', {
                  required: 'Sign-On URL is required',
                })}
              />
              {errors.signOnUrl && (
                <span className="text-red-600 text-xs">
                  {errors.signOnUrl.message}
                </span>
              )}
            </div>

            <div className="mb-2">
              <Tooltip>
                <TooltipTrigger>
                  <Label htmlFor="issuer">{t('issuer')}</Label>
                </TooltipTrigger>
                <TooltipContent>{t('issuerDescription')}</TooltipContent>
              </Tooltip>

              <Input
                id="issuer"
                type="text"
                {...register('issuer', { required: 'Issuer is required' })}
              />
              {errors.issuer && (
                <span className="text-red-600 text-xs">
                  {errors.issuer.message}
                </span>
              )}
            </div>

            <div className="mb-2">
              <Tooltip>
                <TooltipTrigger>
                  <Label htmlFor="certificate">{t('certificate')}</Label>
                </TooltipTrigger>
                <TooltipContent>{t('certificateDescription')}</TooltipContent>
              </Tooltip>

              <Textarea
                id="certificate"
                rows={10}
                {...register('certificate', {
                  required: 'Certificate is required',
                })}
              />
              {errors.certificate && (
                <span className="text-red-600 text-xs">
                  {t('certificateIsRequired')}
                </span>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <Label htmlFor="xmlFile">{t('xmlFileUpload')}</Label>
            <Input
              id="xmlFile"
              type="file"
              accept=".xml"
              onChange={handleFileUpload}
            />
            {!xmlFileContent && (
              <span className="text-red-600 text-xs">
                {t('xmlFileRequired')}
              </span>
            )}
            {!!xmlFileContent && (
              <Textarea
                placeholder={xmlFileContent}
                className="pr-10 min-h-60"
                readOnly
              />
            )}
          </div>
        )}
        <Button
          type="submit"
          className="mr-2"
          dataTestId="save-saml-login"
          disabled={samlType === 'xml' ? !xmlFileContent : !isValid}
        >
          {t('save')}
          {setLoginSettings.isPending && (
            <div className="flex ml-2">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            </div>
          )}
        </Button>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              dataTestId="delete-button"
              variant="outline"
              className="capitalize"
            >
              {t('clear')}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{t('areYouAbsolutelySure')}?</AlertDialogTitle>
              <AlertDialogDescription>
                {t('thisActionCannotBeUndone')}.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction asChild>
                <Button dataTestId="delete-saml" onClick={deleteSaml}>
                  {t('confirm')}
                </Button>
              </AlertDialogAction>
              <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </form>
    </div>
  )
}
