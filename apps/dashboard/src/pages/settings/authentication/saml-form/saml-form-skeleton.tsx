import { Skeleton } from '@libs/ui'

export const SamlFormSkeleton = () => {
  return (
    <div className="mt-8 rounded-md mx-auto">
      <div className="w-2/3 space-y-6 mb-33">
        {/* Entity ID, ACS URL, Relay State section */}
        <div className="space-y-3">
          <div>
            <Skeleton className="h-4 w-20 mb-2" /> {/* Label */}
            <Skeleton className="h-10 w-full" /> {/* Input */}
          </div>
          <div>
            <Skeleton className="h-4 w-16 mb-2" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div>
            <Skeleton className="h-4 w-24 mb-2" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        {/* Toggle group */}
        <div>
          <Skeleton className="h-10 w-48 rounded-xl" />
        </div>

        {/* Form fields */}
        <div className="space-y-4">
          <div>
            <Skeleton className="h-4 w-28 mb-2" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div>
            <Skeleton className="h-4 w-16 mb-2" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div>
            <Skeleton className="h-4 w-24 mb-2" />
            <Skeleton className="h-40 w-full" />
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex gap-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
    </div>
  )
}
