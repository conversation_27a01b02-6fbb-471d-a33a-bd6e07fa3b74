import { RecommendationItem } from './components/recommendation-item'
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  ErrorSomethingWentWrong,
  InputSearch,
} from '@libs/ui'
import { useCustomRecommendations } from './hooks/use-custom-recommendations'
import { t } from 'i18next'
import {
  PlusIcon,
  UploadIcon,
  DownloadIcon,
  ArrowUpDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from 'lucide-react'
import { useState } from 'react'

import { ImportFileDialog } from './components/import-file-dialog'
import { CustomRecommendationsTableSkeleton } from './components/custom-recommendations-table-skeleton'
import type { CustomRecommendationOutput } from 'prime-front-service-client'
import type { Column, ColumnFiltersState } from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnDef,
  type SortingState,
} from '@tanstack/react-table'

interface SortButtonProps {
  title: string
  column: Column<CustomRecommendationOutput, unknown>
}
const SortButton = ({ title, column }: SortButtonProps) => {
  return (
    <Button
      variant="ghost"
      className="gap-2 font-semibold"
      dataTestId="sort-button"
      onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
    >
      {title}
      {column.getIsSorted() === 'asc' ? (
        <ArrowUpIcon className="w-4 h-4 text-muted-foreground" />
      ) : column.getIsSorted() === 'desc' ? (
        <ArrowDownIcon className="w-4 h-4 text-muted-foreground" />
      ) : (
        <ArrowUpDownIcon className="w-4 h-4 text-muted-foreground" />
      )}
    </Button>
  )
}

const columns: ColumnDef<CustomRecommendationOutput>[] = [
  {
    accessorKey: 'recommendation',
    cell: (info) => info.getValue(),
    header: ({ column }) => {
      return <SortButton title="Recommendation" column={column} />
    },
  },
  {
    accessorKey: 'tags',
    cell: (info) => info.getValue(),
    header: ({ column }) => {
      return <SortButton title="Tags" column={column} />
    },
    sortingFn: 'auto',
  },
  {
    accessorKey: 'updated_by', // edited by
    cell: (info) => info.getValue(),
    header: ({ column }) => {
      return <SortButton title="Edited by" column={column} />
    },
  },
  {
    accessorKey: 'updated_at', // edited
    cell: (info) => info.getValue(),
    header: ({ column }) => {
      return <SortButton title="Edited" column={column} />
    },
    sortingFn: 'datetime',
  },
  {
    accessorKey: 'created_at', // created
    cell: (info) => info.getValue(),
    header: ({ column }) => {
      return <SortButton title="Created" column={column} />
    },
    sortingFn: 'datetime',
  },
  {
    accessorKey: 'actions',
    header: () => '',
    cell: (info) => info.getValue(),
  },
]

export const CustomRecommendationsPage = () => {
  const {
    isLoading,
    isError,
    customRecommendations,
    editCustomRecommendation,
    deleteCustomRecommendation,
    availableTags,
    newRecommendation,
    handleAddNew,
    handleSaveNew,
    handleCancelNew,
  } = useCustomRecommendations()

  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])

  const table = useReactTable({
    data: customRecommendations,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  })

  const [openCsvModal, setOpenCsvModal] = useState(false)

  if (isError) {
    return <ErrorSomethingWentWrong />
  }

  return (
    <div className="custom-recommendation-page">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold mb-1">
          {t('customRecommendationsTitle')}
        </h1>
        <p className="text-gray-400">{t('customRecommendationsDescription')}</p>
      </div>
      <div className="flex justify-between items-center mb-4">
        <div className="relative">
          <InputSearch
            value={
              (table.getColumn('recommendation')?.getFilterValue() as string) ??
              ''
            }
            onChange={(event) =>
              table
                .getColumn('recommendation')
                ?.setFilterValue(event.target.value)
            }
            placeholder={`${t('search')}...`}
            className="bg-white"
            disabled={isLoading}
          />
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleAddNew}
            className="capitalize gap-1"
            disabled={!!newRecommendation || isLoading}
            dataTestId="add-new-recommendation-button"
          >
            <PlusIcon className="h-5 w-5" />
            {t('add')}
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                className="capitalize gap-1"
                variant="outline"
                dataTestId="import-csv-button"
                disabled={isLoading}
              >
                <UploadIcon className="h-5 w-5" />
                {t('Import')} {t('CSV')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end">
              <DropdownMenuItem
                onClick={() => setOpenCsvModal(true)}
                className="gap-2 cursor-pointer"
              >
                <UploadIcon className="h-4 w-4" />
                <span>
                  {t('Import')} {t('CSV')}
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <a
                  href="/custom-recommendation.csv"
                  download
                  className="w-full text-left capitalize flex items-center gap-2"
                >
                  <DownloadIcon className="h-4 w-4" />
                  {t('Download')} {t('Template')}
                </a>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="px-5 bg-white rounded-lg shadow overflow-hidden">
        {isLoading ? (
          <CustomRecommendationsTableSkeleton />
        ) : (
          <div className="grid grid-cols-[2.5fr_1fr_1fr_1fr_1fr_1fr]">
            <div className="col-span-full grid grid-cols-subgrid gap-4 items-center font-semibold p-4 text-sm border-b border-dashed capitalize">
              {table
                .getHeaderGroups()
                .map((headerGroup) =>
                  headerGroup.headers.map((header) => (
                    <div key={header.id}>
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                    </div>
                  ))
                )}
            </div>
            {newRecommendation && (
              <RecommendationItem
                onEdit={(recommendation) => handleSaveNew(recommendation)}
                onCancel={handleCancelNew}
                isEditing
                availableTags={availableTags || []}
                {...newRecommendation}
              />
            )}
            {table.getRowModel().rows.map((row) => {
              return (
                <RecommendationItem
                  onDelete={() => deleteCustomRecommendation(row.original.id)}
                  onEdit={(recommendation) =>
                    editCustomRecommendation(row.original.id, recommendation)
                  }
                  onCancel={undefined}
                  availableTags={availableTags || []}
                  key={crypto.randomUUID()}
                  {...row.original}
                />
              )
            })}
          </div>
        )}
      </div>

      <ImportFileDialog
        openCsvModal={openCsvModal}
        setOpenCsvModal={setOpenCsvModal}
      />
    </div>
  )
}
