import { t } from 'i18next'
import type { CustomRecommendationInput } from 'prime-front-service-client'
import { z } from 'zod'

const cleanValue = (value: string): string => {
  // Remove surrounding quotes and trim whitespace
  let cleaned = value.replace(/^["']|["']$/g, '')

  // Handle escaped quotes within JSON strings
  if (cleaned.startsWith('[')) {
    try {
      // Try to parse as JSON directly first
      JSON.parse(cleaned)
      return cleaned
    } catch {
      // If parsing fails, try to fix common CSV escaping issues
      cleaned = cleaned
        .replace(/\\"/g, '"') // Handle escaped quotes
        .replace(/"{2}/g, '"') // Handle double quotes
    }
  }

  return cleaned.trim()
}

const parseCSVLine = (line: string): string[] => {
  const values: string[] = []
  let currentValue = ''
  let inQuotes = false

  for (let i = 0; i < line.length; i++) {
    const char = line[i]

    if (char === '"' && line[i - 1] !== '\\') {
      inQuotes = !inQuotes
      continue
    }

    if (char === ',' && !inQuotes) {
      values.push(currentValue)
      currentValue = ''
      continue
    }

    currentValue += char
  }

  values.push(currentValue)
  return values
}

export const EXPECTED_HEADERS = ['recommendation', 'tags'] as const

export const validateHeaders = (headers: string[]): boolean => {
  return EXPECTED_HEADERS.every((expected) => headers.includes(expected))
}

export const csvSchema = z.object({
  recommendation: z.string().min(1),
  tags: z
    .union([
      z.string().transform((str) => {
        try {
          const parsed = JSON.parse(str)
          return Array.isArray(parsed) ? parsed : []
        } catch {
          return []
        }
      }),

      z.array(z.string()),
    ])
    .pipe(z.array(z.string())),
})

export const parseCSV = (
  csvContent: string,
  createdBy: string
): Partial<CustomRecommendationInput>[] => {
  const lines = csvContent.split('\n')
  const headers = parseCSVLine(lines[0]).map((header) => cleanValue(header))

  return lines
    .slice(1)
    .filter((line) => line.trim())
    .map((line) => {
      const values = parseCSVLine(line).map((value) => cleanValue(value))
      const record = headers.reduce((obj, header, index) => {
        obj[header] = values[index] || null
        return obj
      }, {} as Record<string, string | null>)

      const recordKeys = Object.keys(record)

      if (recordKeys.length > 2) {
        throw new Error(t('errors.invalidCSVFormat'))
      }

      if (recordKeys.includes('')) {
        throw new Error(t('errors.invalidCSVFormat'))
      }

      return {
        recommendation: record.recommendation || '',
        updated_at: new Date(),
        created_at: new Date(),
        updated_by: createdBy,
        created_by: createdBy,
        tags: record.tags
          ? cleanValue(record.tags)
              .split(',')
              .map((tag) => tag.trim())
          : [],
      } as CustomRecommendationInput
    })
}
