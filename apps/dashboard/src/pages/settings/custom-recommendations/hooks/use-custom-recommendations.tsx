import { useCallback, useMemo, useState } from 'react'
import { toast } from 'sonner'
import { t } from 'i18next'
import { useGetConfig, useUpdateConfig } from '../../../../api/use-config-api'
import type { CustomRecommendationInput } from 'prime-front-service-client'
import { useLocalStorage } from '@libs/ui'

export const useCustomRecommendations = () => {
  const [newRecommendation, setNewRecommendation] =
    useState<CustomRecommendationInput | null>(null)

  const [userInfo] = useLocalStorage<{ user_id: string } | null>(
    'userInfo',
    null
  )

  const { data, isPending, refetch, isRefetching, isError } = useGetConfig()

  const customRecommendations = useMemo(
    () => data?.custom_recommendations || [],
    [data]
  )

  const availableTags = useMemo(() => {
    const tags = customRecommendations.map((item) => item.tags).flat()
    return (
      Array.from(new Set(tags)).filter(
        (tag): tag is string => tag !== null && tag !== undefined
      ) || []
    )
  }, [customRecommendations])

  const configMutate = useUpdateConfig()

  const handleDelete = useCallback(
    (id: number) => {
      const newRecommendations = customRecommendations.filter(
        (item) => item.id !== id
      )

      configMutate.mutate(
        {
          AccountConfigUpdate: {
            ...data,
            custom_recommendations: newRecommendations,
          },
        },
        {
          onSuccess: async () => {
            await refetch()
            toast.success(t('recommendationDeletedSuccessfully'))
          },
          onError: async () => {
            toast.error(t('errors.failedToDeletedRecommendation'))
          },
        }
      )
    },
    [configMutate, customRecommendations, data, refetch]
  )

  const handleEdit = useCallback(
    (id: number, recommendation: CustomRecommendationInput) => {
      configMutate.mutate(
        {
          AccountConfigUpdate: {
            ...data,
            custom_recommendations: customRecommendations.map((item) =>
              item.id === id
                ? {
                    ...item,
                    ...recommendation,
                    updated_at: new Date(),
                    updated_by: userInfo?.user_id || '',
                  }
                : item
            ),
          },
        },
        {
          onSuccess: async () => {
            await refetch()
            toast.success(t('recommendationsUpdatedSuccessfully'))
          },
          onError: async () => {
            toast.error(t('errors.updatingRecommendationsFailed'))
          },
        }
      )
    },
    [configMutate, customRecommendations, data, refetch, userInfo?.user_id]
  )

  const updateCustomRecommendations = useCallback(
    (newRecommendations: CustomRecommendationInput[]) => {
      configMutate.mutate(
        {
          AccountConfigUpdate: {
            ...data,
            custom_recommendations: newRecommendations,
          },
        },
        {
          onSuccess: async () => {
            await refetch()
            toast.success(t('recommendationsImportedSuccessfully'))
          },
          onError: async () => {
            toast.error(t('errors.importingRecommendationsFailed'))
          },
        }
      )
    },
    [configMutate, data, refetch]
  )

  const createEmptyRecommendation = (): CustomRecommendationInput => ({
    recommendation: '',
    tags: [],
    updated_at: new Date(),
    created_at: new Date(),
    updated_by: userInfo?.user_id || '',
    created_by: userInfo?.user_id || '',
  })

  const handleAddNew = () => {
    const newRecommendation = createEmptyRecommendation()

    setNewRecommendation(newRecommendation)
  }

  const handleSaveNew = (rec: CustomRecommendationInput) => {
    if (rec.recommendation.trim()) {
      updateCustomRecommendations([rec, ...customRecommendations])
    }

    setNewRecommendation(null)
  }

  const handleCancelNew = () => {
    setNewRecommendation(null)
  }

  const isLoading = isPending || isRefetching || configMutate.isPending

  return {
    userInfo,
    customRecommendations,
    isError,
    isLoading,
    deleteCustomRecommendation: handleDelete,
    editCustomRecommendation: handleEdit,
    updateCustomRecommendations,
    createEmptyRecommendation,
    availableTags,
    newRecommendation,
    handleAddNew,
    handleSaveNew,
    handleCancelNew,
  }
}
