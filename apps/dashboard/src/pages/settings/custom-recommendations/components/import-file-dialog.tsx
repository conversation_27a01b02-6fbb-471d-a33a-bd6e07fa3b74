import {
  Button,
  <PERSON>alog,
  <PERSON><PERSON>Close,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@libs/ui'
import { t } from 'i18next'
import { FileTextIcon, UploadIcon } from 'lucide-react'
import {
  csvSchema,
  EXPECTED_HEADERS,
  parseCSV,
  validateHeaders,
} from '../utils/utils'
import { useRef } from 'react'
import { toast } from 'sonner'
import type { CustomRecommendationInput } from 'prime-front-service-client'
import { useCustomRecommendations } from '../hooks/use-custom-recommendations'

interface ImportFileDialogProps {
  openCsvModal: boolean
  setOpenCsvModal: (value: boolean) => void
}

export const ImportFileDialog = ({
  openCsvModal,
  setOpenCsvModal,
}: ImportFileDialogProps) => {
  const { userInfo, customRecommendations, updateCustomRecommendations } =
    useCustomRecommendations()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleImportCSV = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const csvContent = e.target?.result as string
        const parsedData = parseCSV(csvContent, userInfo?.user_id || '')

        const headers = Object.keys(parsedData[0])
        if (!validateHeaders(headers)) {
          toast.error(
            `Invalid CSV headers. Expected: ${EXPECTED_HEADERS.join(', ')}`
          )
          return
        }

        const validationResults = parsedData.map((row, index) => {
          try {
            return {
              success: true,
              data: csvSchema.parse(row),
            }
          } catch (error) {
            return {
              success: false,
              error,
              rowIndex: index + 1,
            }
          }
        })

        const formatValidationErrors = (
          errors: Array<{
            success: false
            error: { issues: Array<{ message: string; path: string[] }> }
            rowIndex: number
          }>
        ) => {
          const messages = errors.map(({ error, rowIndex }) => {
            const issueMessages = error.issues.map(
              (issue) =>
                `Row ${rowIndex}: ${issue.path.join('.')} - ${issue.message}`
            )
            return issueMessages.join('\n')
          })

          return `${messages.join('\n')}`
        }

        const errors = validationResults.filter(
          (
            result
          ): result is {
            success: false
            error: { issues: { message: string; path: string[] }[] }
            rowIndex: number
          } => !result.success && result.error !== undefined
        )

        if (errors.length > 0) {
          const errorMessage = formatValidationErrors(errors)
          toast.error('CSV Validation Failed', {
            description: errorMessage,
          })
          return
        }

        const validRecommendations = parsedData.filter((item) => {
          const isValid =
            item.recommendation && item.recommendation.trim() !== ''
          return isValid
        }) as CustomRecommendationInput[]

        const newRecommendations = [
          ...customRecommendations,
          ...validRecommendations,
        ]

        updateCustomRecommendations(newRecommendations)
        setOpenCsvModal(false)
      } catch (error) {
        console.error('Error parsing CSV:', error)
        toast.error(t('errors.invalidCSVFormat'))
      }
    }
    reader.readAsText(file)

    event.target.value = ''
  }

  return (
    <Dialog open={openCsvModal} onOpenChange={setOpenCsvModal}>
      <DialogContent>
        <DialogHeader className="space-y-5">
          <DialogTitle>
            {t('Import')} {t('CSV')}
          </DialogTitle>

          <DialogDescription className="text-sm">
            After uploading your file, the custom recommendations will be
            displayed. Please use this template to create your CSV correctly:
          </DialogDescription>
        </DialogHeader>

        <div>
          <Button
            className="justify-start gap-2 h-6"
            variant="outline"
            dataTestId="download-csv-template"
          >
            <a
              href="/custom-recommendation.csv"
              download
              className="w-full  flex items-center gap-2 text-xs font-semibold"
            >
              <FileTextIcon className="h-4 w-4" />
              {t('Download')} {t('CSV')} {t('Template')}
            </a>
          </Button>
        </div>

        <DialogFooter className="sm:justify-start">
          <div className="relative">
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept=".csv"
              onChange={handleFileChange}
            />

            <Button
              onClick={handleImportCSV}
              className="capitalize gap-2 font-normal"
              dataTestId="import-csv-button"
            >
              <UploadIcon className="h-4 w-4" />
              {t('Upload')}
            </Button>
          </div>
          <DialogClose asChild>
            <Button
              type="button"
              variant="outline"
              className="capitalize"
              dataTestId="cancel-import"
            >
              {t('cancel')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
