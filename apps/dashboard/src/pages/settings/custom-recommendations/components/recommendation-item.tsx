import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Badge,
  Button,
  TagsSelector,
} from '@libs/ui'
import { CheckIcon, PencilIcon, Trash2Icon, XIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { t } from 'i18next'
import { cn } from '@libs/common'
import type { CustomRecommendationInput } from 'prime-front-service-client'

interface RecommendationItemProps extends CustomRecommendationInput {
  onDelete?: () => void
  onEdit: (recommendation: CustomRecommendationInput) => void
  onCancel?: () => void
  isEditing?: boolean
  availableTags: string[]
}

export const RecommendationItem = ({
  recommendation,
  tags: tagsProp,
  updated_by,
  updated_at,
  created_at,
  created_by,
  onDelete,
  onEdit,
  onCancel,
  isEditing: isEditingProp = false,
  availableTags,
}: RecommendationItemProps) => {
  const [tags, setTags] = useState<string[]>(tagsProp || [])
  const [isEditing, setIsEditing] = useState(isEditingProp)
  const [editedRecommendation, setEditedRecommendation] =
    useState(recommendation)
  const [isError, setIsError] = useState(false)

  useEffect(() => {
    setIsEditing(isEditingProp)
  }, [isEditingProp])

  const handleEdit = () => {
    if (!editedRecommendation) {
      setIsError(true)
      return
    }
    onEdit({
      recommendation: editedRecommendation,
      tags,
      updated_by,
      updated_at,
      created_at,
      created_by,
    })
    if (!onCancel) {
      setIsEditing(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      setIsEditing(false)
      setEditedRecommendation(recommendation)
    }
  }

  return (
    <div
      className={cn(
        'col-span-full grid grid-cols-subgrid gap-4 p-4 text-muted-foreground items-center border-b last:border-b-0',
        isEditing && 'bg-slate-100'
      )}
    >
      <div className="text-sm pr-2">
        {isEditing ? (
          <div>
            <textarea
              value={editedRecommendation}
              onChange={(e) => {
                setIsError(false)
                setEditedRecommendation(e.target.value)
              }}
              className="w-full p-2 border rounded"
              rows={4}
              autoFocus
            />
            {isError && (
              <p className="text-red-500 text-sm mt-1">
                {t('recommendationCannotBeEmpty')}
              </p>
            )}
          </div>
        ) : (
          <div onClick={() => !onCancel && setIsEditing(true)}>
            {recommendation}
          </div>
        )}
      </div>

      <div className="text-sm">
        {isEditing ? (
          <TagsSelector
            selectedTags={tags}
            availableTags={availableTags}
            onTagsChange={(tags) => {
              setTags(tags)
            }}
          />
        ) : (
          <div className="flex flex-wrap gap-2">
            {tags?.map((tag) => (
              <Badge
                key={crypto.randomUUID()}
                className="gap-1 text-xs text-muted-foreground h-6 bg-white"
                variant="outline"
              >
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </div>
      <div className="text-sm">{updated_by}</div>
      <div className="text-sm">
        {updated_at.toLocaleString('en-US', {
          year: 'numeric',
          month: 'numeric',
          day: 'numeric',
        })}
      </div>
      <div className="text-sm">
        {created_at.toLocaleString('en-US', {
          year: 'numeric',
          month: 'numeric',
          day: 'numeric',
        })}
      </div>
      <div className="flex items-center space-x-2">
        {isEditing ? (
          <>
            <Button
              onClick={handleEdit}
              variant="ghost"
              size="icon"
              dataTestId="save-button"
            >
              <CheckIcon className="w-5 h-5" />
            </Button>
            <Button
              onClick={handleCancel}
              variant="ghost"
              size="icon"
              dataTestId="cancel-button"
            >
              <XIcon className="w-5 h-5" />
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={() => setIsEditing(true)}
              variant="ghost"
              size="icon"
              dataTestId="edit-button"
            >
              <PencilIcon className="w-5 h-5" />
            </Button>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="ghost" size="icon" dataTestId="delete-button">
                  <Trash2Icon className="w-5 h-5" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>
                    {t('deleteRecommendation')}?
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    {t('deleteRecommendationDescription')}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                  <AlertDialogAction onClick={onDelete} asChild>
                    <Button
                      variant="destructive"
                      className="bg-destructive hover:bg-destructive/80"
                      dataTestId="delete-recommendation"
                    >
                      {t('delete')}
                    </Button>
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </>
        )}
      </div>
    </div>
  )
}
