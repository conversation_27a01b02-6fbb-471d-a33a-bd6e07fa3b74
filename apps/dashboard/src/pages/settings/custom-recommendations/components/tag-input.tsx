'use client'

import * as React from 'react'
import { XIcon } from 'lucide-react'
import { Badge, Button, Input } from '@libs/ui'

interface TagInputProps {
  placeholder?: string
  tags: string[]
  setTags: React.Dispatch<React.SetStateAction<string[]>>
}

export function TagInput({
  placeholder = 'Add tag...',
  tags,
  setTags,
}: TagInputProps) {
  const [input, setInput] = React.useState('')
  const inputRef = React.useRef<HTMLInputElement>(null)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && input) {
      e.preventDefault()
      if (!tags.includes(input.trim())) {
        setTags([...tags, input.trim()])
      }
      setInput('')
    } else if (e.key === 'Backspace' && !input && tags.length > 0) {
      e.preventDefault()
      const newTags = [...tags]
      newTags.pop()
      setTags(newTags)
    }
  }

  const removeTag = (tag: string) => {
    setTags(tags.filter((t) => t !== tag))
  }

  return (
    <div className="flex flex-wrap gap-2">
      {tags.map((tag) => (
        <Badge
          key={tag}
          variant="outline"
          className="gap-1 text-xs text-muted-foreground h-6 bg-white"
        >
          {tag}

          <Button
            onClick={() => removeTag(tag)}
            size="icon"
            variant="ghost"
            className="w-3.5 h-3.5 hover:bg-slate-200"
            dataTestId="remove-tag-button"
          >
            <XIcon />
          </Button>
        </Badge>
      ))}
      <Input
        ref={inputRef}
        type="text"
        placeholder={placeholder}
        value={input}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        className="bg-white flex-grow border-0 outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
      />
    </div>
  )
}
