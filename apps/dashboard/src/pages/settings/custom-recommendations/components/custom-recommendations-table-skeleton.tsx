import { Skeleton } from '@libs/ui'

export const CustomRecommendationsTableSkeleton = () => {
  return (
    <div className="grid grid-cols-[2.5fr_1fr_1fr_1fr_1fr_1fr]">
      <div className="col-span-full grid grid-cols-subgrid gap-4 items-center font-semibold p-6 text-sm border-b border-dashed capitalize">
        {Array(6)
          .fill(null)
          .map((_, index) => (
            <div key={index}>
              <Skeleton className="h-4 w-24" />
            </div>
          ))}
      </div>

      {Array(5)
        .fill(null)
        .map((_, index) => (
          <div
            key={index}
            className="col-span-full grid grid-cols-subgrid gap-4 items-center p-4 border-b last:border-b-0"
          >
            <div>
              <Skeleton className="h-5 w-64" />
            </div>
            <div className="flex gap-2">
              {Array(2)
                .fill(null)
                .map((_, tagIndex) => (
                  <Skeleton key={tagIndex} className="h-6 w-16 rounded-full" />
                ))}
            </div>
            <div>
              <Skeleton className="h-4 w-32" />
            </div>
            <div>
              <Skeleton className="h-4 w-24" />
            </div>
            <div>
              <Skeleton className="h-4 w-24" />
            </div>
            <div className="flex gap-2 justify-end">
              <Skeleton className="h-8 w-8 rounded-md" />
              <Skeleton className="h-8 w-8 rounded-md" />
            </div>
          </div>
        ))}
    </div>
  )
}
