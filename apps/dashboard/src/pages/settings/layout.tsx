import { NavLink, Outlet, useLocation } from 'react-router'
import React from 'react'
import {
  BarChart4,
  BellRingIcon,
  Clock8Icon,
  Edit3Icon,
  GitCompareIcon,
  Grid2X2Icon,
  HardDriveDownloadIcon,
  KeyRoundIcon,
  UsersRoundIcon,
} from 'lucide-react'
import { t } from 'i18next'
import { cn } from '@libs/common'

const navLinkClassName =
  'h-[72px] text-gray-600 text-base font-medium flex items-center gap-3 p-2 capitalize transition-all rounded-lg hover:shadow border border-transparent hover:border-transparent hover:shadow-transparent hover:bg-slate-50 aria-[current=page]:bg-white  aria-[current=page]:shadow'

export const SettingsLayout = ({ className }: { className?: string }) => {
  const location = useLocation()
  const isCustomRecommendations =
    location.pathname === '/settings/custom-recommendations'
  const isNotificationsPage = location.pathname === '/settings/notifications'

  const isFullWidth = isCustomRecommendations || isNotificationsPage
  return (
    <div className={cn('grid grid-cols-[340px_1fr] min-h-screen', className)}>
      <div className="bg-slate-100 px-8">
        <nav className="grid gap-2 mt-16 sticky top-0">
          <NavLink to="/settings/sources" className={navLinkClassName}>
            <GitCompareIcon size={24} strokeWidth={1.5} /> {t('sources')}
          </NavLink>
          <NavLink
            to="/settings/security-frameworks"
            className={navLinkClassName}
          >
            <HardDriveDownloadIcon size={24} strokeWidth={1.5} />{' '}
            {t('securityAndCompliance')}
          </NavLink>

          <NavLink to="/settings/tasks-status" className={navLinkClassName}>
            <BarChart4 size={24} strokeWidth={1.5} /> {t('tasksStatus')}
          </NavLink>
          <NavLink to="/settings/users-management" className={navLinkClassName}>
            <UsersRoundIcon size={24} strokeWidth={1.5} />{' '}
            {t('usersManagement')}
          </NavLink>
          <NavLink
            to="/settings/custom-recommendations"
            className={navLinkClassName}
          >
            <Edit3Icon size={24} strokeWidth={1.5} />{' '}
            {t('customRecommendationsTitle')}
          </NavLink>
          <NavLink to="/settings/jira-attributes" className={navLinkClassName}>
            <Grid2X2Icon size={24} strokeWidth={1.5} /> {t('jiraAttributes')}
          </NavLink>
          <NavLink to="/settings/audit-logs" className={navLinkClassName}>
            <Clock8Icon size={24} strokeWidth={1.5} /> {t('auditLogs')}
          </NavLink>
          <NavLink to="/settings/authentication" className={navLinkClassName}>
            <KeyRoundIcon size={24} strokeWidth={1.5} /> {t('authentication')}
          </NavLink>
          <NavLink to="/settings/notifications" className={navLinkClassName}>
            <BellRingIcon size={24} strokeWidth={1.5} /> {t('Notifications')}
          </NavLink>
        </nav>
      </div>
      <div
        className={cn(
          'w-full max-w-[1000px] mt-16 px-8',
          isFullWidth && 'max-w-full'
        )}
      >
        <Outlet />
      </div>
    </div>
  )
}
