import { TasksStatusList } from './components/tasks-status-list'
import { ErrorSomethingWentWrong } from '@libs/ui'
import { BookXIcon } from 'lucide-react'
import { t } from 'i18next'
import { useGetJobs } from '../../../api/use-jobs-api'
import { TasksStatusSkeleton } from './components/tasks-status-skeleton'

export const TasksStatusPage = () => {
  const { data, isPending, isError } = useGetJobs()

  if (isError) {
    return <ErrorSomethingWentWrong />
  }

  return (
    <div>
      <div className="page-title mb-8">
        <h1 className="text-2xl font-semibold mb-1 capitalize">{t('tasks')}</h1>
      </div>

      {isPending ? (
        <TasksStatusSkeleton />
      ) : !data?.length ? (
        <div className="w-full min-h-96 flex flex-col justify-center items-center text-muted-foreground">
          <BookXIcon className="w-8 h-8 mx-auto" />
          <h2 className="mt-6 font-bold tracking-tight text-muted-foreground">
            {t('noTasksFound')}
          </h2>
        </div>
      ) : (
        <TasksStatusList data={data} />
      )}
    </div>
  )
}
