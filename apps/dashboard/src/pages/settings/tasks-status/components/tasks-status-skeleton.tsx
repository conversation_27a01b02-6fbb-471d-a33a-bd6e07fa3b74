import { Progress, Skeleton } from '@libs/ui'

export const TasksStatusSkeleton = () => {
  return (
    <div className="space-y-3" data-testid="tasks-status-skeleton">
      {[1, 2, 3].map((key) => (
        <div
          key={key}
          className="grid grid-cols-[2.5fr_2fr_2fr] items-center gap-4 bg-gray-100 p-4 rounded-lg dark:bg-gray-800"
        >
          <div>
            <h3 className="font-medium">
              <Skeleton className="h-5 w-32" />
            </h3>
          </div>
          <div className="text-right">
            <Skeleton className="h-4 w-32 ml-auto mb-1" />
            <Skeleton className="h-3 w-8 ml-auto" />
          </div>
          <div className="text-right flex flex-col items-end">
            <Progress className="w-32" value={0} progressColor={''} />
            <Skeleton className="h-3 w-24 mt-1" />
          </div>
        </div>
      ))}
    </div>
  )
}
