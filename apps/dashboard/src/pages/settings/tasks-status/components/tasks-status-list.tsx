import { Progress } from '@libs/ui'
import { t } from 'i18next'
import type { JobResponse } from 'prime-front-service-client'
import { memo } from 'react'

const progressColor = 'bg-emerald-400'
const progressColorError = 'bg-rose-600'

interface TasksStatusListProps {
  data: JobResponse[]
}

const TaskItem = memo(({ task }: { task: JobResponse }) => (
  <div className="grid grid-cols-[2.5fr_2fr_2fr] items-center gap-4 bg-gray-100 p-4 rounded-lg dark:bg-gray-800">
    <div>
      <h3 className="font-medium capitalize">
        {t(task?.name?.replace(/_/g, ' '))}
      </h3>
    </div>
    <div className="text-right">
      <p className="text-sm">{task.created_at?.toLocaleString()}</p>
      <p className="text-gray-500 text-xs">Start</p>
    </div>
    <div className="text-right flex flex-col items-end">
      <Progress
        className="w-32"
        value={Math.trunc(task.progress as number)}
        progressColor={
          task.error || task.status.toLowerCase() === 'failed'
            ? progressColorError
            : progressColor
        }
      />
      <p className="text-gray-500 text-xs mt-1">
        {Math.trunc(task.progress as number)}% {task.status}
      </p>
    </div>
  </div>
))

export const TasksStatusList = memo(({ data }: TasksStatusListProps) => {
  return (
    <div className="space-y-3" data-testid="tasks-status-list">
      {data.map((task) => (
        <TaskItem key={task.id || crypto.randomUUID()} task={task} />
      ))}
    </div>
  )
})
