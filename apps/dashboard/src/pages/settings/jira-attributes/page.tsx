/*eslint-disable max-lines*/
import { t } from 'i18next'
import {
  Button,
  ErrorSomethingWentWrong,
  InputSearch,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@libs/ui'
import React, { useEffect, useMemo, useState } from 'react'
import { toast } from 'sonner'
import type {
  SourceModelResponse,
  ProviderFieldInfoOptions,
} from 'prime-front-service-client'
import { Loader2, RefreshCw } from 'lucide-react'
import {
  useGetConfig,
  useGetJiraFields,
  useUpdateConfig,
} from '../../../api/use-config-api'
import {
  useBuildProviderFieldsDataJob,
  useGetAllSources,
} from '../../../api/use-sources-api'
import type { SourceInfo } from '../sources/utils'
import { atom, useAtom } from 'jotai/index'
import { JiraAttributesTable } from './components/jira-attributes-table'
import { cn } from '@libs/common'
import { JiraAttributesSkeleton } from './components/jira-attributes-skeleton'

export const selectedAttributesAtom = atom<string[]>([])

const valueFilterClass: Record<'any' | 'has value' | 'no value', string> = {
  any: 'bg-white border border-slate-300 hover:bg-white',
  'has value': 'border border-green-400 bg-green-50 hover:bg-green-50',
  'no value': 'border border-red-500 bg-red-50 hover:bg-red-50',
}
export const JiraAttributesPage = () => {
  const {
    data: sources,
    isPending: sourcesPending,
    isSuccess: sourcesSuccess,
    isError: sourcesError,
  } = useGetAllSources(1)

  const [selectedSource, setSelectedSource] =
    useState<SourceModelResponse | null>(null)

  const [valuesFilterMode, setValuesFilterMode] = useState<
    'any' | 'has value' | 'no value'
  >('any')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedAttributes, setSelectedAttributes] = useAtom(
    selectedAttributesAtom
  )
  const [shouldFetchJiraFields, setShouldFetchJiraFields] = useState(false)
  const {
    data: config,
    error: configError,
    isError: isConfigError,
    isSuccess: isConfigSuccess,
    refetch: configRefetch,
  } = useGetConfig()

  const { mutate: updateConfigMutation, isPending: isUpdatePending } =
    useUpdateConfig()

  const {
    mutate: buildProviderFieldsDataJobMutation,
    isPending: isBuildProviderFieldsDataJobPending,
  } = useBuildProviderFieldsDataJob()

  const { data, isPending, error, isError, isSuccess, refetch, isRefetching } =
    useGetJiraFields(
      shouldFetchJiraFields && selectedSource ? selectedSource.id : undefined
    )

  const [filteredData, setFilteredData] = useState(data?.fields)

  useEffect(() => {
    if (sourcesSuccess && sources.length > 0) {
      setSelectedSource(sources[0])
      setShouldFetchJiraFields(true)
    }
  }, [sources, sourcesSuccess])

  useEffect(() => {
    if (isConfigSuccess) {
      setSelectedAttributes([...(config?.providers_attributes?.jira || [])])
    }
  }, [config, isConfigSuccess])

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target['value'])
  }
  const toggleFilterMode = () => {
    setValuesFilterMode((prevMode) => {
      if (prevMode === 'any') return 'has value'
      if (prevMode === 'has value') return 'no value'
      return 'any'
    })
  }

  const saveJiraConfig = () => {
    updateConfigMutation(
      {
        AccountConfigUpdate: {
          providers_attributes: {
            jira: selectedAttributes,
          },
        },
      },
      {
        onSuccess: async () => {
          await configRefetch()
          toast.success(t('updatedJiraAttributesSuccessfully'))
        },
        onError: async () => {
          toast.error(t('errors.updatingConfigFailed'))
        },
      }
    )
  }

  const getSourceInfo = (
    source: SourceModelResponse | undefined | null
  ): SourceInfo | undefined => {
    return source?.info as SourceInfo
  }

  useEffect(() => {
    if (shouldFetchJiraFields && selectedSource) {
      refetch().then()
      setSelectedAttributes([...(config?.providers_attributes?.jira || [])])
    }
  }, [shouldFetchJiraFields, selectedSource, refetch])

  useEffect(() => {
    const filterByMode = (item: ProviderFieldInfoOptions) => {
      if (valuesFilterMode === 'has value') {
        return item.options && item.options.length > 0
      } else if (valuesFilterMode === 'no value') {
        return !item.options || item.options.length === 0
      }
      return true
    }
    if (searchQuery) {
      setFilteredData(
        data?.fields.filter(
          (item: ProviderFieldInfoOptions) =>
            item.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
            filterByMode(item)
        )
      )
    } else {
      setFilteredData(data?.fields.filter(filterByMode))
    }
  }, [data, searchQuery, valuesFilterMode])

  const sortedFilteredData = useMemo(() => {
    return filteredData?.sort(
      (a: ProviderFieldInfoOptions, b: ProviderFieldInfoOptions) => {
        const aSelected = selectedAttributes.includes(a.id)
        const bSelected = selectedAttributes.includes(b.id)
        if (aSelected && !bSelected) {
          return -1
        } else if (!aSelected && bSelected) {
          return 1
        } else {
          return a.name.localeCompare(b.name)
        }
      }
    )
  }, [selectedSource, data, config, searchQuery, filteredData])

  const refresh = () => {
    buildProviderFieldsDataJobMutation(selectedSource?.id || 0, {
      onSuccess: () => {
        toast.warning(t('attributesRefreshWarning'))
      },
      onError: () => {
        toast.error(t('errors.attributesRefreshFailed'))
      },
    })
  }

  const isLoading = isPending || isRefetching || sourcesPending

  if (sourcesError) {
    return <ErrorSomethingWentWrong />
  }

  if (sourcesSuccess && !sources.length) {
    return (
      <div className="flex justify-center items-center w-full rounded-md border bg-card shadow-sm capitalize h-[400px]">
        <h1>{t('pleaseAddSource')}</h1>
      </div>
    )
  }

  return (
    <div className="jira-attributes-page">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold mb-1 capitalize">
          {t('jiraAttributesTitle')}
        </h1>
        <p className="text-gray-400">{t('jiraAttributesDescription')}</p>
      </div>
      <div className="flex items-end justify-between">
        <div className="flex relative">
          <InputSearch
            placeholder={t('search')}
            value={searchQuery}
            onChange={handleSearchChange}
            onClear={() => setSearchQuery('')}
            className="w-56"
            data-testid="jira-attributes-search"
          />
        </div>
        <div className="flex gap-5">
          <div className="flex flex-col gap-1">
            <Label className="text-xs font-bold capitalize ml-1">
              {t('values')}
            </Label>
            <Button
              onClick={toggleFilterMode}
              dataTestId="values-filter-button"
              className={cn(
                'px-4 capitalize text-slate-800 shadow-none',
                valueFilterClass[valuesFilterMode]
              )}
            >
              {valuesFilterMode}
            </Button>
          </div>
          <div className="flex flex-col gap-1">
            <Label className="text-xs font-bold capitalize ml-1">
              {t('source')}
            </Label>
            <Select
              disabled={sourcesPending}
              value={selectedSource?.id?.toString()}
              onValueChange={(value) => {
                const source = sources?.find((s) => s.id.toString() === value)
                if (source) {
                  setSelectedSource(source)
                  setShouldFetchJiraFields(true)
                }
              }}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue
                  placeholder={
                    getSourceInfo(selectedSource)?.name ||
                    selectedSource?.id ||
                    ''
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {sources?.map((source) => (
                  <SelectItem key={source.id} value={source.id.toString()}>
                    {getSourceInfo(source)?.name || source?.id || ''}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="px-4 capitalize gap-2"
            dataTestId="refresh-button"
            disabled={isBuildProviderFieldsDataJobPending}
            onClick={() => refresh()}
          >
            <RefreshCw
              className={cn(
                'h-4 w-4',
                isBuildProviderFieldsDataJobPending
                  ? 'animate-spin'
                  : 'animate-none'
              )}
            />
            {t('refresh')}
          </Button>
          <Button onClick={() => saveJiraConfig()} dataTestId="save-button">
            <span>{t('save')}</span>
            {isUpdatePending && (
              <div className="flex ml-2">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              </div>
            )}
          </Button>
        </div>
      </div>

      {/* Only show skeleton when loading table data */}
      <div className="my-4">
        {isLoading ? (
          <JiraAttributesSkeleton />
        ) : (
          isSuccess &&
          isConfigSuccess &&
          data && <JiraAttributesTable data={sortedFilteredData || []} />
        )}
      </div>

      {isError && (
        <div>
          {t('error')}: {error.message}
        </div>
      )}
      {isConfigError && (
        <div>
          {t('error')}: {configError.message}
        </div>
      )}
    </div>
  )
}
