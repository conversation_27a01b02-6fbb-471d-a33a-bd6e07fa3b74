import {
  Badge,
  Checkbox,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@libs/ui'
import { t } from 'i18next'
import { useState } from 'react'
import { cn } from '@libs/common'
import { useAtom } from 'jotai/index'
import { selectedAttributesAtom } from '../page'
import type { ProviderFieldInfoOptions } from 'prime-front-service-client'

interface JiraAttributesTableProps {
  data: ProviderFieldInfoOptions[]
}
export const JiraAttributesTable = ({ data }: JiraAttributesTableProps) => {
  const [selectedAttributes, setSelectedAttributes] = useAtom(
    selectedAttributesAtom
  )
  const [openPopoverId, setOpenPopoverId] = useState<string | null>(null)
  const toggleSelected = (
    isSelected: boolean,
    field: ProviderFieldInfoOptions
  ) => {
    setSelectedAttributes((prevSelected) => {
      if (isSelected) {
        return [...prevSelected, field.id]
      }
      return prevSelected.filter((f) => f !== field.id)
    })
  }

  if (!data || !data.length) {
    return (
      <div className=" border-slate-200 rounded-md  shadow-sm bg-white min-h-80 flex justify-center items-center text-muted-foreground">
        {t('noAttributes')}
      </div>
    )
  }

  return (
    <div className="table-wrapper relative h-[calc(100vh_-_14rem)] overflow-auto border border-slate-200 rounded-md  shadow-sm bg-white">
      <Table className="w-full ">
        <TableHeader className="border-0 text-sm bg-white sticky top-0 z-10 border-b border-dashed">
          <TableRow className="border-b-0">
            <TableHead className="py-2 px-4">
              <Checkbox
                checked={
                  selectedAttributes.length === data.length && data.length > 0
                }
                onCheckedChange={(value) => {
                  if (value) {
                    setSelectedAttributes(data.map((field) => field.id))
                  } else {
                    setSelectedAttributes([])
                  }
                }}
                aria-label="Select all rows"
              />
            </TableHead>
            <TableHead className="px-2 py-4 capitalize">
              <div className="flex items-center">{t('fieldName')}</div>
            </TableHead>
            <TableHead className="px-2 py-4 capitalize">
              <div className="flex items-center">{t('fieldId')}</div>
            </TableHead>
            <TableHead className="px-2 py-4 capitalize">
              <div className="flex items-center">{t('values')}</div>
            </TableHead>
            <TableHead className="px-2 py-4 capitalize">
              <div className="flex items-center">{t('fieldType')}</div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody
          className={cn(
            '[&_tr:last-child]:border-1 rounded-lg',
            data?.length ? 'border-b' : 'border-0'
          )}
        >
          {data.map((field: ProviderFieldInfoOptions) => (
            <TableRow key={field.id} className="border-b">
              <TableCell className="py-2 px-4">
                <Checkbox
                  checked={selectedAttributes.includes(field.id)}
                  onCheckedChange={(value) => toggleSelected(!!value, field)}
                  aria-label="Select row"
                />
              </TableCell>
              <TableCell className="p-2">{field.name}</TableCell>
              <TableCell className="p-2">{field.id}</TableCell>
              <TableCell className="p-2">
                <div className="flex flex-wrap gap-1 text-muted-foreground min-w-52">
                  {field.options &&
                    (field.options?.length > 2 ? (
                      <>
                        {field.options[0]}, {field.options[1]}
                        <Popover open={openPopoverId === field.id}>
                          <PopoverTrigger
                            onMouseEnter={() => setOpenPopoverId(field.id)}
                            onMouseLeave={() => setOpenPopoverId(null)}
                          >
                            <Badge
                              variant="secondary"
                              className="rounded-full text-gray-400 border-gray-400 h-6 bg-white font-light gap-2"
                            >
                              + {field.options?.length - 2}
                            </Badge>
                          </PopoverTrigger>
                          <PopoverContent
                            side="left"
                            className="w-full max-w-60 gap-2 flex flex-wrap"
                          >
                            {field.options?.map((label, index) => (
                              <div key={crypto.randomUUID()}>
                                {label}
                                {(field.options?.length ?? 0) - 1 === index
                                  ? ''
                                  : ','}{' '}
                              </div>
                            ))}
                          </PopoverContent>
                        </Popover>
                      </>
                    ) : (
                      field.options?.map((label, index) => (
                        <div key={crypto.randomUUID()}>
                          {label}
                          {(field.options?.length ?? 0) - 1 === index
                            ? ''
                            : ','}{' '}
                        </div>
                      ))
                    ))}
                </div>
              </TableCell>
              <TableCell className="p-2">{field.type}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
