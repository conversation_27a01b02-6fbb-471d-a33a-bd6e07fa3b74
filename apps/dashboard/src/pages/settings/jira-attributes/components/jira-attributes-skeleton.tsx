import { Skeleton } from '@libs/ui'

export const <PERSON><PERSON><PERSON><PERSON>ributesSkeleton = () => {
  return (
    <div data-testid="loading-spinner">
      {/* Table */}
      <div className="my-4 rounded-md border">
        {/* Table header */}
        <div className="bg-gray-100/40 dark:bg-gray-800/40">
          <div className="grid grid-cols-[56px_2fr_2fr_3fr] px-4 py-3">
            <Skeleton className="h-5 w-5" /> {/* Checkbox */}
            <Skeleton className="h-5 w-24" /> {/* Name */}
            <Skeleton className="h-5 w-24" /> {/* ID */}
            <Skeleton className="h-5 w-24" /> {/* Values */}
          </div>
        </div>

        {/* Table rows */}
        <div className="divide-y">
          {Array(8)
            .fill(null)
            .map((_, index) => (
              <div
                key={index}
                className="grid grid-cols-[56px_2fr_2fr_3fr] items-center px-4 py-3"
              >
                <Skeleton className="h-5 w-5" /> {/* Checkbox */}
                <Skeleton className="h-5 w-48" /> {/* Name */}
                <Skeleton className="h-5 w-32" /> {/* ID */}
                <div className="flex gap-1">
                  {Array(3)
                    .fill(null)
                    .map((_, i) => (
                      <Skeleton key={i} className="h-6 w-20 rounded-full" />
                    ))}
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  )
}
