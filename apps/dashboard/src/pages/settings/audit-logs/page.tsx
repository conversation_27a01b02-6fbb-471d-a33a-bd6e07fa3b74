import { ErrorSomethingWentWrong } from '@libs/ui'
import { ActivityLogTable } from './components/activity-log-table'
import { t } from 'i18next'
import { useAuditApi } from '../../../api/use-audit-api'
import { ActivityLogTableSkeleton } from './components/activity-log-table-skeleton'

export const AuditLogsPage = () => {
  const { data, isPending, isError } = useAuditApi()

  if (isError) {
    return <ErrorSomethingWentWrong />
  }

  return (
    <div className="audit-log-page">
      <div className="mb-8">
        <h1 className="text-2xl font-semibold mb-1 capitalize">
          {t('systemActivityLogsTitle')}
        </h1>
        <div className="text-gray-400">
          {t('systemActivityLogsDescription')}
        </div>
      </div>

      {isPending ? (
        <ActivityLogTableSkeleton />
      ) : (
        <ActivityLogTable data={data?.results || []} />
      )}
    </div>
  )
}
