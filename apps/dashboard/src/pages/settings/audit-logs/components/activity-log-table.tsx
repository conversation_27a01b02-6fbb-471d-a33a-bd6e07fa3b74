import {
  Avatar,
  AvatarFallback,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@libs/ui'
import { t } from 'i18next'
import type { AuditEvent } from 'prime-front-service-client'

const formatDate = (date: Date): string => {
  let hours: number | string = date.getHours()
  let minutes: number | string = date.getMinutes()

  let month: number | string = date.getMonth() + 1
  let day: number | string = date.getDate()
  const year: number | string = date.getFullYear() % 100

  if (hours < 10) hours = '0' + hours
  if (minutes < 10) minutes = '0' + minutes
  if (month < 10) month = '0' + month
  if (day < 10) day = '0' + day

  return `${hours}:${minutes}, ${month}/${day}/${year}`
}

interface ActivityLogTableProps {
  data: AuditEvent[]
}

export const ActivityLogTable = ({ data }: ActivityLogTableProps) => {
  return (
    <div className="bg-white h-[600px] overflow-auto border rounded-md">
      <Table>
        <TableHeader>
          <TableRow className="capitalize">
            <TableHead className="px-4 py-6 w-72">{t('activity')}</TableHead>
            <TableHead className="px-4 py-6">{t('author')}</TableHead>
            <TableHead className="px-4 py-6">{t('date')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((activity) => (
            <TableRow key={crypto.randomUUID()}>
              <TableCell className="font-medium p-4 text-muted-foreground capitalize">
                {t(activity.audit_action)}
              </TableCell>
              <TableCell className="p-4">
                <div className="flex items-center space-x-3 text-s">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>
                      {activity.user?.charAt(0)?.toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-muted-foreground">{activity.user}</span>
                </div>
              </TableCell>
              <TableCell className="p-4 text-muted-foreground">
                {formatDate(activity.date)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
