import { Skeleton } from '@libs/ui'

export const ActivityLogTableSkeleton = () => {
  return (
    <div className="rounded-md border">
      <div className="bg-gray-100/40 dark:bg-gray-800/40">
        <div className="grid grid-cols-[1fr_1fr_1fr] px-4 py-6">
          <div>
            <Skeleton className="h-4 w-24" />
          </div>
          <div>
            <Skeleton className="h-4 w-24" />
          </div>
          <div>
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </div>

      <div className="divide-y">
        {Array(10)
          .fill(null)
          .map((_, index) => (
            <div
              key={index}
              className="grid grid-cols-[1fr_1fr_1fr] items-center px-4 py-3"
            >
              <div>
                <Skeleton className="h-4 w-32" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-4 w-32" />
              </div>
              <div>
                <Skeleton className="h-4 w-64" />
              </div>
            </div>
          ))}
      </div>
    </div>
  )
}
