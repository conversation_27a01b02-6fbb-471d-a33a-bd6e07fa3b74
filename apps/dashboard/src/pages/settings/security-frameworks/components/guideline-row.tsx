import { useState } from 'react'
import { Button, Textarea } from '@libs/ui'
import { Edit2Icon, Trash2Icon, CheckIcon, XIcon } from 'lucide-react'
import { t } from 'i18next'
import { format } from 'date-fns'
import type { PolicyMetadata } from 'prime-front-service-client'
import { useUpdatePolicy } from '../../../../api/use-policies-api'

interface GuidelineRowProps {
  policy: PolicyMetadata
  onDelete: (id: number) => void
}

export const GuidelineRow = ({ policy, onDelete }: GuidelineRowProps) => {
  const [isEditing, setIsEditing] = useState(false)
  const [description, setDescription] = useState(policy.description || '')
  const updatePolicyMutation = useUpdatePolicy()

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleSave = () => {
    updatePolicyMutation.mutate(
      {
        policy_id: policy.id,
        PolicyUpdateRequest: {
          description,
        },
      },
      {
        onSuccess: () => {
          setIsEditing(false)
        },
      }
    )
  }

  const handleCancel = () => {
    setDescription(policy.description || '')
    setIsEditing(false)
  }

  return (
    <div className="col-span-full grid grid-cols-subgrid gap-4 items-center p-6 text-sm border-b last:border-b-0 hover:bg-accent/50 text-slate-500">
      <div>{decodeURIComponent(policy.name).trim()}</div>
      <div
        className="cursor-pointer"
        onClick={() => !isEditing && handleEdit()}
      >
        {isEditing ? (
          <Textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="field-sizing-content max-h-32 min-h-0 resize-none w-full"
            autoFocus
          />
        ) : (
          <span className={!description ? 'text-gray-400 italic' : ''}>
            {description || t('clickToEdit')}
          </span>
        )}
      </div>
      <div>{policy.uploaded_by}</div>
      <div>{format(policy.created_at, 'dd/MM/yyyy HH:mm')}</div>
      <div className="flex items-center justify-end gap-2">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={isEditing ? handleSave : handleEdit}
          dataTestId={
            isEditing ? 'save-guideline-button' : 'edit-guideline-button'
          }
        >
          {isEditing ? (
            <CheckIcon className="h-4 w-4" />
          ) : (
            <Edit2Icon className="h-4 w-4" />
          )}
          <span className="sr-only">{isEditing ? t('save') : t('edit')}</span>
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={isEditing ? handleCancel : () => onDelete(policy.id)}
          dataTestId={
            isEditing ? 'cancel-edit-button' : 'delete-guideline-button'
          }
        >
          {isEditing ? (
            <XIcon className="h-4 w-4" />
          ) : (
            <Trash2Icon className="h-4 w-4" />
          )}
          <span className="sr-only">
            {isEditing ? t('cancel') : t('delete')}
          </span>
        </Button>
      </div>
    </div>
  )
}
