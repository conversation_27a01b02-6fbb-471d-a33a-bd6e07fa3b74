import { Skeleton } from '@libs/ui'

export const GuidelinesTableSkeleton = () => {
  return (
    <div className="rounded-lg border bg-white">
      <div className="grid grid-cols-[2.5fr_1fr_1fr_1fr_1fr]">
        {/* Header */}
        <div className="col-span-full grid grid-cols-subgrid gap-4 items-center font-semibold p-6 text-sm border-b border-dashed">
          {Array(5)
            .fill(null)
            .map((_, index) => (
              <div key={index}>
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
        </div>

        {/* Rows */}
        {Array(5)
          .fill(null)
          .map((_, index) => (
            <div
              key={index}
              className="col-span-full grid grid-cols-subgrid gap-4 items-center p-4 border-b last:border-b-0"
            >
              <div>
                <Skeleton className="h-5 w-64" />
              </div>
              <div>
                <Skeleton className="h-4 w-32" />
              </div>
              <div>
                <Skeleton className="h-4 w-24" />
              </div>
              <div>
                <Skeleton className="h-4 w-24" />
              </div>
              <div className="flex gap-2 justify-end">
                <Skeleton className="h-8 w-8 rounded-md" />
                <Skeleton className="h-8 w-8 rounded-md" />
              </div>
            </div>
          ))}
      </div>
    </div>
  )
}
