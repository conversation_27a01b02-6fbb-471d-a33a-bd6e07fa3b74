import { useState } from 'react'
import { Button, buttonVariants } from '@libs/ui'
import { ArrowDown, ArrowDownUpIcon } from 'lucide-react'
import { t } from 'i18next'
import type { Column, SortingState } from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnDef,
} from '@tanstack/react-table'
import { cn } from '@libs/common'
import type { PolicyMetadata } from 'prime-front-service-client'
import { useDeletePolicies } from '../../../../api/use-policies-api'

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
} from '@libs/ui'
import { Loader2 } from 'lucide-react'
import { toast } from 'sonner'
import { GuidelineRow } from './guideline-row'

interface GuidelinesTableProps {
  data: PolicyMetadata[]
  refetch: () => void
}

interface SortButtonProps {
  title: string
  column: Column<PolicyMetadata, unknown>
}

const SortButton = ({ title, column }: SortButtonProps) => {
  const isSorted = column.getIsSorted()
  const isSortedAsc = isSorted === 'asc'

  return (
    <div className="flex items-center">
      <span>{title}</span>
      <Button
        variant="ghost"
        className="p-0 h-6 w-6 text-slate-600"
        size="icon"
        dataTestId="sort-button"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        <div className="flex p-0 font-light">
          {isSorted ? (
            <ArrowDown
              className={cn(
                'p-0 h-5 w-4',
                isSorted ? 'text-teal-600' : 'text-primary',
                isSorted && isSortedAsc ? 'rotate-180' : ''
              )}
            />
          ) : (
            <ArrowDownUpIcon className="p-0 h-5 w-4 text-gray-500" />
          )}
        </div>
      </Button>
    </div>
  )
}

export const GuidelinesTable = ({ data, refetch }: GuidelinesTableProps) => {
  const [sorting, setSorting] = useState<SortingState>([])
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [selectedPolicyId, setSelectedPolicyId] = useState<number | null>(null)
  const { mutate: deletePolicies, isPending: isDeletingPending } =
    useDeletePolicies()

  const handleDelete = (id: number) => {
    setSelectedPolicyId(id)
    setDeleteConfirmOpen(true)
  }

  const confirmDelete = () => {
    if (selectedPolicyId === null) {
      toast.error(t('errors.noGuidelineSelected'))
      return
    }

    deletePolicies([selectedPolicyId], {
      onSuccess: () => {
        toast.success(t('guidelineDeletedSuccessfully'))
        setDeleteConfirmOpen(false)
        setSelectedPolicyId(null)
        refetch()
      },
      onError: () => {
        toast.error(t('errors.failedToDeleteGuideline'))
      },
    })
  }

  const columns: ColumnDef<PolicyMetadata>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => {
        return <SortButton title={t('Policy')} column={column} />
      },
    },
    {
      accessorKey: 'description',
      header: ({ column }) => {
        return <SortButton title={t('Description')} column={column} />
      },
    },
    {
      accessorKey: 'uploaded_by',
      header: ({ column }) => {
        return <SortButton title={t('Uploaded By')} column={column} />
      },
    },
    {
      accessorKey: 'created_at',
      header: ({ column }) => {
        return <SortButton title={t('Upload Time')} column={column} />
      },
      sortingFn: 'datetime',
    },
    {
      id: 'actions',
      header: () => '',
    },
  ]

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  })

  return (
    <>
      <div className="rounded-lg border bg-white">
        <div className="grid grid-cols-[2.5fr_1fr_1fr_1fr_1fr]">
          <div className="col-span-full grid grid-cols-subgrid gap-4 items-center p-6 text-sm border-b border-dashed">
            {table.getHeaderGroups().map((headerGroup) =>
              headerGroup.headers.map((header) => (
                <div key={header.id} className="text-sm font-semibold">
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext()
                  )}
                </div>
              ))
            )}
          </div>
          {table.getRowModel().rows.map((row) => (
            <GuidelineRow
              key={row.id}
              policy={row.original}
              onDelete={handleDelete}
            />
          ))}
        </div>
      </div>

      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('deleteGuideline')}?</AlertDialogTitle>
            <AlertDialogDescription>
              {t('thisActionCannotBeUndone')}.{' '}
              {t('thisWillPermanentlyDeleteGuideline')}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={isDeletingPending}
              dataTestId="confirm-delete-guideline"
              className={buttonVariants({ variant: 'destructive' })}
            >
              {isDeletingPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {t('delete')}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
