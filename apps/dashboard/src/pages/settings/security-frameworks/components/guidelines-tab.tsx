import { useState } from 'react'
import { <PERSON><PERSON> } from '@libs/ui'
import { DocumentUploadModal } from '../../../documents/components/document-upload-modal'
import { GuidelinesTable } from './guidelines-table'
import { toast } from 'sonner'
import { t } from 'i18next'
import { CirclePlusIcon } from 'lucide-react'
import {
  useGetPolicies,
  useUploadPolicies,
} from '../../../../api/use-policies-api'
import { GuidelinesTableSkeleton } from './guidelines-table-skeleton'
import { errorCodesMap } from '../../../../api/error-map'

export const GuidelinesTab = () => {
  const { data, refetch, isFetching } = useGetPolicies()
  const { mutate: uploadPolicies, isPending: isUploadingPending } =
    useUploadPolicies()
  const [open, setOpen] = useState(false)

  const handleUpload = async (files: File[]) => {
    uploadPolicies(files, {
      onSuccess: () => {
        toast.success(`${files.length} guideline(s) uploaded successfully`)
        refetch()
        setOpen(false)
      },
      onError: async (error: any) => {
        const message = await error?.response?.json()

        if (message.type && message.type in errorCodesMap) {
          const errorMessage =
            errorCodesMap[message.type as keyof typeof errorCodesMap]
          toast.error(errorMessage)
        } else {
          toast.error('Failed to upload guidelines')
        }
      },
    })
  }

  if (isFetching) {
    return <GuidelinesTableSkeleton />
  }

  return (
    <div className="guidelines-tab">
      {!data?.length ? (
        <div className="flex flex-col gap-8 justify-center items-center h-96">
          <p className="text-muted-foreground">
            {t('noFilesUploadedYetGuidelines')}
          </p>
          <div>
            <Button
              onClick={() => setOpen(true)}
              dataTestId="upload-guidelines-button"
              className="flex items-center gap-2"
            >
              {t('uploadPDF')}
            </Button>
          </div>

          <DocumentUploadModal
            open={open}
            onOpenChange={setOpen}
            onUpload={handleUpload}
            isPending={isUploadingPending}
            title={t('uploadPDF')}
            description={t('Upload your company security guidelines')}
            acceptedFileTypes=".pdf,.doc,.docx"
            maxFiles={5}
          />
        </div>
      ) : (
        <div>
          <div className="flex items-center justify-between gap-8 mb-4">
            <p className="text-gray-400">
              {t('addCompanyPoliciesToKeepEverythingInOnePlace')}
            </p>
            <Button
              onClick={() => setOpen(true)}
              dataTestId="upload-guidelines-button"
              className="flex items-center gap-2"
            >
              <CirclePlusIcon className="w-5 h-5" />
              {t('Upload Guidelines')}
            </Button>
          </div>

          <GuidelinesTable data={data} refetch={refetch} />
        </div>
      )}

      <DocumentUploadModal
        open={open}
        onOpenChange={setOpen}
        onUpload={handleUpload}
        isPending={isUploadingPending}
        title={t('Upload Guidelines')}
        description={t('Upload your company security guidelines')}
        acceptedFileTypes=".pdf"
        maxFiles={5}
      />
    </div>
  )
}
