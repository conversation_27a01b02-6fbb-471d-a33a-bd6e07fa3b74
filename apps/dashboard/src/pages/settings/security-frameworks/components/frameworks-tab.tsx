import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Button,
  Card,
  CardContent,
  CardHeader,
  Checkbox,
  CisIcon,
  HitrustIcn,
  NistIcn,
  PciDssIcn,
} from '@libs/ui'
import { SecurityFramework as SecurityFrameworkInput } from 'prime-front-service-client'
import { Loader2 } from 'lucide-react'
import { t } from 'i18next'
import { cn } from '@libs/common'
import { SecurityFrameworksSkeleton } from './security-frameworks-skeleton'

interface FrameworksTabProps {
  selectedFramework: SecurityFrameworkInput | undefined
  setSelectedFramework: (framework: SecurityFrameworkInput) => void
  isUpdatePending: boolean
  pendingConfig: boolean
  saveConfig: () => void
}

const headerClass = 'text-gray-600 text-xl font-semibold'

export const FrameworksTab = ({
  selectedFramework,
  setSelectedFramework,
  isUpdatePending,
  pendingConfig,
  saveConfig,
}: FrameworksTabProps) => {
  return (
    <>
      <div className="flex justify-between items-center">
        <p className="text-gray-400">{t('securityFrameworksDescription')}</p>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              disabled={!selectedFramework || isUpdatePending || pendingConfig}
              dataTestId="save-button"
            >
              {isUpdatePending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {t('save')}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {t('securityFrameworkConfirmation')}
              </AlertDialogTitle>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction onClick={() => saveConfig()}>
                <Button disabled={isUpdatePending} dataTestId="confirm-button">
                  {isUpdatePending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {t('securityFrameworkConfirm')}
                </Button>
              </AlertDialogAction>
              <AlertDialogCancel>
                {t('securityFrameworkCancel')}
              </AlertDialogCancel>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      <div className="grid grid-cols-2 grid-rows-2 gap-6 my-6">
        {pendingConfig || !selectedFramework ? (
          <SecurityFrameworksSkeleton />
        ) : (
          <>
            <Card
              onClick={() => setSelectedFramework(SecurityFrameworkInput.NIST)}
              className={cn(
                'cursor-pointer',
                selectedFramework === SecurityFrameworkInput.NIST
                  ? 'border-black border-2'
                  : ''
              )}
            >
              <CardHeader>
                <div className="flex justify-between items-center mb-3">
                  <NistIcn />
                  <Checkbox
                    className="rounded-xl h-5 w-5"
                    checked={selectedFramework === SecurityFrameworkInput.NIST}
                  />
                </div>
                <div className={headerClass}>
                  {t('accountFrameworks.nistHeader')}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-slate-400">
                  {t('accountFrameworks.nistDescription')}
                </div>
              </CardContent>
            </Card>
            <Card
              onClick={() =>
                setSelectedFramework(SecurityFrameworkInput.HITRUST)
              }
              className={cn(
                'cursor-pointer',
                selectedFramework === SecurityFrameworkInput.HITRUST
                  ? 'border-black border-2'
                  : ''
              )}
            >
              <CardHeader>
                <div className="flex justify-between items-center mb-3">
                  <HitrustIcn />
                  <Checkbox
                    className="rounded-xl h-5 w-5"
                    checked={
                      selectedFramework === SecurityFrameworkInput.HITRUST
                    }
                  />
                </div>
                <div className={headerClass}>
                  {t('accountFrameworks.highTrustHeader')}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-slate-400">
                  {t('accountFrameworks.highTrustDescription')}
                </div>
              </CardContent>
            </Card>
            {
              <Card
                onClick={() => setSelectedFramework(SecurityFrameworkInput.PCI)}
                className={cn(
                  'cursor-pointer',
                  selectedFramework === SecurityFrameworkInput.PCI
                    ? 'border-black border-2'
                    : ''
                )}
              >
                <CardHeader>
                  <div className="flex justify-between items-center mb-3">
                    <PciDssIcn />
                    <Checkbox
                      className="rounded-xl h-5 w-5"
                      checked={selectedFramework === SecurityFrameworkInput.PCI}
                    />
                  </div>
                  <div className={headerClass}>
                    {t('accountFrameworks.pciHeader')}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-slate-400">
                    {t('accountFrameworks.pciDescription')}
                  </div>
                </CardContent>
              </Card>
            }
            <Card
              onClick={() => setSelectedFramework(SecurityFrameworkInput.CIS)}
              className={cn(
                'cursor-pointer',
                selectedFramework === SecurityFrameworkInput.CIS
                  ? 'border-black border-2'
                  : ''
              )}
            >
              <CardHeader>
                <div className="flex justify-between items-center mb-3">
                  <CisIcon />
                  <Checkbox
                    className="rounded-xl h-5 w-5"
                    checked={selectedFramework === SecurityFrameworkInput.CIS}
                  />
                </div>
                <div className={headerClass}>
                  {t('accountFrameworks.cisHeader')}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-slate-400">
                  {t('accountFrameworks.cisDescription')}
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </>
  )
}
