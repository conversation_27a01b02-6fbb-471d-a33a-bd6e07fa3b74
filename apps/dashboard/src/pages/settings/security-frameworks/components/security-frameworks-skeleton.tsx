import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, Skeleton } from '@libs/ui'

export const SecurityFrameworksSkeleton = () => {
  return (
    <>
      {[1, 2, 3, 4].map((key) => (
        <Card key={key}>
          <CardHeader>
            <div className="flex justify-between items-center mb-3">
              <Skeleton className="h-8 w-12" />
              <Skeleton className="h-5 w-5 rounded-xl" />
            </div>
            <Skeleton className="h-7 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      ))}
    </>
  )
}
