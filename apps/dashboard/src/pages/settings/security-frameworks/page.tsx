import { ErrorSomething<PERSON>ent<PERSON>rong, Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@libs/ui'
import { t } from 'i18next'
import { useEffect, useState } from 'react'
import { useGetConfig, useUpdateConfig } from '../../../api/use-config-api'
import { toast } from 'sonner'
import { SecurityFramework as SecurityFrameworkInput } from 'prime-front-service-client'
import { useFlagsWrapper } from '../../../hooks/use-flags-wrapper'
import { useQueryState } from 'nuqs'
import { FrameworksTab } from './components/frameworks-tab'
import { GuidelinesTab } from './components/guidelines-tab'

export const SecurityFrameworksPage = () => {
  const { companyGuidelines } = useFlagsWrapper()
  const [tab, setTab] = useQueryState('tab', {
    defaultValue: 'frameworks',
    parse: (value) =>
      ['frameworks', 'guidelines'].includes(value) ? value : 'frameworks',
    history: 'replace',
  })

  useEffect(() => {
    if (!tab) {
      setTab('frameworks')
    }
  }, [tab, setTab])

  const {
    data: config,
    isPending: pendingConfig,
    isError: isConfigError,
    refetch: configRefetch,
  } = useGetConfig()
  const { mutate: updateConfigMutation, isPending: isUpdatePending } =
    useUpdateConfig()

  const [selectedFramework, setSelectedFramework] =
    useState<SecurityFrameworkInput>()

  useEffect(() => {
    if (config?.security_framework) {
      setSelectedFramework(config.security_framework)
    } else if (!pendingConfig) {
      setSelectedFramework(SecurityFrameworkInput.NIST)
    }
  }, [config, pendingConfig])

  const saveConfig = () => {
    if (!selectedFramework) {
      toast.error(t('errors.noFrameworkSelected'))
      return
    }
    updateConfigMutation(
      {
        AccountConfigUpdate: { security_framework: selectedFramework },
      },
      {
        onSuccess: async () => {
          await configRefetch()
          toast.success(t('updatedSecurityFrameworkSuccessfully'))
        },
        onError: async () => {
          toast.error(t('errors.updatingSecurityFrameworkFailed'))
        },
      }
    )
  }

  if (isConfigError) {
    return <ErrorSomethingWentWrong />
  }

  return (
    <div>
      <div className="page-title mb-6">
        <h1 className="text-2xl font-semibold mb-1 capitalize">
          {t('securityAndCompliance')}
        </h1>
      </div>

      <Tabs defaultValue="frameworks" value={tab} onValueChange={setTab}>
        {companyGuidelines && (
          <TabsList className="mb-6">
            <TabsTrigger value="frameworks">{t('frameworks')}</TabsTrigger>
            <TabsTrigger value="guidelines">
              {t('companyGuidelines')}
            </TabsTrigger>
          </TabsList>
        )}

        {tab === 'frameworks' && (
          <FrameworksTab
            selectedFramework={selectedFramework}
            setSelectedFramework={setSelectedFramework}
            isUpdatePending={isUpdatePending}
            pendingConfig={pendingConfig}
            saveConfig={saveConfig}
          />
        )}
        {tab === 'guidelines' && <GuidelinesTab />}
      </Tabs>
    </div>
  )
}
