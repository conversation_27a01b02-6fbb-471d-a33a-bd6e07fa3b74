import { t } from 'i18next'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  SlackIcon,
  Skeleton,
  ErrorSomethingWentWrong,
  useUrlState,
} from '@libs/ui'
import {
  CirclePlus,
  Loader2,
  MailIcon,
  PlugIcon,
  RefreshCcw,
  Unplug,
} from 'lucide-react'

import { cn } from '@libs/common'
import { NotificationItem } from './components/notification-item'
import { modalAtom, useNotifications } from './hooks/use-notifications'
import { useSetAtom } from 'jotai'
import { NotificationModal } from './components/notification-modal'
import { Link } from 'react-router'
import { useEffect, useRef } from 'react'
import { toast } from 'sonner'
import type { UrlStateProps } from '../sources/page'

export const NotificationsPage = () => {
  const {
    authTokenData,
    slackAuthLink,
    notificationsData,
    notificationsLoading,
    useGetNotificationsError,
    isLoading,
    getChannelsByIds,
    revokeSlackToken,
    getPresetNamesByIds,
    handleRefreshChannels,
    refreshChannelsIsPending,
  } = useNotifications()

  const setModalOpen = useSetAtom(modalAtom)

  const [urlState, setUrlState] = useUrlState<UrlStateProps>()
  const toastShownRef = useRef(false)

  useEffect(() => {
    if (urlState.error && !toastShownRef.current) {
      toast.error(urlState.error)
      toastShownRef.current = true
      setUrlState((prev) => {
        const { error, ...rest } = prev
        if (error) return rest
        return prev
      })
    }
  }, [setUrlState, urlState.error])

  return (
    <div className="wrap">
      <NotificationModal />

      <div className="w-full">
        <div className="mb-10">
          <h1 className="text-2xl font-semibold mb-1 capitalize">
            {t('notifications')}
          </h1>
          <p className="text-sm text-muted-foreground">
            {t('slackDescription')}
          </p>
        </div>

        <div className="mb-14">
          <h2 className="text-xl font-semibold mb-4">{t('integrations')}</h2>

          {isLoading ? (
            <Card className="p-4 rounded-3xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Skeleton className="w-5 h-5" />
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-6 w-20 rounded-full" />
                </div>
                <div className="flex gap-12 items-center">
                  <Skeleton className="h-9 w-40" />
                  <Skeleton className="h-9 w-48" />
                </div>
              </div>
            </Card>
          ) : (
            <Card className="py-3.5 px-4 rounded-[20px]">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <SlackIcon className="w-5 h-5" />
                  <span className="font-bold text-sm">{t('slack')}</span>
                  <Badge
                    className={cn(
                      'text-gray-400',
                      authTokenData
                        ? 'bg-green-100 text-green-700 border border-green-700'
                        : 'bg-gray-100 text-gray-400 border border-gray-400'
                    )}
                    variant="secondary"
                  >
                    {authTokenData ? t('connected') : t('notConnected')}
                  </Badge>
                </div>
                <div className="flex gap-4 items-center">
                  <Button
                    variant="ghost"
                    className="flex items-center gap-2 text-xs font-bold text-slate-600"
                    dataTestId="connect-slack-button"
                    disabled={refreshChannelsIsPending || !authTokenData}
                    onClick={() => {
                      handleRefreshChannels()
                    }}
                  >
                    {refreshChannelsIsPending ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      <RefreshCcw className="w-5 h-5" />
                    )}

                    {t('refreshChannels')}
                  </Button>

                  {authTokenData ? (
                    <Button
                      variant="ghost"
                      className="flex items-center gap-2 text-xs font-bold text-slate-600"
                      dataTestId="connect-slack-button"
                      onClick={() => {
                        revokeSlackToken()
                      }}
                    >
                      <Unplug className="w-5 h-5" />
                      {t('disconnectSlackAccount')}
                    </Button>
                  ) : (
                    <Button
                      variant="ghost"
                      className="flex items-center gap-2 text-xs font-bold text-slate-600"
                      dataTestId="connect-slack-button"
                    >
                      <Link
                        to={slackAuthLink?.slack_link || ''}
                        className="flex gap-2"
                      >
                        <PlugIcon className="w-5 h-5" />
                        {t('connectSlackAccount')}
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          )}
        </div>

        {useGetNotificationsError ? (
          <ErrorSomethingWentWrong />
        ) : (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4 ">
              <h2 className="text-xl font-semibold">
                {t('createNotifications')}
              </h2>

              <Button
                className="flex items-center gap-2"
                dataTestId="create-notification-button"
                disabled={notificationsLoading}
                onClick={() => setModalOpen(true)}
              >
                <CirclePlus className="w-5 h-5" />
                {t('add')}
              </Button>
            </div>

            <div className="grid grid-flow-col grid-cols-[40px_repeat(5,_minmax(0,_1fr))_70px] gap-4 items-center justify-between p-4">
              <div></div>
              <div className="font-bold">{t('notification')}</div>
              <div className="font-bold">{t('when?')}</div>
              <div className="font-bold">{t('views')}</div>
              <div className="flex items-center gap-2">
                <SlackIcon className="w-5 h-5" />
                <span className="font-bold text-sm">{t('slack')}</span>
              </div>
              <div className="flex items-center gap-2 font-bold">
                <MailIcon className="w-5 h-5" />
                {t('email')}
              </div>
              <div></div>
              <div></div>
            </div>
            <div className="space-y-4">
              {notificationsLoading ? (
                <Card className="p-4 rounded-[20px]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Skeleton className="w-5 h-5" />
                      <Skeleton className="h-4 w-12" />
                      <Skeleton className="h-6 w-20 rounded-full" />
                    </div>
                    <Skeleton className="h-9 w-40" />
                  </div>
                </Card>
              ) : (
                notificationsData?.map((notification) => (
                  <NotificationItem
                    key={crypto.randomUUID()}
                    channels={getChannelsByIds(
                      notification.slack_channel_ids || []
                    )}
                    presetNames={getPresetNamesByIds(
                      notification.selected_views || []
                    )}
                    {...notification}
                  />
                ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
