/*eslint-disable max-lines */
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { getUserTimezoneOffset } from '@libs/common'
import { toast } from 'sonner'
import {
  useAddNotification,
  useUpdateNotification,
} from '../../../../api/use-notifications-api'
import { useGetFilterPresets } from '../../../../api/use-config-api'
import { modalAtom, useNotifications } from '../hooks/use-notifications'
import {
  Button,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  Input,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Form,
  DialogFooter,
  SlackIcon,
  Separator,
  Label,
  FormDescription,
  MultiSelectCombobox,
  FormMessage,
  DialogClose,
} from '@libs/ui'
import { t } from 'i18next'
import { WeekDay } from 'prime-front-service-client'
import { formatTime, timeOptions } from '../utils'
import { Loader2, MailIcon } from 'lucide-react'
import { useSetAtom } from 'jotai'
import { useMemo } from 'react'

export type AllNotificationTypes =
  | 'digest_report'
  | 'new_cases_alert'
  | 'new_psv_alert'
  | 'scan_started'
  | 'scan_completed'

const createModalNotificationTypeValues = [
  'digest_report',
  'new_cases_alert',
  'new_psv_alert',
] as const

export const AllNotificationTypeValues = [
  'digest_report',
  'new_cases_alert',
  'new_psv_alert',
  'scan_started',
  'scan_completed',
  'design_review_completed',
] as const

const WeekDays = Object.values(WeekDay) as [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
]

const isValidEmail = (email: string): boolean => {
  const emailRegex =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/

  return emailRegex.test(email)
}

export const formSchema = z
  .object({
    notification_type: z.enum(AllNotificationTypeValues),
    selected_views: z.array(z.string()).optional(),
    timing: z.enum(['daily', 'weekly']),
    daily_time: z.string(),
    weekly_time: z.string(),
    days: z.array(z.enum(WeekDays)),
    slack_channel_ids: z.array(z.string()),
    email: z.string(),
  })
  .superRefine((data, ctx) => {
    const isScanNotification =
      data.notification_type === 'scan_started' ||
      data.notification_type === 'scan_completed'

    if (
      !isScanNotification &&
      data.notification_type !== 'new_psv_alert' &&
      data.notification_type !== 'digest_report' &&
      (!data.selected_views || data.selected_views.length === 0)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Custom views are required for this notification type',
        path: ['selected_views'],
      })
    }

    if (data.slack_channel_ids.length === 0 && !data.email) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Either Slack channel or email is required',
        path: ['slack_channel_ids'],
      })
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Either Slack channel or email is required',
        path: ['email'],
      })
    }

    if (data.email) {
      const emails = data.email.split(',').map((email) => email.trim())
      const invalidEmails = emails.filter((email) => !isValidEmail(email))

      if (invalidEmails.length > 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Invalid email format: ${invalidEmails.join(', ')}`,
          path: ['email'],
        })
      }
    }
  })

export const NotificationForm = () => {
  const setModalOpen = useSetAtom(modalAtom)
  const {
    slackChannels,
    notificationToEdit,
    isEditMode,
    setRefetchNotificationsCount,
  } = useNotifications()
  const { data: presets } = useGetFilterPresets('cases')
  const { data: containersPresets } = useGetFilterPresets('containers')

  const { mutate: addNotification, isPending: isAddPending } =
    useAddNotification()
  const { mutate: updateNotification, isPending: isUpdatePending } =
    useUpdateNotification()

  const views = useMemo(
    () => [
      ...(presets?.map((preset) => {
        return { preset: preset, type: 'workroom' }
      }) || []),
      ...(containersPresets?.map((preset) => {
        return { preset: preset, type: 'container' }
      }) || []),
    ],
    [presets, containersPresets]
  )

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    mode: 'onChange',
    defaultValues: {
      notification_type: notificationToEdit?.notification_type || undefined,
      selected_views: notificationToEdit?.selected_views || undefined,
      timing: notificationToEdit?.timing || 'daily',
      daily_time: notificationToEdit?.daily_time || '09:00',
      weekly_time: notificationToEdit?.weekly_time || '09:00',
      days: notificationToEdit?.days || [],
      slack_channel_ids: notificationToEdit?.slack_channel_ids || [],
      email: notificationToEdit?.email || '',
    },
  })

  const userTimezone = getUserTimezoneOffset()

  const isScanStartedCompleted =
    notificationToEdit?.notification_type === 'scan_started' ||
    notificationToEdit?.notification_type === 'scan_completed'

  function onSubmit(values: z.infer<typeof formSchema>) {
    const emails = values.email
      ? values.email
          .split(',')
          .map((email) => email.trim())
          .filter((email) => email.length > 0)
      : []

    const daily_time = `${values.daily_time}${userTimezone}`
    const weekly_time = `${values.weekly_time}${userTimezone}`

    const newNotification = {
      notification_type: values.notification_type,
      selected_views: values.selected_views,
      slack_channel_ids: values.slack_channel_ids,
      mail_recipients: emails,
      time: values.timing === 'daily' ? daily_time : weekly_time,
      days:
        values.days.length && values.timing === 'weekly' ? values.days : null,
      enabled: true,
    }

    if (isEditMode && notificationToEdit?.id !== undefined) {
      updateNotification(
        {
          notification_id: notificationToEdit.id,
          NotificationArgsUpdate: newNotification,
        },
        {
          onError: () => {
            toast.error('Failed to update notification')
          },
          onSuccess: () => {
            toast.success('Notification updated')
            setModalOpen(false)
            setRefetchNotificationsCount((prev) => prev + 1)
          },
        }
      )
    } else {
      addNotification(
        {
          NotificationArgsCreate: newNotification,
        },
        {
          onError: () => {
            toast.error('Failed to create notification')
          },
          onSuccess: () => {
            toast.success('Notification created')
            setModalOpen(false)
            setRefetchNotificationsCount((prev) => prev + 1)
          },
        }
      )
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="px-6 py-4 space-y-6">
          {!isScanStartedCompleted && (
            <>
              <FormField
                control={form.control}
                name="notification_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('notification')} *</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value)
                        if (value === 'new_psv_alert') {
                          form.setValue('selected_views', [])
                        }
                      }}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger
                          className="w-[240px]"
                          disabled={isEditMode}
                        >
                          <SelectValue placeholder="Pick a notification" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {(isEditMode
                          ? AllNotificationTypeValues
                          : createModalNotificationTypeValues
                        )
                          .filter((notification) => {
                            return notification !== 'design_review_completed'
                          })
                          .map((notification) => (
                            <SelectItem key={notification} value={notification}>
                              {t('notificationTypes.' + notification)}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              {form.watch('notification_type') !== 'new_psv_alert' &&
                form.watch('notification_type') !== 'digest_report' && (
                  <FormField
                    control={form.control}
                    name="selected_views"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customViews')} *</FormLabel>

                        <MultiSelectCombobox
                          onChange={(option) => {
                            field.onChange(option.map((item) => item.value))
                          }}
                          options={(views ?? []).map((preset) => ({
                            value: preset.preset.query_id,
                            label: preset.preset.name,
                            meta: preset.type,
                          }))}
                          value={views
                            ?.filter((preset) => {
                              return field?.value?.includes(
                                preset.preset.query_id
                              )
                            })
                            .map((preset) => ({
                              value: preset.preset.query_id,
                              label: preset.preset.name,
                            }))}
                          placeholder={t('pickCustomViews')}
                          emptyMessage={t('noCustomViewsFound')}
                          isInModal={true}
                        />
                      </FormItem>
                    )}
                  />
                )}

              <FormField
                control={form.control}
                name="timing"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <Label className="font-bold">{t('when?')}</Label>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-3"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="daily" />
                          </FormControl>
                          <Label>{t('dailyNotification')}</Label>
                        </FormItem>
                        <FormItem className="pl-7">
                          <FormLabel
                            className="capitalize"
                            disabled={field.value !== 'daily'}
                          >
                            {t('time')}
                          </FormLabel>
                          <Select
                            disabled={field.value !== 'daily'}
                            defaultValue={
                              notificationToEdit?.daily_time || '09:00'
                            }
                            onValueChange={(value) =>
                              form.setValue('daily_time', value)
                            }
                          >
                            <SelectTrigger size="sm">
                              <SelectValue placeholder="9:00 AM" />
                            </SelectTrigger>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time} value={time}>
                                  {formatTime(time)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription className="text-xs text-left">
                            {t('yourTimeZone')}: UTC{userTimezone}
                          </FormDescription>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="weekly" />
                          </FormControl>
                          <Label>{t('weeklyNotification')}</Label>
                        </FormItem>
                        <div className="weekly-wrapper grid grid-cols-2 grid-flow-col">
                          <FormItem className="flex flex-col gap-2 pl-7 space-y-0">
                            <FormLabel
                              className="capitalize"
                              disabled={field.value !== 'weekly'}
                            >
                              {t('day')}
                            </FormLabel>
                            <MultiSelectCombobox
                              onChange={(option) => {
                                form.setValue(
                                  'days',
                                  option.map((item) => item.value as WeekDay)
                                )
                              }}
                              options={WeekDays.map((day) => ({
                                value: day,
                                label: day,
                              }))}
                              value={form
                                .watch('days')
                                .map((day) => ({ value: day, label: day }))}
                              placeholder={t('pickYourDays')}
                              emptyMessage={t('noDaysFound')}
                              isInModal={true}
                              disabled={field.value !== 'weekly'}
                              buttonSize="sm"
                            />
                          </FormItem>
                          <FormItem className="flex flex-col gap-2 pl-7 space-y-0">
                            <FormLabel
                              className="capitalize"
                              disabled={field.value !== 'weekly'}
                            >
                              {t('time')}
                            </FormLabel>
                            <Select
                              disabled={field.value !== 'weekly'}
                              onValueChange={(value) =>
                                form.setValue('weekly_time', value)
                              }
                              defaultValue={
                                notificationToEdit?.weekly_time || '09:00'
                              }
                            >
                              <SelectTrigger size="sm">
                                <SelectValue placeholder="9:00 AM" />
                              </SelectTrigger>
                              <SelectContent>
                                {timeOptions.map((time) => (
                                  <SelectItem key={time} value={time}>
                                    {formatTime(time)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormDescription className="text-xs text-right">
                              {t('yourTimeZone')}: UTC{userTimezone}
                            </FormDescription>
                          </FormItem>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
              <Separator />
            </>
          )}

          <div className="space-y-4">
            <h3 className="flex items-center gap-2 font-bold">
              <SlackIcon className="w-5 h-5" />
              {t('slackSettings')}
            </h3>

            <FormField
              control={form.control}
              name="slack_channel_ids"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="capitalize">{t('channel')}</FormLabel>
                  <MultiSelectCombobox
                    onChange={(option) => {
                      field.onChange(option.map((item) => item.value))
                      form.trigger(['slack_channel_ids', 'email'])
                    }}
                    options={
                      slackChannels?.channels
                        ? slackChannels.channels.map((channel) => ({
                            value: channel.id,
                            label: `#${channel.name}`,
                          }))
                        : []
                    }
                    value={
                      slackChannels?.channels
                        ? slackChannels.channels
                            .filter((channel) => {
                              return field?.value?.includes(channel.id)
                            })
                            .map((channel) => ({
                              value: channel.id,
                              label: `#${channel.name}`,
                            }))
                        : []
                    }
                    placeholder={t('pickYourChannels')}
                    emptyMessage={t('noChannelsFound')}
                    isInModal={true}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="space-y-4">
            <h3 className="flex items-center gap-2 font-bold">
              <MailIcon className="w-5 h-5" />
              {t('emailSettings')}
            </h3>

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('emailAddress')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>, <EMAIL>"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e.target.value)
                        form.trigger(['slack_channel_ids', 'email'])
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <DialogFooter className="sticky bottom-0 z-50 bg-background px-6 py-4 border-t flex justify-between w-full">
          <DialogClose asChild>
            <Button
              variant="ghost"
              type="button"
              dataTestId="cancel-notification-button"
              onClick={() => setModalOpen(false)}
            >
              {t('cancel')}
            </Button>
          </DialogClose>
          <Button
            type="submit"
            dataTestId="done-notification-button"
            disabled={
              !form.formState.isValid || isAddPending || isUpdatePending
            }
          >
            {(isAddPending || isUpdatePending) && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {t('done')}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  )
}
