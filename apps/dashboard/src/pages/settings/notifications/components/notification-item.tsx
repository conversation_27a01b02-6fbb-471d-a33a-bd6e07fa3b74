import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Badge,
  Button,
  Card,
  Switch,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@libs/ui'
import {
  useDeleteNotification,
  useUpdateNotification,
} from '../../../../api/use-notifications-api'
import { PencilIcon, Trash2 } from 'lucide-react'
import type { NotificationArgs, SlackChannel } from 'prime-front-service-client'
import { toast } from 'sonner'
import { t } from 'i18next'
import { useSetAtom } from 'jotai'
import { useState } from 'react'
import { formatTime } from '../utils'
import {
  modalAtom,
  notificationToEditAtom,
  refetchNotificationsCountAtom,
} from '../hooks/use-notifications'

interface NotificationItemProps extends NotificationArgs {
  channels: SlackChannel[]
  presetNames: { name: string; type: string }[]
}

interface ListWithOverflowProps {
  items: string[] | { name: string; type: string }[]
  maxVisible?: number
  renderItem?: (
    item: string | { name: string; type: string }
  ) => React.ReactNode
}

export const ListWithOverflow = ({
  items,
  maxVisible = 1,
  renderItem = (item) =>
    typeof item === 'string' ? item : `${item.name} (${item.type})`,
}: ListWithOverflowProps) => {
  if (!items.length) return null

  if (items.length <= maxVisible) {
    return <>{renderItem(items[0])}</>
  }

  return (
    <div className="flex items-center gap-1">
      <span className="text-sm"> {renderItem(items[0])}</span>

      <Tooltip>
        <TooltipTrigger disabled>
          <Badge variant="secondary" className="cursor-pointer">
            +{items.length - maxVisible}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          {items.slice(maxVisible).map((item) => (
            <div key={crypto.randomUUID()}>{renderItem(item)}</div>
          ))}
        </TooltipContent>
      </Tooltip>
    </div>
  )
}
export const NotificationItem = (props: NotificationItemProps) => {
  const {
    notification_type,
    id,
    channels,
    days,
    time,
    mail_recipients,
    selected_views,
    presetNames,
    slack_channel_ids,
    enabled: isEnabled,
  } = props
  const setNotificationToEdit = useSetAtom(notificationToEditAtom)
  const setModalOpen = useSetAtom(modalAtom)
  const { mutate: mutateDelete } = useDeleteNotification()
  const { mutate: mutateUpdate } = useUpdateNotification()
  const setRefetchNotificationsCount = useSetAtom(refetchNotificationsCountAtom)
  const [enabled, setEnabled] = useState(isEnabled)

  const handleDelete = () => {
    mutateDelete(
      {
        notification_type,
        notification_id: id,
      },
      {
        onSuccess: () => {
          toast.success('Notification deleted')
          setRefetchNotificationsCount((prev) => prev + 1)
        },
        onError: () => {
          toast.error('Failed to delete notification')
        },
      }
    )
  }

  const handleEdit = () => {
    const notificationData = {
      id,
      notification_type,
      selected_views: selected_views || [],
      timing: (days?.length ? 'weekly' : 'daily') as 'weekly' | 'daily',
      daily_time: time?.split('+')[0] || '',
      weekly_time: time?.split('+')[0] || '',
      days: days || [],
      slack_channel_ids: slack_channel_ids || [],
      email: mail_recipients?.join(', ') || '',
      enabled: isEnabled,
    }
    setNotificationToEdit(notificationData)
    setModalOpen(true)
  }

  const handleToggle = () => {
    const emailsAndChannelsEmpty =
      !mail_recipients?.length && !slack_channel_ids?.length

    if (emailsAndChannelsEmpty) {
      handleEdit()
      return
    }

    setEnabled((prev) => !prev)
    mutateUpdate(
      {
        notification_id: id,
        NotificationArgsUpdate: {
          ...props,
          notification_type,
          enabled: !enabled,
        },
      },
      {
        onSuccess: () => {
          toast.success(
            enabled ? 'Notification disabled' : 'Notification enabled'
          )
        },
        onError: () => {
          toast.error('Failed to toggle notification')
        },
      }
    )
  }

  const isWeekly = !!days?.length

  const renderView = (item: string | { name: string; type: string }) => {
    if (typeof item === 'string') {
      return <span>{item}</span>
    }

    return (
      <div className="flex items-center gap-2">
        {item.name}
        <div className="text-gray-400 capitalize">({item?.type})</div>
      </div>
    )
  }

  return (
    <Card className="p-4 rounded-[20px]">
      <div className="grid grid-flow-col grid-cols-[40px_repeat(5,_minmax(0,_1fr))_70px] gap-4 items-center justify-between text-sm">
        <div className="switch-wrapper">
          <Switch checked={enabled} onCheckedChange={handleToggle} />
        </div>
        <div>
          <h3 className="font-bold">
            {t('notificationTypes.' + notification_type)}
          </h3>
        </div>

        <div className="grid items-center grid-flow-col gap-2 text-sm">
          {time && (
            <div>
              {isWeekly ? (
                <span>
                  {days?.map((day, i) => (
                    <span key={day}>
                      {day}
                      {i < days.length - 1 ? ', ' : ''} {'  '}
                    </span>
                  ))}
                  at {formatTime(time || '')}
                </span>
              ) : (
                <span>Daily at {formatTime(time || '')}</span>
              )}
            </div>
          )}
        </div>

        <div>
          {!!presetNames.length && (
            <ListWithOverflow items={presetNames} renderItem={renderView} />
          )}
        </div>

        <div className="text-sm flex items-center gap-2">
          {!!channels.length && (
            <ListWithOverflow
              items={channels.map((channel) => channel.name)}
              renderItem={(name) => (
                <span className="font-bold">#{name as string}</span>
              )}
            />
          )}
        </div>

        <div className="grid items-center grid-flow-col  gap-2">
          {!!mail_recipients?.length && (
            <div className="flex items-center gap-2">
              <ListWithOverflow items={mail_recipients} />
            </div>
          )}
        </div>

        <div className="flex items-center">
          <Button
            onClick={handleEdit}
            dataTestId="edit-notification-button"
            variant="ghost"
            size="icon"
          >
            <PencilIcon className="w-5 h-5" />
          </Button>
          {notification_type !== 'scan_started' &&
            notification_type !== 'scan_completed' && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    dataTestId="delete-button"
                  >
                    <Trash2 className="w-5 h-5 text-gray-500" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      {t('areYouAbsolutelySure')}?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      {t('thisActionCannotBeUndone')}.{' '}
                      {t('thisWillPermanentlyDeleteNotification')}.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                    <AlertDialogAction asChild>
                      <Button
                        variant="destructive"
                        className="bg-destructive hover:bg-destructive/80"
                        dataTestId="delete-notification"
                        onClick={handleDelete}
                      >
                        {t('delete')}
                      </Button>
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
        </div>
      </div>
    </Card>
  )
}
