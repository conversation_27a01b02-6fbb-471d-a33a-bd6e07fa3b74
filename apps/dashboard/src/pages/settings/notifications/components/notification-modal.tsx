/*eslint-disable max-lines */
import { X } from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@libs/ui'
import { DialogClose } from '@radix-ui/react-dialog'

import { useAtom } from 'jotai'

import { modalAtom, useNotifications } from '../hooks/use-notifications'
import { t } from 'i18next'
import { NotificationForm } from './notification-form'

export function NotificationModal() {
  const [modalOpen, setModalOpen] = useAtom(modalAtom)
  const { setNotificationToEdit, isEditMode } = useNotifications()

  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent
        onCloseAutoFocus={() => setNotificationToEdit(null)}
        className="overflow-hidden flex flex-col gap-0 p-0 max-h-[85vh] sm:max-w-[400px] "
      >
        <DialogHeader className="sticky top-0 z-50 bg-background px-6 py-4 border-b flex flex-row items-center justify-between">
          <DialogTitle className="text-xl font-semibold">
            {isEditMode ? t('editNewNotification') : t('createNewNotification')}
          </DialogTitle>
          <DialogClose>
            <X className="h-4 w-4" />
            <span className="sr-only">{t('close')}</span>
          </DialogClose>
        </DialogHeader>
        <div className="flex-1 overflow-y-auto space-y-6">
          <NotificationForm />
        </div>
      </DialogContent>
    </Dialog>
  )
}
