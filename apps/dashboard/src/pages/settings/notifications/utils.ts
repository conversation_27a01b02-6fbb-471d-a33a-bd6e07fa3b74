import { z } from 'zod'

export const timeOptions = [
  '00:00',
  '01:00',
  '02:00',
  '03:00',
  '04:00',
  '05:00',
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00',
]

/// This function formats the time to a 12-hour format
export const formatTime = (time: string) => {
  const [hours] = time.split(':')
  const hour = parseInt(hours, 10)
  const ampm = hour < 12 ? 'AM' : 'PM'
  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
  return `${displayHour}:00 ${ampm}`
}

export const slackNotificationSchema = z.object({
  slackChannelId: z.string().min(1, { message: 'Select a channel' }),
  dailyDigestTime: z.string().min(1),
  dailyDigestEnabled: z.boolean(),
  scanStartedEnabled: z.boolean(),
  scanCompletedEnabled: z.boolean(),
})
