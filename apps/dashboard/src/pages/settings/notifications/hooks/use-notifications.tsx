import { useMemo } from 'react'
import { atom, useAtom } from 'jotai'
import {
  useGetSlackChannels,
  useIntegrationGetAuthLink,
  useSlackGetToken,
  useSlackRefreshChannels,
  useSlackRevokeToken,
} from '../../../../api/use-integration-api'
import { useGetNotifications } from '../../../../api/use-notifications-api'
import type { SlackChannel } from 'prime-front-service-client'
import { toast } from 'sonner'
import { t } from 'i18next'
import type { formSchema } from '../components/notification-form'
import type { z } from 'zod'
import { usePresets } from '../../../workroom/hooks'
import { usePresets as useContainersPresets } from '../../../containers/hooks'

export const refetchNotificationsCountAtom = atom(1)
export const modalAtom = atom(false)

export const notificationToEditAtom = atom<
  (z.infer<typeof formSchema> & { id?: number }) | null
>(null)

export const useNotifications = () => {
  const { presets } = usePresets()
  const { presets: containerPresets } = useContainersPresets()

  const [notificationToEdit, setNotificationToEdit] = useAtom(
    notificationToEditAtom
  )

  const [refetchNotificationsCount, setRefetchNotificationsCount] = useAtom(
    refetchNotificationsCountAtom
  )

  const { isPending: authLinkPending, data: slackAuthLink } =
    useIntegrationGetAuthLink()

  const { mutate: mutateRevokeToken } = useSlackRevokeToken()

  const {
    data: authTokenData,
    isPending: authTokenPending,
    isRefetching: authTokenRefetching,
    refetch: refetchAuthToken,
  } = useSlackGetToken()

  const { data: slackChannels, isFetching: channelsFetching } =
    useGetSlackChannels(authTokenData)

  const {
    data: notificationsData,
    isLoading: notificationsLoading,
    isError: useGetNotificationsError,
  } = useGetNotifications(refetchNotificationsCount)

  const { mutate: refreshChannelsMutate, isPending: refreshChannelsIsPending } =
    useSlackRefreshChannels()

  const isChannelLoading =
    authTokenPending || (!!authTokenData && channelsFetching)

  const isLoading =
    authTokenPending ||
    authTokenRefetching ||
    isChannelLoading ||
    authLinkPending

  const getChannelsByIds = useMemo(
    () =>
      (channelIds: string[]): SlackChannel[] => {
        return (
          slackChannels?.channels?.filter((c) => channelIds.includes(c.id)) ||
          []
        )
      },
    [slackChannels?.channels]
  )

  const getPresetNamesByIds = (
    presetIds: string[]
  ): { name: string; type: string }[] => {
    return [
      ...presets
        .filter((preset) => presetIds.includes(preset.query_id))
        .map((preset) => {
          return { name: preset.name, type: 'Workroom' }
        }),
      ...containerPresets
        .filter((preset) => presetIds.includes(preset.query_id))
        .map((preset) => {
          return { name: preset.name, type: 'Container' }
        }),
    ]
  }

  const revokeSlackToken = () => {
    mutateRevokeToken(undefined, {
      onSuccess: () => {
        toast.success(t('slackDisconnected'))
        refetchAuthToken()
      },
      onError: () => {
        toast.error(t('slackDisconnectError'))
      },
    })
  }

  const handleRefreshChannels = () => {
    refreshChannelsMutate(undefined, {
      onError: () => {
        toast.error(t('failedToRefreshChannels'))
      },
      onSuccess: () => {
        toast.success(t('channelsRefreshed'))
      },
    })
  }

  return {
    authTokenData,
    slackAuthLink,
    slackChannels,
    notificationsData,
    notificationsLoading,
    useGetNotificationsError,
    isLoading,
    getChannelsByIds,
    revokeSlackToken,
    notificationToEdit,
    setNotificationToEdit,
    isEditMode: !!notificationToEdit,
    setRefetchNotificationsCount,
    getPresetNamesByIds,
    handleRefreshChannels,
    refreshChannelsIsPending,
  }
}
