import { z } from 'zod'

export type FormFieldsType = 'jira_url' | 'email' | 'api_token'

export interface SourceInfo {
  jira_url: string
  email: string
  jql?: string
  name?: string
  since_in_days?: number
  projects?: string[]
}

const schemaTransform = (schema: z.ZodObject<any>) => {
  return Object.entries(schema.shape).map(([key, value]) => {
    return {
      fieldName: key,
      description:
        value instanceof z.ZodString || value instanceof z.ZodOptional
          ? value._def.description
          : '',
    }
  })
}

export const baseFormSchema = z.object({
  name: z.string().min(3, 'Name must contain at least 3 character(s)'),
  jira_url: z
    .string()
    .url()
    .startsWith('https://')
    .includes('atlassian')
    .min(5, 'Jira URL is invalid'),
  email: z.string().email(),
  api_token: z
    .string()
    .min(5, 'API Token must contain at least 5 character(s)')
    .describe('API Token will be hidden after submit'),
})

export const editFormSchema = baseFormSchema.extend({
  api_token: z
    .string()
    .optional()
    .describe('API Token will be hidden after submit'),
})

export const systemFormSchema = z.object({
  projects: z
    .array(z.string())
    .nonempty('At least one project must be selected'),
  since_in_days: z.number(),
})

export const customFormSchema = z.object({
  jql: z.string().describe('JQL is optional').nonempty(),
})

export const baseFormFields = schemaTransform(baseFormSchema)
