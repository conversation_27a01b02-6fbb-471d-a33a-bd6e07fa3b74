import {
  <PERSON><PERSON>,
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormControl,
  FormDescription,
  Input,
} from '@libs/ui'
import { Loader2 } from 'lucide-react'
import type { FormFieldsType } from '../utils'
import { baseFormFields } from '../utils'
import { SourceSettings } from './source-settings'
import { t } from 'i18next'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import type { z } from 'zod'
import type {
  JiraInsertModel,
  ProjectResponse,
} from 'prime-front-service-client'
import {
  useAddJiraSource,
  useGetProjects,
} from '../../../../api/use-sources-api'
import { baseFormSchema, systemFormSchema } from '../utils'
import { refetchSourcesAtom, sourceModalAtom } from '../page'
import { useSet<PERSON>tom } from 'jotai'
import { isOlderThanNDays } from './utils'

export const AddSourceForm = () => {
  const setOpen = useSetAtom(sourceModalAtom)
  const setRefetchCount = useSetAtom(refetchSourcesAtom)
  const [step, setStep] = useState<'initial' | 'settings'>('initial')
  const [projectsOptions, setProjectsOptions] = useState<ProjectResponse[]>([])
  const [baseFormValues, setBaseFormValues] = useState<z.infer<
    typeof baseFormSchema
  > | null>(null)

  const { mutateAsync: addSource, isPending: isAdding } = useAddJiraSource()
  const { mutateAsync: getProjects, isPending: isFetching } = useGetProjects()
  const [hasMoreProjects, setHasMoreProjects] = useState(true)
  const [isFetchingMore, setIsFetchingMore] = useState(false)
  const [projectOffset, setProjectOffset] = useState(0)

  const baseForm = useForm<z.infer<typeof baseFormSchema>>({
    resolver: zodResolver(baseFormSchema),
    defaultValues: {
      name: '',
      jira_url: '',
      email: '',
      api_token: '',
    },
    mode: 'onChange',
  })

  const sourceSettingsForm = useForm<{
    projects: string[]
    since_in_days: number
  }>({
    resolver: zodResolver(systemFormSchema),
    defaultValues: {
      projects: [],
      since_in_days: 30,
    },
  })

  const projects = sourceSettingsForm.getValues('projects')
  const since_in_days = sourceSettingsForm.getValues('since_in_days')

  const handleProjectChange = (project: string) => {
    const newProjects = projects.includes(project)
      ? projects.filter((p) => p !== project)
      : [...projects, project]
    sourceSettingsForm.setValue('projects', newProjects)
  }

  const onContinue = async (values: z.infer<typeof baseFormSchema>) => {
    if (!(await baseForm.trigger())) return

    try {
      const offset = 0
      const limit = 100
      const firstPage = await getProjects({
        JiraInsertModel: {
          ...values,
        },
        limit,
        offset,
      })

      const shouldStop = firstPage.results.some((project) =>
        isOlderThanNDays(project?.insight?.last_issue_update_time, 90)
      )

      setProjectsOptions(firstPage.results)
      setProjectOffset(limit)
      setHasMoreProjects(!shouldStop && (firstPage.has_next ?? false))
      setBaseFormValues(values)
      setStep('settings')
    } catch (error: any) {
      const detail = await error?.response?.json()?.detail
      toast.error(detail || t('errors.failedToFetchProjects'))
    }
  }

  const loadMoreProjects = async () => {
    if (!hasMoreProjects || isFetchingMore) return
    setIsFetchingMore(true)

    const limit = 100

    const nextPage = await getProjects({
      JiraInsertModel: {
        ...baseFormValues,
      } as JiraInsertModel,
      limit,
      offset: projectOffset,
    })

    const shouldStop = nextPage.results.some((project) =>
      isOlderThanNDays(project?.insight?.last_issue_update_time, 90)
    )

    setProjectsOptions((prev) => [...prev, ...nextPage.results])
    setProjectOffset((prev) => prev + 100)
    setHasMoreProjects(!shouldStop && (nextPage.has_next ?? false))
    setIsFetchingMore(false)
  }

  const onSubmit = async (values: z.infer<typeof baseFormSchema>) => {
    let valuesToSubmit: JiraInsertModel = {
      ...values,
    }

    valuesToSubmit = {
      ...valuesToSubmit,
      jql_parameters_filter: {
        projects: projects.length ? projects : [],
        since_in_days,
      },
    }

    try {
      await addSource({
        JiraInsertModel: {
          ...valuesToSubmit,
        },
      })
      toast.success(t('sourceAddedSuccessfully'))
      baseForm.reset()
      sourceSettingsForm.reset()
      setOpen(false)
      setRefetchCount((prev) => prev + 1)
    } catch (error: any) {
      const detail = await error?.response?.json()?.detail
      toast.error(detail || t('errors.failedToAddSource'))
    }
  }

  const isFormValid =
    step === 'initial'
      ? baseForm.formState.isValid
      : sourceSettingsForm.formState.isValid

  const selectedProjects = sourceSettingsForm.watch('projects')

  if (step === 'settings') {
    return (
      <SourceSettings
        baseForm={baseForm}
        sourceSettingsForm={sourceSettingsForm}
        projectsOptions={projectsOptions}
        updatePending={isAdding}
        selectedProjects={selectedProjects}
        loadMoreProjects={loadMoreProjects}
        hasMoreProjects={hasMoreProjects}
        isFetchingMore={isFetchingMore}
        handleProjectChange={handleProjectChange}
        onSubmit={onSubmit}
      />
    )
  }

  if (step === 'initial') {
    return (
      <Form {...baseForm}>
        <form
          onSubmit={baseForm.handleSubmit(onContinue)}
          className="space-y-4"
        >
          {step === 'initial' && (
            <>
              {baseFormFields.map(({ fieldName, description }) => (
                <FormField
                  key={fieldName}
                  name={fieldName as FormFieldsType}
                  control={baseForm.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t(`sourceForm.${fieldName}`)}
                        <span className="text-red-500 mx-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      {description && (
                        <FormDescription className="">
                          {description}
                        </FormDescription>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
              <Button
                type="submit"
                disabled={!isFormValid || isFetching}
                dataTestId="continue-button"
              >
                {isFetching && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {t('continue')}
              </Button>
            </>
          )}
        </form>
      </Form>
    )
  }
}
