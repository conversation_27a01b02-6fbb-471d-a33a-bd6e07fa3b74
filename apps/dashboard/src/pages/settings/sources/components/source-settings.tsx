/*eslint-disable max-lines*/
import {
  Button,
  Checkbox,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@libs/ui'
import { t } from 'i18next'
import {
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  InfoIcon,
  Loader2,
  SettingsIcon,
} from 'lucide-react'
import type { ProjectResponse } from 'prime-front-service-client'
import type { UseFormReturn } from 'react-hook-form'
import { sourceModalAtom } from '../page'
import { useSetAtom } from 'jotai'
import { useFlagsWrapper } from '../../../../hooks/use-flags-wrapper'
import { useEffect, useMemo, useState } from 'react'

interface SourceSettingsProps {
  baseForm: UseFormReturn<any>
  sourceSettingsForm: UseFormReturn<any>
  projectsOptions: Array<ProjectResponse>
  updatePending: boolean
  selectedProjects: Array<string>
  showNumberOfDays?: boolean
  loadMoreProjects?: () => void
  hasMoreProjects?: boolean
  isFetchingMore?: boolean
  handleProjectChange: (project: string) => void
  onSubmit: (data: any) => void
  onCancel?: () => void
}

export const SourceSettings = ({
  baseForm,
  sourceSettingsForm,
  projectsOptions,
  updatePending,
  selectedProjects,
  showNumberOfDays = true,
  handleProjectChange,
  loadMoreProjects,
  hasMoreProjects,
  isFetchingMore,
  onSubmit,
  onCancel,
}: SourceSettingsProps) => {
  const defaultDaysValue = '90'
  const setOpen = useSetAtom(sourceModalAtom)
  const { sourceDaysBack } = useFlagsWrapper()
  const [sortConfig, setSortConfig] = useState<{
    key: 'name' | 'updatedAt' | null
    direction: 'asc' | 'desc'
  }>({ key: 'updatedAt', direction: 'desc' })

  const [searchTerm, setSearchTerm] = useState('')
  const [showPermissionProjects, setShowPermissionProjects] =
    useState<boolean>(false)

  useEffect(() => {
    if (hasMoreProjects) {
      loadMoreProjects?.()
    }
  }, [hasMoreProjects, projectsOptions])

  const handleSort = (key: 'name' | 'updatedAt') => {
    setSortConfig((prev) => {
      if (prev.key === key) {
        return { key, direction: prev.direction === 'asc' ? 'desc' : 'asc' }
      } else {
        return { key, direction: 'asc' }
      }
    })
  }

  const filteredAndSortedProjects = useMemo(() => {
    let filtered = projectsOptions.filter((project) =>
      project.name.toLowerCase().includes(searchTerm.toLowerCase())
    )

    if (showPermissionProjects) {
      filtered = filtered.filter(
        (project) =>
          project?.access?.view_project_access &&
          project?.access?.view_issues_access
      )
    }

    if (!sortConfig.key) return filtered

    const sorted = [...filtered].sort((a, b) => {
      if (sortConfig.key === 'name') {
        return a.name.localeCompare(b.name)
      } else if (sortConfig.key === 'updatedAt') {
        const aTime = a?.insight?.last_issue_update_time?.getTime() || 0
        const bTime = b?.insight?.last_issue_update_time?.getTime() || 0
        return aTime - bTime
      }
      return 0
    })

    return sortConfig.direction === 'asc' ? sorted : sorted.reverse()
  }, [projectsOptions, sortConfig, searchTerm, showPermissionProjects])

  return (
    <Form {...sourceSettingsForm}>
      <form onSubmit={baseForm.handleSubmit(onSubmit)}>
        <div className="space-y-4">
          <FormField
            name="projects"
            control={sourceSettingsForm.control}
            render={() => (
              <FormItem>
                <FormLabel>
                  <div className="mb-1">Select projects for filtering</div>
                  <div className="flex items-center gap-2 bg-warning-foreground px-2 py-1.5 rounded-md text-xs font-light mb-4 text-amber-700">
                    <InfoIcon size={18} /> If no fields are selected, all fields
                    will be used by default
                  </div>
                </FormLabel>
                <FormControl>
                  <div>
                    <div className="flex items-center gap-4">
                      <Input
                        className="my-4"
                        placeholder="Search projects..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        id="project-search-input"
                      />
                      <div className="flex items-center gap-1 text-sm font-medium whitespace-nowrap text-slate-500">
                        <Switch
                          checked={showPermissionProjects}
                          onCheckedChange={setShowPermissionProjects}
                        />
                        {t('projectsWithPermission')}
                      </div>
                    </div>
                    <div className="max-h-72 overflow-auto">
                      <Table className="text-sm text-muted-foreground capitalize border-collapse w-full">
                        <TableHeader className="sticky top-0 bg-background z-10">
                          <TableRow>
                            <TableHead></TableHead>
                            <TableHead className="text-left">
                              <div className="flex items-center gap-1">
                                {t('name')}
                                <Button
                                  type="button"
                                  size="sm"
                                  variant="ghost"
                                  className="flex items-center px-2"
                                  onClick={() => handleSort('name')}
                                  dataTestId="sort-by-name"
                                >
                                  {sortConfig.key === 'name' ? (
                                    sortConfig.direction === 'asc' ? (
                                      <ArrowUp size={14} />
                                    ) : (
                                      <ArrowDown size={14} />
                                    )
                                  ) : (
                                    <ArrowUpDown size={14} />
                                  )}
                                </Button>
                              </div>
                            </TableHead>
                            <TableHead className="text-left">
                              <div className="flex items-center gap-1">
                                {t('lastUpdated')}
                                <Button
                                  type="button"
                                  size="sm"
                                  variant="ghost"
                                  className="flex items-center px-2"
                                  onClick={() => handleSort('updatedAt')}
                                  dataTestId="sort-by-updated"
                                >
                                  {sortConfig.key === 'updatedAt' ? (
                                    sortConfig.direction === 'asc' ? (
                                      <ArrowUp size={14} />
                                    ) : (
                                      <ArrowDown size={14} />
                                    )
                                  ) : (
                                    <ArrowUpDown size={14} />
                                  )}
                                </Button>
                              </div>
                            </TableHead>
                            <TableHead className="text-left">
                              {t('issues')}
                            </TableHead>
                            {!showNumberOfDays && (
                              <TableHead className="text-left">
                                {t('monitor')}
                              </TableHead>
                            )}
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredAndSortedProjects.map((project) => {
                            const disabled =
                              !project?.access?.view_project_access ||
                              !project?.access?.view_issues_access

                            return (
                              <TableRow
                                key={crypto.randomUUID()}
                                className={
                                  disabled
                                    ? 'opacity-50 pointer-events-none'
                                    : 'cursor-pointer'
                                }
                                onClick={() => handleProjectChange(project.key)}
                              >
                                <TableCell>
                                  <Checkbox
                                    checked={selectedProjects.includes(
                                      project.key
                                    )}
                                    onChange={() =>
                                      handleProjectChange(project.key)
                                    }
                                    className="pointer-events-none"
                                  />
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <span className="capitalize">
                                      {project.name}
                                    </span>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {project?.insight?.last_issue_update_time?.toLocaleDateString()}
                                </TableCell>
                                <TableCell>
                                  {project?.insight?.total_issue_count}
                                </TableCell>
                                {!showNumberOfDays && (
                                  <TableCell>
                                    <Button
                                      dataTestId="configure"
                                      variant="outline"
                                      className="flex items-center gap-1 capitalize"
                                    >
                                      <SettingsIcon className="w-4 h-4" />
                                      {t('configure')}
                                    </Button>
                                  </TableCell>
                                )}
                              </TableRow>
                            )
                          })}
                        </TableBody>
                      </Table>
                    </div>
                    {isFetchingMore && hasMoreProjects && (
                      <div className="flex justify-center py-4">
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </div>
                    )}
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
          {showNumberOfDays && (
            <FormField
              name="since_in_days"
              control={sourceSettingsForm.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('numberOfDaysToPullDataBasedOn')}</FormLabel>
                  {sourceDaysBack ? (
                    <FormControl>
                      <Input
                        required
                        type="number"
                        inputMode="numeric"
                        pattern="[0-9]*"
                        placeholder={t('numberOfDaysToPullDataBasedOn')}
                        value={field.value ?? ''}
                        onChange={(e) => {
                          field.onChange(Number(e.target.value))
                        }}
                      />
                    </FormControl>
                  ) : (
                    <FormControl>
                      <Select
                        onValueChange={(value) => field.onChange(Number(value))}
                        value={field.value?.toString() || defaultDaysValue}
                      >
                        <SelectTrigger className="w-[200px] capitalize">
                          <SelectValue placeholder={t('selectDaysNumber')} />
                        </SelectTrigger>
                        <SelectContent>
                          {[30, 60, 90].map((days) => (
                            <SelectItem key={days} value={days.toString()}>
                              {days.toString()} {t('days')}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                  )}
                </FormItem>
              )}
            />
          )}

          <div className="flex justify-start gap-4 pt-4">
            <Button
              type="submit"
              disabled={updatePending}
              className="capitalize"
              dataTestId="save-source-settings"
            >
              {updatePending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {t('save')}
            </Button>
            <Button
              type="button"
              variant="outline"
              className="capitalize bg-transparent shadow-none"
              dataTestId="cancel-source-settings"
              onClick={
                onCancel
                  ? onCancel
                  : () => {
                      setOpen(false)
                    }
              }
            >
              {t('cancel')}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  )
}
