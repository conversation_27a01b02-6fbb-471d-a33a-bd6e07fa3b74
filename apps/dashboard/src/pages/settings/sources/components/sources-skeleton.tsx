import { Skeleton } from '@libs/ui'

export const SourcesSkeleton = () => {
  return (
    <div data-testid="sources-skeleton" className="space-y-4">
      {[1, 2, 3].map((key) => (
        <div
          key={key}
          className="w-full px-6 py-4 bg-white rounded-xl shadow border border-gray-200 justify-between items-center inline-flex"
        >
          <div className="flex-col justify-start items-start gap-2 inline-flex">
            <div className="rounded-md justify-start items-start gap-2 inline-flex">
              <Skeleton className="h-8 w-8 mt-1" />
              <div className="flex-col justify-start items-start gap-1 inline-flex ml-2">
                <Skeleton className="h-8 w-40" />
                <Skeleton className="h-4 w-60" />
              </div>
            </div>
          </div>
          <div>
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
      ))}
    </div>
  )
}
