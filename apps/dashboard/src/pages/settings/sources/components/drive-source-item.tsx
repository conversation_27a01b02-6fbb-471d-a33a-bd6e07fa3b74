import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  GDriveIcn,
  Separator,
} from '@libs/ui'
import { Loader2, Pencil } from 'lucide-react'
import type { SourceModelResponse } from 'prime-front-service-client'
import { t } from 'i18next'
import { refetchSourcesAtom, sourceModalAtom } from '../page'
import { useAtom } from 'jotai'
import { useDeleteSource } from '../../../../api/use-sources-api'
import { toast } from 'sonner'
import { useSetAtom } from 'jotai/index'

interface SourcesTableItemProps {
  source: SourceModelResponse
}

export const DriveSourceItem = ({ source }: SourcesTableItemProps) => {
  const [open, setOpen] = useAtom(sourceModalAtom)
  const deleteSourceMutation = useDeleteSource()
  const refetchSources = useSetAtom(refetchSourcesAtom)

  const onDelete = (source_id: number) => {
    deleteSourceMutation.mutate(source_id, {
      onSuccess: () => {
        toast.success(t('sourceDeletedSuccessfully'))
        refetchSources((prev) => prev + 1)
        setOpen(false)
      },
      onError: () => {
        toast.error(t('errors.failedToDeleteSource'))
      },
    })
  }

  return (
    <div
      key={source.id}
      className="w-full px-6 py-4 bg-white rounded-xl shadow border border-gray-200 justify-between items-center inline-flex"
    >
      <div className="flex-col justify-start items-start gap-2 inline-flex">
        <div className="rounded-md justify-start items-start gap-2 inline-flex">
          <GDriveIcn className="mt-1" />
          <div className="flex-col justify-start items-start gap-1 inline-flex ml-2">
            <div className="text-gray-600 text-xl font-bold capitalize">
              {t('gDrive')}
            </div>
          </div>
        </div>
      </div>
      <div>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button variant="ghost" dataTestId="edit-source-button">
              <Pencil />
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[470px]">
            <DialogHeader>
              <DialogTitle className="text-xl capitalize mb-3">
                {t('edit')} {t('source')}
              </DialogTitle>
            </DialogHeader>
            <div className="text-sm font-light text-slate-500">
              {t('gDriveAccountDescription')}
            </div>
            <Separator className="my-2" />
            <div className="text-xs text-slate-400 font-light">
              {t('gDriveDeleteWarning')}
            </div>

            <div className="flex items-start justify-between mt-4">
              <div>
                <DialogClose>
                  <Button
                    className="capitalize"
                    variant="ghost"
                    dataTestId="cancel-button"
                  >
                    {t('cancel')}
                  </Button>
                </DialogClose>
              </div>
              <Button
                variant="destructive"
                onClick={() => onDelete(source.id)}
                disabled={deleteSourceMutation.isPending}
                dataTestId="delete-button"
              >
                {deleteSourceMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {t('delete')}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
