import { useForm } from 'react-hook-form'
import { editFormSchema, systemFormSchema } from '../utils'
import { zodResolver } from '@hookform/resolvers/zod'
import type z from 'zod'
import type {
  JiraInsertModel,
  ProjectResponse,
} from 'prime-front-service-client'
import { useCallback, useState } from 'react'
import {
  useDeleteSource,
  useGetJiraProjectsBySourceId,
  useGetSource,
  useUpdateJiraSource,
} from '../../../../api/use-sources-api'
import { toast } from 'sonner'
import { t } from 'i18next'
import { refetchSourcesAtom, sourceModalAtom } from '../page'
import { useSetAtom } from 'jotai'
import { documentsModalAtom } from '../../../documents/page'

export type SourceFormStep = 'initial' | 'settings'

export const useSourceForm = (sourceId: number) => {
  const { data: source, isPending: isSourceLoading } = useGetSource(+sourceId)

  const updateSourceMutation = useUpdateJiraSource()
  const deleteSourceMutation = useDeleteSource()
  const getJiraProjectsBySourceIdMutation = useGetJiraProjectsBySourceId()

  const [projectsOptions, setProjectsOptions] = useState<ProjectResponse[]>([])
  const [step, setStep] = useState<SourceFormStep>('initial')
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])
  const refetchSources = useSetAtom(refetchSourcesAtom)
  const setOpen = useSetAtom(sourceModalAtom)
  const setOpenDocuments = useSetAtom(documentsModalAtom)

  const editBaseForm = useForm<z.infer<typeof editFormSchema>>({
    resolver: zodResolver(editFormSchema),
    defaultValues: {
      jira_url: '',
      email: '',
      api_token: '',
      name: '',
    },
  })

  const editSourceSettingsForm = useForm<{
    projects: string[]
    since_in_days: number
  }>({
    resolver: zodResolver(systemFormSchema),
    defaultValues: {
      projects: [],
      since_in_days: 30,
    },
  })

  const projects = editSourceSettingsForm.getValues('projects')

  const onContinue = useCallback(async () => {
    const commonFormValues = editBaseForm.getValues()
    const settingsFormValues = editSourceSettingsForm.getValues()

    const jiraInsertModel: JiraInsertModel = {
      email: commonFormValues.email,
      jira_url: commonFormValues.jira_url,
      api_token: commonFormValues.api_token || '',
      name: commonFormValues.name,
    }

    if (settingsFormValues.projects.length > 0) {
      jiraInsertModel.jql_parameters_filter = {
        projects: selectedProjects,
        since_in_days: settingsFormValues.since_in_days || 90,
      }
    }

    try {
      await updateSourceMutation.mutateAsync({
        source_id: sourceId,
        JiraInsertModel: {
          email: jiraInsertModel.email,
          api_token: jiraInsertModel.api_token,
          jira_url: jiraInsertModel.jira_url,
          jql_parameters_filter: jiraInsertModel.jql_parameters_filter || null,
          name: jiraInsertModel.name,
        },
      })

      let allProjects: ProjectResponse[] = []
      let offset = 0
      const limit = 100
      let hasNext = true

      while (hasNext) {
        const response = await getJiraProjectsBySourceIdMutation.mutateAsync({
          source_id: sourceId,
          limit,
          offset,
        })

        allProjects = [...allProjects, ...response.results]
        offset += limit
        hasNext = response.has_next ?? false
      }

      setProjectsOptions(allProjects)
      setStep('settings')
    } catch (error: any) {
      const detail = await error?.response?.json()?.detail
      toast.error(detail || t('errors.failedToFetchProjects'))
    }
  }, [
    editBaseForm,
    editSourceSettingsForm,
    getJiraProjectsBySourceIdMutation,
    selectedProjects,
    sourceId,
    updateSourceMutation,
  ])

  const handleEditFormSubmit = async () => {
    const settingsValues = editSourceSettingsForm.getValues()

    return await updateSourceMutation.mutateAsync(
      {
        source_id: Number(sourceId),
        JiraInsertModel: {
          email: editBaseForm.getValues().email,
          jira_url: editBaseForm.getValues().jira_url,
          api_token: editBaseForm.getValues().api_token || '',
          name: editBaseForm.getValues().name,
          jql_parameters_filter: {
            projects: source?.info?.jql_parameters_filter?.projects || [],
            since_in_days: settingsValues?.since_in_days,
          },
          design_review_projects: selectedProjects,
        } as JiraInsertModel,
      },
      {
        onSuccess: async () => {
          toast.success(t('sourceUpdatedSuccessfully'))

          editBaseForm.reset()
          editSourceSettingsForm.reset()
          setOpen(false)
          setOpenDocuments(false)
          refetchSources((prev) => prev + 1)
        },
        onError: async () => {
          toast.error(t('errors.failedToUpdateSource'))
        },
      }
    )
  }

  const handleProjectChange = (project: string) => {
    const newProjects = projects.includes(project)
      ? projects.filter((p) => p !== project)
      : [...projects, project]
    editSourceSettingsForm.setValue('projects', newProjects)
    setSelectedProjects(newProjects)
  }

  const onDeleteSource = (source_id: number) => {
    deleteSourceMutation.mutate(source_id, {
      onSuccess: async () => {
        toast.success(t('sourceDeletedSuccessfully'))
        setOpen(false)
        refetchSources((prev) => prev + 1)
      },
      onError: () => {
        toast.error(t('errors.failedToDeleteSource'))
      },
    })
  }

  return {
    editBaseForm,
    editSourceSettingsForm,
    projectsOptions,
    onContinue,
    handleEditFormSubmit,
    updateSourceMutation,
    selectedProjects,
    setSelectedProjects,
    handleProjectChange,
    onDeleteSource,
    isDeletingSource: deleteSourceMutation.isPending,
    source,
    isSourceLoading,
    step,
    setStep,
    setProjectsOptions,
  }
}
