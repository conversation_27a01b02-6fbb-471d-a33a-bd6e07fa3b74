import {
  AtlassianIcn,
  Button,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  ErrorSomethingWentWrong,
  GDriveIcn,
  useUrlState,
} from '@libs/ui'
import { PlusIcon } from 'lucide-react'
import {
  useAddGoogleSource,
  useGetAllSources,
} from '../../../api/use-sources-api'
import { AddSourceForm } from './components/add-source-form'
import { atom, useAtom, useAtomValue } from 'jotai'
import { ScopeProvider } from 'jotai-scope'
import { t } from 'i18next'
import { useEffect, useRef } from 'react'
import { SourceItem } from './components/source-item'
import { DriveSourceItem } from './components/drive-source-item'
import { SourcesSkeleton } from './components/sources-skeleton'
import { toast } from 'sonner'
import { useFlagsWrapper } from '../../../hooks/use-flags-wrapper'

export interface UrlStateProps {
  error: string
}

export const sourceModalAtom = atom<boolean>(false)
export const refetchSourcesAtom = atom(1)

export const SourcesPage = () => {
  const [atlassianOpen, setAtlassianOpen] = useAtom(sourceModalAtom)
  const refetchCount = useAtomValue(refetchSourcesAtom)
  const { data, isPending, isRefetching, isError } =
    useGetAllSources(refetchCount)
  const addGoogleSourceMutation = useAddGoogleSource()
  const { googleDriveSource } = useFlagsWrapper()
  const [urlState, setUrlState] = useUrlState<UrlStateProps>()
  const toastShownRef = useRef(false)

  useEffect(() => {
    if (urlState.error && !toastShownRef.current) {
      toast.error(urlState.error)
      toastShownRef.current = true
      setUrlState((prev) => {
        const { error, ...rest } = prev
        if (error) return rest
        return prev
      })
    }
  }, [setUrlState, urlState.error])

  useEffect(() => {
    if (addGoogleSourceMutation.data) {
      window.location.href = addGoogleSourceMutation.data.url
    }
  }, [addGoogleSourceMutation.data])

  if (isError) {
    return <ErrorSomethingWentWrong />
  }

  const isLoading = isPending || isRefetching

  return (
    <div>
      <div className="flex justify-between">
        <div className="page-title">
          <h1 className="text-2xl font-semibold mb-1 capitalize">
            {t('sources')}
          </h1>
          <p className="text-gray-400">
            {t('manageSourcesForSecurityReviews')}
          </p>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              className="gap-1 capitalize"
              dataTestId="add-source-button"
              disabled={isLoading}
            >
              <PlusIcon />
              {t('add')} {t('source')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem
              className="capitalize"
              onSelect={() => setAtlassianOpen(true)}
            >
              <div className="flex items-center gap-2">
                <AtlassianIcn className="h-5 w-5" />
                {t('atlassian')}
              </div>
            </DropdownMenuItem>
            {googleDriveSource && (
              <DropdownMenuItem
                className="capitalize"
                onSelect={() => addGoogleSourceMutation.mutate()}
              >
                <div className="flex items-center gap-2">
                  <GDriveIcn className="h-5 w-5" />
                  {t('gDrive')}
                </div>
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <Dialog open={atlassianOpen} onOpenChange={setAtlassianOpen}>
          <DialogContent className="w-[800px]">
            <DialogHeader>
              <DialogTitle className="text-xl">
                {t('add')} {t('source')}
              </DialogTitle>
            </DialogHeader>
            <AddSourceForm />
          </DialogContent>
        </Dialog>
      </div>

      <div className="my-8">
        {isLoading ? (
          <SourcesSkeleton />
        ) : data?.length ? (
          <div className="grid grid-cols-1 gap-6">
            {data.map((s) => {
              return (
                <ScopeProvider atoms={[sourceModalAtom]} key={s.id}>
                  {s.source_type === 'Google' && googleDriveSource && (
                    <DriveSourceItem source={s} />
                  )}
                  {s.source_type === 'Jira' && <SourceItem source={s} />}
                </ScopeProvider>
              )
            })}
          </div>
        ) : (
          <div className="flex justify-center p-8 text-muted-foreground">
            {t('noSources')}
          </div>
        )}
      </div>
    </div>
  )
}
