import { useState } from 'react'
import { PsvFiltersDrawer } from './psv-filters-drawer'
import { ActiveFilterBadge, FileDownloader } from '@libs/ui'
import { psvColumns } from './psv-util'
import { PsvBulkEdit } from './psv-bulk-edit'
import { usePsvFilters } from '../hooks/use-psv-filters'
import type { PotentialSecurityViolation } from 'prime-front-service-client'

interface PsvToolbarProps {
  refetch: () => void
  data: PotentialSecurityViolation[]
}

export const PsvToolbar = ({ refetch, data }: PsvToolbarProps) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [isBulkEditDrawerOpen, setIsBulkEditDrawerOpen] = useState(false)
  const { getFilter, deleteFilter } = usePsvFilters()

  return (
    <div className="toolbar flex items-center justify-between mb-4 w-full">
      <div className="flex gap-3 flex-wrap">
        {psvColumns.map((column) => (
          <ActiveFilterBadge
            key={crypto.randomUUID()}
            title={column.label}
            id={column.filterId || column.key}
            isDate={column.isDate}
            getFilter={getFilter}
            deleteFilter={deleteFilter}
          />
        ))}
      </div>
      <div className="flex items-center">
        <PsvBulkEdit
          refetch={refetch}
          isOpen={isBulkEditDrawerOpen}
          onOpenChange={setIsBulkEditDrawerOpen}
        />
        <FileDownloader pathname="/psv/export" />
        <PsvFiltersDrawer
          data={data}
          isOpen={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
        />
      </div>
    </div>
  )
}
