import {
  <PERSON><PERSON>,
  <PERSON>over<PERSON><PERSON><PERSON>,
  <PERSON>overContent,
  Button,
  RadioGroup,
  RadioGroupItem,
  Textarea,
  Label,
} from '@libs/ui'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { Check, XCircle } from 'lucide-react'
import { t } from 'i18next'
import { toast } from 'sonner'
import type { PsvStatus } from 'prime-front-service-client'
import { useUpdatePsvStatus } from '../../../api/use-psv-api'

interface ViolationActionsProps {
  id: number
  status: PsvStatus
  refetch: () => void
}
export const ViolationActions = ({
  id,
  status,
  refetch,
}: ViolationActionsProps) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState<number | null>(null)
  const [popoverSuccess, setPopoverSuccess] = useState<boolean>(false)
  const [radioValue, setRadioValue] = useState<string | null>('')
  const [dismissReason, setDismissReason] = useState<string | null>('')
  const triggerRef = useRef<HTMLDivElement | null>(null)
  const { mutate: updatePsvStatusMutation } = useUpdatePsvStatus()
  const handlePopoverOpenChange = (open: boolean, id: number) => {
    if (popoverSuccess) {
      refetch()
    }
    if (open) {
      setIsPopoverOpen(id)
    } else {
      setIsPopoverOpen(null)
      setDismissReason(null)
      setRadioValue(null)
    }
  }

  const handleRadioChange = useCallback(
    (value: string) => {
      setRadioValue(value)
      if (value !== 'other') {
        setDismissReason(value)
      } else {
        setDismissReason('')
      }
    },
    [id]
  )

  const handleUpdateStatus = useCallback(
    async (psvId: number, status: PsvStatus, reason?: string) => {
      updatePsvStatusMutation(
        {
          psv_id: psvId,
          new_status: status,
          dismissed_reason: reason,
        },
        {
          onSuccess: () => {
            toast.success(t('violationUpdatedSuccessfully'))
            if (status === 'dismissed') {
              setPopoverSuccess(true)
            } else {
              refetch()
            }
          },
          onError: () => {
            toast.error(t('errors.failedToUpdateViolation'))
          },
        }
      )
    },
    [id, status]
  )

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        if (!entry.isIntersecting && isPopoverOpen === id) {
          handlePopoverOpenChange(false, 0)
        }
      },
      { root: null, threshold: 0 }
    )

    if (triggerRef.current) {
      observer.observe(triggerRef.current)
    }

    return () => {
      if (triggerRef.current) {
        observer.unobserve(triggerRef.current)
      }
    }
  }, [id, isPopoverOpen])

  return (
    <div className="flex items-center justify-end gap-2">
      <Popover
        open={isPopoverOpen === id}
        onOpenChange={(value) => {
          handlePopoverOpenChange(value, id)
        }}
      >
        <PopoverTrigger asChild>
          <div ref={triggerRef} className="flex-col align-middle text-center">
            <Button
              disabled={status === 'dismissed'}
              variant="outline"
              className="px-2 w-10"
              dataTestId="dismiss-button"
            >
              <XCircle size={20} />
            </Button>
            <div className="font-light text-gray-500 text-xs mt-1 capitalize">
              {t('dismiss')}
            </div>
          </div>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto"
          side="bottom"
          align="end"
          sideOffset={10}
          asChild
          onClick={(e) => e.stopPropagation()}
          onFocus={(e) => e.stopPropagation()}
          onBlur={(e) => e.stopPropagation()}
        >
          {popoverSuccess ? (
            <div className="p-4">
              <div className="mb-1">{t('thankYou')}!</div>
              <div>{t('psvSuccessMessage')}</div>
              <Button
                className="mt-4"
                dataTestId="got-it-button"
                onClick={() => {
                  handlePopoverOpenChange(false, 0)
                  setPopoverSuccess(false)
                  refetch()
                }}
              >
                {t('gotIt')}
              </Button>
            </div>
          ) : (
            <div>
              <div className="font-semibold mb-2">{t('whyWasItDismissed')}</div>
              <RadioGroup
                value={radioValue || ''}
                onValueChange={handleRadioChange}
              >
                <div className="flex items-center space-x-2 my-3">
                  <RadioGroupItem value={t('knownIssue')} id="knownIssue" />
                  <Label htmlFor={t('knownIssue')}>{t('knownIssue')}</Label>
                </div>
                <div className="flex items-center space-x-2 mb-3">
                  <RadioGroupItem
                    value={t('noSecurityImplications')}
                    id={t('noSecurityImplications')}
                  />
                  <Label htmlFor={t('noSecurityImplications')}>
                    {t('noSecurityImplications')}
                  </Label>
                </div>
                <div className="flex items-center space-x-2 mb-3">
                  <RadioGroupItem
                    value={t('Opened by Security - Ignore')}
                    id={t('Opened by Security - Ignore')}
                  />
                  <Label htmlFor={t('Opened by Security - Ignore')}>
                    {t('Opened by Security - Ignore')}
                  </Label>
                </div>
                <div className="flex items-start space-x-2 mb-3">
                  <RadioGroupItem value="other" id="other" />
                  <Label htmlFor="other">
                    Other
                    {radioValue === 'other' && (
                      <Textarea
                        className="my-4 p-2 w-full"
                        id="other-dismiss-reason"
                        placeholder={t('type')}
                        onChange={(e) => setDismissReason(e.target.value)}
                        disabled={radioValue !== 'other'}
                      />
                    )}
                  </Label>
                </div>
              </RadioGroup>
              <Button
                className="mt-2"
                dataTestId="submit-dismiss-button"
                onClick={async () => {
                  await handleUpdateStatus(
                    id,
                    'dismissed',
                    dismissReason || undefined
                  )
                }}
                disabled={!dismissReason}
              >
                {t('submit')}
              </Button>
            </div>
          )}
        </PopoverContent>
      </Popover>

      <div className="flex-col align-middle text-center pr-4">
        <Button
          onClick={() => handleUpdateStatus(id, 'done')}
          disabled={status === 'done'}
          variant="outline"
          className="px-2 w-10"
          dataTestId="done-button"
        >
          <Check className="font-light" size={20} />
        </Button>
        <div className="font-light text-gray-500 text-xs mt-1 capitalize">
          {t('done')}
        </div>
      </div>
    </div>
  )
}
