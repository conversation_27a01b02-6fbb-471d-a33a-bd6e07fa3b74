import React, { useCallback, useState } from 'react'
import {
  <PERSON><PERSON>,
  Drawer,
  DrawerClose,
  Drawer<PERSON>ontent,
  DrawerDes<PERSON>,
  DrawerHeader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerFooter,
  DrawerTrigger,
  RadioGroup,
  RadioGroupItem,
  Label,
  Textarea,
  StateBadge,
} from '@libs/ui'
import { Pencil, XIcon } from 'lucide-react'
import { t } from 'i18next'
import { useAtomValue } from 'jotai/index'
import { selectedPsvAtom } from './psv-table'
import { toast } from 'sonner'
import type {
  PsvStatus,
  SinglePsvUpdateRequest,
} from 'prime-front-service-client'
import { useBulkUpdatePsvStatus } from '../../../api/use-psv-api'
interface PSVBulkEditProps {
  refetch: () => void
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}

export const PsvBulkEdit = ({
  isOpen,
  onOpenChange,
  refetch,
}: PSVBulkEditProps) => {
  const selectedPsv = useAtomValue(selectedPsvAtom)

  const [status, setStatus] = useState<PsvStatus | null>(null)
  const [dismissRadioValue, setDismissRadioValue] = useState('')
  const [dismissReason, setDismissReason] = useState<string | null>(null)
  const { mutate: bulkUpdatePsvStatusMutation } = useBulkUpdatePsvStatus()
  const clear = () => {
    setStatus(null)
  }

  const handleDismissRadioChange = useCallback(
    (value: string) => {
      setDismissRadioValue(value)
      if (value !== 'other') {
        setDismissReason(value)
      } else {
        setDismissReason('')
      }
    },
    [status, selectedPsv]
  )

  const handleStatusChange = useCallback(
    (value: PsvStatus) => {
      setStatus(value as PsvStatus)
      setDismissRadioValue('')
      setDismissReason('')
    },
    [status, selectedPsv]
  )

  const apply = async () => {
    if (status) {
      await updateBulk()
    } else {
      onOpenChange(false)
    }
  }

  const updateBulk = async () => {
    const violations = selectedPsv.reduce((acc, violation) => {
      acc[violation.psv_id] = {
        new_status: status,
        dismissed_reason: dismissReason || null,
      } as SinglePsvUpdateRequest
      return acc
    }, {} as { [key: string]: SinglePsvUpdateRequest })

    bulkUpdatePsvStatusMutation(
      { violations },
      {
        onSuccess: () => {
          toast.success(t('violationUpdatedSuccessfully'))
          refetch()
          setDismissReason(null)
          setDismissRadioValue('')
          clear()
          onOpenChange(false)
        },
        onError: () => {
          toast.error(t('errors.failedToUpdateViolation'))
        },
      }
    )
  }
  return (
    <Drawer direction="right" open={isOpen} onOpenChange={onOpenChange}>
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          className="capitalize h-8"
          dataTestId="bulk-edit-button"
          disabled={!selectedPsv.length}
        >
          <Pencil className="pr-2" />
          {t('bulkEdit')}
        </Button>
      </DrawerTrigger>
      <DrawerContent className="fixed top-0 right-0 h-full w-[400px] bg-white shadow-lg overflow-y-auto overflow-x-hidden">
        <DrawerHeader className="p-8">
          <DrawerTitle>
            <div className="w-full flex items-center justify-between mx-0 text-slate-800 text-xl capitalize">
              {t('bulkEdit')}
              <DrawerClose>
                <XIcon />
              </DrawerClose>
            </div>
          </DrawerTitle>
          <DrawerDescription></DrawerDescription>
        </DrawerHeader>
        <div className="flex-col px-8 py-4 h-full">
          <div className="capitalize font-medium mb-2">{t('status')}</div>
          <RadioGroup
            value={status || ''}
            onValueChange={(value) => handleStatusChange(value as PsvStatus)}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="open" id="r1" />
              <Label htmlFor="r1">
                <StateBadge state="open"></StateBadge>
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="done" id="r2" />
              <Label htmlFor="r2">
                <StateBadge state="done"></StateBadge>
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="dismissed" id="r3" />
              <Label htmlFor="r3">
                <StateBadge state="dismissed"></StateBadge>
              </Label>
            </div>
            {status === 'dismissed' && (
              <div className="mt-2 ml-4">
                <div className="font-medium">{t('whyWasItDismissed')}</div>
                <RadioGroup
                  value={dismissRadioValue}
                  onValueChange={handleDismissRadioChange}
                >
                  <div className="flex items-center space-x-2 my-3">
                    <RadioGroupItem value="Not an Issue" id="no-issue" />
                    <Label htmlFor="no-issue">{t('notAnIssue')}</Label>
                  </div>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem
                      value="Missing Context"
                      id="missing-context"
                    />
                    <Label htmlFor="missing-context">
                      {t('missingContext')}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="Already Aware" id="already-aware" />
                    <Label htmlFor="already-aware">{t('alreadyAware')}</Label>
                  </div>
                  <div className="flex items-start space-x-2 mb-3">
                    <RadioGroupItem value="other" id="other" />
                    <Label htmlFor="other">
                      Other
                      {dismissRadioValue === 'other' && (
                        <Textarea
                          className="my-4 p-2 w-full"
                          id="other-dismiss-reason"
                          placeholder={t('type')}
                          onChange={(e) => setDismissReason(e.target.value)}
                          disabled={dismissRadioValue !== 'other'}
                        />
                      )}
                    </Label>
                  </div>
                </RadioGroup>
              </div>
            )}
          </RadioGroup>
        </div>
        <DrawerFooter className="flex justify-end items-end w-full px-8 py-4 m-0 sticky bottom-0 bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1),0_-2px_4px_-1px_rgba(0,0,0,0.06)]">
          <div className="flex justify-end items-end w-full gap-2">
            <Button
              variant="outline"
              onClick={() => clear()}
              className="capitalize"
              dataTestId="clear-bulk-edit"
            >
              {t('clear')}
            </Button>

            <div className="flex items-center">
              <Button
                onClick={() => apply()}
                disabled={status === 'dismissed' && !dismissReason}
                dataTestId="apply-bulk-edit"
                className="capitalize"
              >
                {t('apply')}
              </Button>
            </div>
          </div>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}
