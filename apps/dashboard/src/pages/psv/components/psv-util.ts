import type { PotentialSecurityViolation } from 'prime-front-service-client/src/models'

interface PsvColumn {
  key: keyof PotentialSecurityViolation
  label: string
  isDate: boolean
  isJira: boolean
  filterId?: string
}

export const psvColumns: PsvColumn[] = [
  {
    key: 'title',
    label: 'tableColumns.title',
    isDate: false,
    isJira: true,
  },
  {
    key: 'issue_id',
    label: 'tableColumns.issueId',
    isDate: false,
    isJira: true,
  },
  {
    key: 'project',
    filterId: 'provider_fields.project',
    label: 'tableColumns.project',
    isDate: false,
    isJira: true,
  },
  {
    key: 'reporter',
    filterId: 'provider_fields.reporter',
    label: 'tableColumns.reporter',
    isDate: false,
    isJira: true,
  },
  {
    key: 'created_at',
    label: 'tableColumns.created',
    isDate: true,
    isJira: true,
  },
  {
    key: 'detection_date',
    label: 'tableColumns.detected',
    isDate: true,
    isJira: false,
  },
  {
    key: 'status',
    label: 'tableColumns.status',
    isDate: false,
    isJira: false,
  },
  {
    key: 'type',
    label: 'tableColumns.type',
    isDate: false,
    isJira: false,
  },
]
