/*eslint-disable max-lines*/
import {
  Button,
  Checkbox,
  JiraIcn,
  Pagination,
  StateBadge,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableSkeleton,
} from '@libs/ui'
import {
  ArrowDown,
  ArrowDownUpIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from 'lucide-react'
import { t } from 'i18next'
import { format } from 'date-fns'
import { Link } from 'react-router'
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react'
import { cn } from '@libs/common'
import { usePsvFilters } from '../hooks/use-psv-filters'
import { psvColumns } from './psv-util'
import { atom, useAtom } from 'jotai/index'
import { PsvToolbar } from './psv-toolbar'
import { usePsvSort } from '../hooks/use-psv-sort'
import { ViolationActions } from './violation-actions'
import { useGetPsv } from '../../../api/use-psv-api'
import type { PotentialSecurityViolation } from 'prime-front-service-client/src/models'
import { snakeToText } from '../utils'

const limit = 20
export const selectedPsvAtom = atom<PotentialSecurityViolation[]>([])

export const PSVTable = () => {
  const [selectedPsv, setSelectedPsv] = useAtom(selectedPsvAtom)
  const [currentPage, setCurrentPage] = useState(0)

  const { stringifiedFilters } = usePsvFilters()

  const { sortConfig, toggleSort } = usePsvSort<PotentialSecurityViolation>()

  const { data, isPending, isRefetching, refetch, isSuccess, isError } =
    useGetPsv({
      limit,
      offset: currentPage * limit,
      f: stringifiedFilters.length ? stringifiedFilters : undefined,
      s: sortConfig ? [JSON.stringify(sortConfig)] : undefined,
    })

  const totalPages = data?.total ? Math.ceil(data?.total / limit) : 0

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage)
  }

  useEffect(() => {
    setCurrentPage(0)
    refetch()
  }, [stringifiedFilters])

  useEffect(() => {
    refetch()
  }, [sortConfig])

  const [expandedRows, setExpandedRows] = useState<Record<number, boolean>>({})

  const toggleRowExpansion = useCallback(
    (rowId: number) => {
      setExpandedRows((prev) => ({
        ...prev,
        [rowId]: !prev[rowId],
      }))
    },
    [data]
  )

  const toggleSelected = (
    isSelected: boolean,
    psv: PotentialSecurityViolation
  ) => {
    setSelectedPsv((prevSelected) => {
      if (isSelected) {
        return [...prevSelected, psv]
      }
      return prevSelected.filter((v) => v.psv_id !== psv.psv_id)
    })
  }

  const toggleAllRowsSelected = (isSelected: boolean) => {
    if (isSelected) {
      setSelectedPsv((prevSelected) => {
        const newSelected = [...prevSelected]
        data?.results?.forEach((incident) => {
          if (
            !newSelected.some((selected) => selected.psv_id === incident.psv_id)
          ) {
            newSelected.push(incident)
          }
        })
        return newSelected
      })
    } else {
      setSelectedPsv((prevSelected) => {
        const currentIds = data?.results?.map((i) => i.psv_id)
        return prevSelected.filter(
          (incident) => !currentIds?.includes(incident.psv_id)
        )
      })
    }
  }

  const isAllRowsSelected = useMemo(() => {
    return (
      !!data?.results?.length &&
      data?.results?.every((i) =>
        selectedPsv.some((selected) => selected.psv_id === i.psv_id)
      )
    )
  }, [data?.results, selectedPsv])

  const isSomePageRowsSelected = useMemo(() => {
    const tableIds = new Set(data?.results?.map((i) => i.psv_id))
    return selectedPsv.some((i) => tableIds.has(i.psv_id)) && !isAllRowsSelected
  }, [data?.results, selectedPsv])

  useEffect(() => {
    setSelectedPsv([])
  }, [data])

  if (isError) {
    return (
      <div className="min-h-80 flex justify-center items-center bg-white border p-2 text-muted-foreground">
        {t('somethingWentWrong')}
      </div>
    )
  }

  return (
    <>
      <PsvToolbar refetch={refetch} data={data?.results || []} />
      <div className="table-wrapper relative h-[calc(100vh_-_16rem)] overflow-auto border border-red-300 rounded-md  shadow-sm bg-white">
        {isPending || isRefetching ? (
          <TableSkeleton />
        ) : (
          <Table className="w-full ">
            <TableHeader className="border-0 text-sm bg-white sticky top-0 z-10 shadow-[0_1px_0_rgba(0,0,0,1)]">
              <TableRow className="border-b-0">
                <TableHead className="py-2 px-4">
                  <Checkbox
                    checked={isAllRowsSelected || isSomePageRowsSelected}
                    onCheckedChange={(value) => toggleAllRowsSelected(!!value)}
                  />
                </TableHead>
                <TableHead className="p-2"></TableHead>
                {psvColumns.map((column, index) => (
                  <TableHead
                    className={cn(
                      'px-2 py-4 capitalize min-w-32',
                      column.key === 'title' && 'min-w-64'
                    )}
                    key={`${column.key}-${index}`}
                  >
                    <div className="flex items-center font-bold">
                      {column.isJira && (
                        <JiraIcn className="mr-2 inline text-jira" />
                      )}
                      {t(column.label)}
                      <Button
                        variant="ghost"
                        className="p-0 h-6 w-6 text-slate-600"
                        size="icon"
                        dataTestId="sort-button"
                        onClick={() =>
                          toggleSort(column?.filterId || column.key)
                        }
                      >
                        <div className="flex p-0 font-light">
                          {(column.filterId &&
                            sortConfig?.field === column.filterId) ||
                          sortConfig?.field === column.key ? (
                            <ArrowDown
                              className={cn(
                                'p-0 h-5 w-4',
                                sortConfig?.field === column?.filterId ||
                                  sortConfig?.field === column.key
                                  ? 'text-teal-600'
                                  : 'text-primary',
                                (sortConfig?.field === column?.filterId ||
                                  sortConfig?.field === column.key) &&
                                  sortConfig?.direction === 'asc'
                                  ? 'rotate-180'
                                  : ''
                              )}
                            />
                          ) : (
                            <ArrowDownUpIcon className="p-0 h-5 w-4 text-gray-500" />
                          )}
                        </div>
                      </Button>
                    </div>
                  </TableHead>
                ))}
                <TableHead className="p-2"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody
              className={cn(
                '[&_tr:last-child]:border-1 rounded-lg',
                data?.total ? 'border-b' : 'border-0'
              )}
            >
              {isSuccess && data && data.results?.length ? (
                data.results.map((violation: PotentialSecurityViolation) => (
                  <Fragment key={violation.psv_id || crypto.randomUUID()}>
                    <TableRow
                      key={violation.psv_id}
                      className={cn(
                        expandedRows[violation.psv_id]
                          ? 'border-b-0'
                          : 'border-b'
                      )}
                    >
                      <TableCell className="py-2 px-4">
                        <Checkbox
                          checked={selectedPsv.includes(violation)}
                          onCheckedChange={(value) =>
                            toggleSelected(!!value, violation)
                          }
                          aria-label="Select row"
                        />
                      </TableCell>
                      <TableCell>
                        <div
                          className="cursor-pointer p-2 w-8"
                          onClick={() => toggleRowExpansion(violation.psv_id)}
                        >
                          {expandedRows[violation.psv_id] ? (
                            <ChevronDownIcon className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronRightIcon className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </TableCell>
                      {psvColumns.map((column, index) => {
                        return (
                          <TableCell
                            key={`${column.key}-${index}`}
                            className="p-2"
                          >
                            <div
                              className={cn(
                                'flex items-center gap-1',
                                column.key === 'issue_id' &&
                                  'underline cursor-pointer'
                              )}
                            >
                              {column.isDate ? (
                                violation[column.key] instanceof Date ? (
                                  format(
                                    violation[column.key] as Date,
                                    'dd/MM/yy'
                                  )
                                ) : (
                                  ''
                                )
                              ) : column.key === 'status' ? (
                                <StateBadge state={violation.status} />
                              ) : column.key === 'issue_id' ? (
                                <Link
                                  to={violation.issue_link || ''}
                                  target="_blank"
                                >
                                  {violation[column.key]?.toString() || ''}
                                </Link>
                              ) : column.key === 'type' ? (
                                <span className="capitalize">
                                  {snakeToText(violation.type) || ''}
                                </span>
                              ) : (
                                violation[column.key]?.toString() || ''
                              )}
                            </div>
                          </TableCell>
                        )
                      })}
                      <TableCell className="p-2">
                        <ViolationActions
                          id={violation.psv_id}
                          status={violation.status}
                          refetch={refetch}
                        />
                      </TableCell>
                    </TableRow>
                    {expandedRows[violation.psv_id] && (
                      <TableRow
                        className="mb-8 group-hover:bg-red-50"
                        key={violation.psv_id + '-expanded'}
                      >
                        <TableCell
                          colSpan={2}
                          className="p-2 bg-white border-b  font-light text-slate-500"
                        ></TableCell>
                        <TableCell
                          colSpan={8}
                          className="px-2 bg-white flex font-light text-slate-500 pb-6 gap-1"
                        >
                          <div className="flex flex-col gap-1 pl-3">
                            <div className="font-semibold text-slate-700">
                              {t(`incidentDescription.${violation.type}`)}:
                            </div>
                            <code className="relative rounded bg-muted px-4 py-2 font-mono text-xs">
                              {violation.description}
                            </code>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </Fragment>
                ))
              ) : (
                <div className="flex center h-full p-10">{t('noPsv')}</div>
              )}
            </TableBody>
          </Table>
        )}
      </div>

      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          limit={limit}
          total={data?.total || 0}
          totalPages={totalPages}
          handlePageChange={handlePageChange}
        />
      )}
    </>
  )
}
