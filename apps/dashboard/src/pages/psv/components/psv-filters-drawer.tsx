import React, { useEffect, useMemo } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Drawer<PERSON>lose,
  Drawer<PERSON>ontent,
  DrawerD<PERSON><PERSON>,
  Drawer<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ooter,
  Drawer<PERSON>rigger,
  FilterSearchInput,
  FilterDateRangePicker,
} from '@libs/ui'
import { XIcon } from 'lucide-react'
import { t } from 'i18next'
import { usePsvFilters } from '../hooks/use-psv-filters'
import type { PotentialSecurityViolation } from 'prime-front-service-client'
import { useGetWorkroomFields } from '../../../api/use-config-api'
import { FilterCheckboxCollapse } from '../../workroom/components'

interface PSIFiltersProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  data: PotentialSecurityViolation[]
}

export const PsvFiltersDrawer = ({ isOpen, onOpenChange }: PSIFiltersProps) => {
  const {
    filters,
    getPendingFilter,
    updatePendingFilters,
    applyPendingFilters,
    clearPendingFilters,
    setPendingFilters,
  } = usePsvFilters()

  const handleApply = async () => {
    applyPendingFilters()
    onOpenChange(false)
  }

  useEffect(() => {
    if (isOpen) {
      setPendingFilters(filters)
    } else {
      clearPendingFilters()
    }
  }, [clearPendingFilters, filters, isOpen, setPendingFilters])

  const { data: workroomFields } = useGetWorkroomFields()

  const projects = useMemo(
    () =>
      workroomFields?.find((field) => field?.id === 'provider_fields.project')
        ?.options || [],
    [workroomFields]
  )
  const reporters = useMemo(
    () =>
      workroomFields?.find((field) => field?.id === 'provider_fields.reporter')
        ?.options || [],
    [workroomFields]
  )

  return (
    <Drawer direction="right" open={isOpen} onOpenChange={onOpenChange}>
      <DrawerTrigger asChild>
        <Button className="capitalize h-8" dataTestId="filters-button">
          {t('filters')}
        </Button>
      </DrawerTrigger>
      <DrawerContent className="fixed top-0 right-0 h-full w-[400px] bg-white shadow-lg overflow-y-auto overflow-x-hidden">
        <DrawerHeader className="p-8">
          <DrawerTitle>
            <div className="w-full flex items-center justify-between mx-0 text-slate-800 text-xl capitalize">
              {t('filters')}
              <DrawerClose>
                <XIcon />
              </DrawerClose>
            </div>
          </DrawerTitle>
          <DrawerDescription></DrawerDescription>
        </DrawerHeader>
        <div className="flex-col p-8 h-full">
          <FilterSearchInput
            title="title"
            filterKey="title"
            showOperator={false}
            savedFilter={getPendingFilter('title')}
            onFilterChange={updatePendingFilters}
          />
          <FilterCheckboxCollapse
            id={'status'}
            title="status"
            savedFilter={getPendingFilter('status')}
            options={['open', 'done', 'dismissed']}
            onFilterChange={updatePendingFilters}
          />
          <FilterSearchInput
            title="issue id"
            filterKey="issue_id"
            showOperator={false}
            savedFilter={getPendingFilter('issue_id')}
            onFilterChange={updatePendingFilters}
          />
          <FilterCheckboxCollapse
            id="provider_fields.project"
            title="project"
            savedFilter={getPendingFilter('provider_fields.project')}
            options={projects}
            onFilterChange={updatePendingFilters}
          />
          <FilterCheckboxCollapse
            id="provider_fields.reporter"
            title="reporter"
            savedFilter={getPendingFilter('provider_fields.reporter')}
            options={reporters}
            onFilterChange={updatePendingFilters}
          />
          <FilterCheckboxCollapse
            id="type"
            title="type"
            savedFilter={getPendingFilter('type')}
            options={[
              'aws_key',
              'github_token',
              'private_key',
              'password',
              'token',
            ]}
            onFilterChange={updatePendingFilters}
          />
          <FilterDateRangePicker
            filterKey="created_at"
            title="created"
            savedFilter={getPendingFilter('created_at')}
            onFilterChange={updatePendingFilters}
          />
          <FilterDateRangePicker
            filterKey="detection_date"
            title="detection"
            savedFilter={getPendingFilter('detection_date')}
            onFilterChange={updatePendingFilters}
          />
        </div>
        <DrawerFooter className="flex justify-end items-end w-full px-8 py-4 m-0 sticky bottom-0 bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1),0_-2px_4px_-1px_rgba(0,0,0,0.06)]">
          <div className="flex justify-end items-end w-full gap-2">
            <Button
              variant="outline"
              onClick={() => clearPendingFilters()}
              className="capitalize"
              dataTestId="clear-filters"
            >
              {t('clear')}
            </Button>

            <div className="flex items-center  overflow-hidden">
              <Button
                onClick={() => handleApply()}
                dataTestId="apply-filters"
                className="capitalize"
              >
                {t('apply')}
              </Button>
            </div>
          </div>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}
