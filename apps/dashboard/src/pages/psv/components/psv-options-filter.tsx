import type { FilterItem } from '@libs/ui'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
  Badge,
} from '@libs/ui'
import { CheckIcon, ChevronDown, ChevronUp } from 'lucide-react'
import { cn } from '@libs/common'
import { t } from 'i18next'
import React, { useEffect, useState } from 'react'
import { snakeToText } from '../utils'

interface DataTableFacetedFilterProps {
  id: string
  title: string
  options: string[]
  isBadge?: boolean
  savedFilter?: FilterItem
  onFilterChange: (filter: FilterItem) => void
}

export function PsvOptionsFilter({
  id,
  title,
  options,
  isBadge,
  savedFilter,
  onFilterChange,
}: DataTableFacetedFilterProps) {
  const [initialOptions] = useState<string[]>(options)
  const [isOpen, setIsOpen] = useState(false)

  const [selectedOptions, setSelectedOptions] = useState(
    savedFilter?.value || []
  )
  const [filterOperator, setFilterOperator] = useState(savedFilter?.op || 'eq')

  useEffect(() => {
    if (!savedFilter) {
      setSelectedOptions([])
      setFilterOperator('eq')
    } else {
      setSelectedOptions(savedFilter.value || [])
      setFilterOperator(savedFilter.op || 'eq')
    }
  }, [savedFilter])

  const onSelect = (option: string) => {
    setSelectedOptions((prev: any) => {
      const isSelected = prev.includes(option)
      const newSelectedOptions = isSelected
        ? prev.filter((selected: string) => selected !== option)
        : [...prev, option]

      onFilterChange({
        field: id,
        op: filterOperator,
        value: newSelectedOptions,
      })

      return newSelectedOptions
    })
  }

  return (
    <Collapsible
      className={cn(
        'mb-4 w-full',
        isOpen ? 'border-b border-gray-200 mb-2 pb-2' : ''
      )}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <CollapsibleTrigger asChild>
        <div
          className={cn(
            'flex justify-between items-center gap-3 cursor-pointer capitalize mb-3 font-medium pb-2',
            isOpen ? '' : 'border-b border-gray-200'
          )}
        >
          <div className="flex gap-2">
            <span>{title}</span>
            {savedFilter && <span>({savedFilter.value.length})</span>}
          </div>
          <div className="flex items-center gap-1">
            {isOpen ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
          </div>
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent className="w-[300px] p-0">
        <Command>
          <CommandList>
            <CommandEmpty>{t('noResultsFound')}</CommandEmpty>
            <CommandGroup className="filter-command-group">
              {initialOptions.map((option) => {
                const isSelected = selectedOptions.includes(option)
                return (
                  <CommandItem
                    key={crypto.randomUUID()}
                    onSelect={() => onSelect(option)}
                    className="capitalize"
                  >
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        isSelected
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible'
                      )}
                    >
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>

                    {isBadge ? (
                      <Badge className="capitalize h-6 shadow-none hover:bg-black">
                        {option}
                      </Badge>
                    ) : (
                      <div>{id === 'type' ? snakeToText(option) : option}</div>
                    )}
                  </CommandItem>
                )
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </CollapsibleContent>
    </Collapsible>
  )
}
