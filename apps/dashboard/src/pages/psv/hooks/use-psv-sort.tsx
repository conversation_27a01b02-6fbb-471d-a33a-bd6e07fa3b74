import { useCallback, useEffect, useMemo } from 'react'
import { useUrlStateDefaultOptions } from './use-psv-filters'
import type { UrlStateProps } from './use-psv-filters'
import { useUrlState } from '@libs/ui'

export const usePsvSort = <T extends Record<string, any>>() => {
  const [urlState, setUrlState] = useUrlState<UrlStateProps>(
    { s: [] },
    useUrlStateDefaultOptions
  )

  const sortConfig = useMemo(
    () => (urlState.s?.length ? urlState.s[0] : null),
    [urlState.s]
  )

  useEffect(() => {
    setUrlState((prev) => ({
      ...prev,
      s: sortConfig ? [sortConfig] : undefined,
    }))
  }, [sortConfig, setUrlState])

  const toggleSort = useCallback(
    (field: string) => {
      setUrlState((prev) => {
        const existingSort = prev.s?.find(
          (s: { field: string; direction: 'asc' | 'desc' }) => s.field === field
        )
        if (existingSort) {
          return {
            ...prev,
            s:
              existingSort.direction === 'asc'
                ? [{ field, direction: 'desc' }]
                : [{ field, direction: 'asc' }],
          }
        }
        return { ...prev, s: [{ field, direction: 'asc' }] }
      })
    },
    [setUrlState]
  )

  const sortData = (data: T[]) => {
    if (!sortConfig) return data

    const { field, direction } = sortConfig

    return [...data].sort((a, b) => {
      if (a[field] < b[field]) return direction === 'asc' ? -1 : 1
      if (a[field] > b[field]) return direction === 'asc' ? 1 : -1
      return 0
    })
  }

  return {
    sortConfig,
    sortData,
    toggleSort,
  }
}
