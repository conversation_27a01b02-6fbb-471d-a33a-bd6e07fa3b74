import { useCallback, useEffect, useMemo } from 'react'
import { atom, useAtom } from 'jotai'

import type { FilterItem, UseUrlStateOptions } from '@libs/ui'
import type { SortItem } from '../../workroom/hooks'
import { useUrlState } from '@libs/ui'

const filtersAtom = atom<FilterItem[]>([
  { field: 'status', value: ['open'], op: 'eq' },
])

export interface UrlStateProps {
  f?: FilterItem[]
  s?: SortItem[]
}

export const useUrlStateDefaultOptions = {
  parseOptions: {
    arrayFormat: 'bracket-separator',
  },
  stringifyOptions: {
    arrayFormat: 'bracket-separator',
  },
} as UseUrlStateOptions

const pendingFiltersAtom = atom<FilterItem[]>([])

export const usePsvFilters = () => {
  const [filters, setFilters] = useAtom(filtersAtom)
  const [pendingFilters, setPendingFilters] = useAtom(pendingFiltersAtom)

  const [urlState, setUrlState] = useUrlState<UrlStateProps>(
    { f: [] },
    useUrlStateDefaultOptions
  )

  useEffect(() => {
    if (urlState.f) {
      setFilters(urlState.f)
    }
  }, [urlState.f, setFilters])

  useEffect(() => {
    setUrlState((prev) => ({
      ...prev,
      f: filters.length ? filters : undefined,
    }))
  }, [filters, setUrlState])

  const stringifiedFilters = useMemo(() => {
    return filters?.length
      ? filters?.map((filter: FilterItem) => JSON.stringify(filter))
      : []
  }, [filters])

  const updateFilter = useCallback(
    (newFilter: FilterItem) => {
      setFilters((prevFilters) => {
        const existingIndex = prevFilters.findIndex(
          (f) => f.field === newFilter.field
        )
        if (existingIndex === -1) {
          return [...prevFilters, newFilter]
        }
        const updatedFilters = [...prevFilters]
        updatedFilters[existingIndex] = newFilter
        return updatedFilters
      })
    },
    [setFilters]
  )

  const deleteFilter = useCallback(
    (field: string) => {
      setFilters((prevFilters) => prevFilters.filter((f) => f.field !== field))
    },
    [setFilters]
  )

  const clearFilters = useCallback(() => {
    setFilters([])
  }, [setFilters])

  const getFilter = useCallback(
    (field: string): FilterItem | undefined => {
      return filters.find((f) => f.field === field)
    },
    [filters]
  )

  const updateFilters = useCallback(
    (newFilters: FilterItem[]) => {
      setFilters(newFilters)
    },
    [setFilters]
  )

  const updatePendingFilters = useCallback(
    (newFilter: FilterItem) => {
      setPendingFilters((prev) => {
        if (newFilter.value.length === 0) {
          return prev.filter((f) => f.field !== newFilter.field)
        }
        const updatedFilters = prev.filter((f) => f.field !== newFilter.field)

        return [...updatedFilters, newFilter]
      })
    },
    [setPendingFilters]
  )

  const clearPendingFilters = useCallback(() => {
    setPendingFilters([])
  }, [setPendingFilters])

  const applyPendingFilters = useCallback(() => {
    if (!pendingFilters.length) {
      setFilters([])
    } else {
      setFilters((prevFilters) => {
        const mergedFilters = prevFilters.filter(
          (f) => !pendingFilters.some((pf) => pf.field === f.field)
        )
        return [...mergedFilters, ...pendingFilters]
      })
    }
    clearPendingFilters()
  }, [setFilters, pendingFilters, clearPendingFilters])

  const getPendingFilter = useCallback(
    (field: string): FilterItem | undefined => {
      return pendingFilters.find((f) => f.field === field)
    },
    [pendingFilters]
  )

  return {
    filters,
    updateFilter,
    deleteFilter,
    clearFilters,
    stringifiedFilters,
    getFilter,
    updateFilters,
    pendingFilters,
    updatePendingFilters,
    clearPendingFilters,
    applyPendingFilters,
    getPendingFilter,
    setPendingFilters,
  }
}
