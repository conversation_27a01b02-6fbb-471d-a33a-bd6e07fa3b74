/* eslint-disable max-lines */
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Alert,
  AlertDescription,
  AlertTitle,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Checkbox,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Command,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
  Progress,
  ScrollArea,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Textarea,
  Input,
  Label,
} from '@libs/ui'
import {
  AlertCircle,
  ChevronRight,
  Download,
  FileText,
  Moon,
  Plus,
  Search,
  Settings,
  Trash,
  User,
} from 'lucide-react'
import { useState } from 'react'
import type { GooglePickerFile } from '../../hooks/use-google-picker'
import { GooglePicker } from '../../components'

export const DesignKitPage = () => {
  const [selectedFiles, setSelectedFiles] = useState<GooglePickerFile[]>([])

  const handleFilesSelected = (files: GooglePickerFile[]) => {
    setSelectedFiles(files)
  }

  return (
    <ScrollArea className="h-full">
      <div className="container py-10 space-y-12">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Design System</h1>
          <Badge variant="outline">v1.0</Badge>
        </div>

        <Separator />

        {/* Google Picker Integration */}
        <section className="rounded-lg border bg-background p-6">
          <h2 className="relative -top-[38px] bg-background px-2.5 py-1.5 inline border rounded-md text-2xl font-semibold">
            Google Drive Integration
          </h2>
          <div className="space-y-8">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Google Picker</h3>
              <div className="flex flex-col gap-6">
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    Click the button below to open the Google Picker and select
                    files from Google Drive.
                  </p>
                  <GooglePicker
                    onSelect={handleFilesSelected}
                    buttonText="Select Files from Google Drive"
                    dataTestId="google-picker-demo-button"
                    mimeTypes={[
                      'application/pdf',
                      'image/jpeg',
                      'image/png',
                      'image/gif',
                      'application/vnd.google-apps.document',
                    ]}
                  />
                </div>

                {selectedFiles.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="font-medium">Selected Files:</h4>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {selectedFiles.map((file) => (
                        <Card key={file.id}>
                          <CardHeader className="pb-2">
                            <div className="flex items-center gap-2">
                              {file.iconUrl ? (
                                <img
                                  src={file.iconUrl}
                                  alt="File icon"
                                  className="w-5 h-5"
                                />
                              ) : (
                                <FileText className="w-5 h-5 text-muted-foreground" />
                              )}
                              <CardTitle className="text-base truncate">
                                {file.name}
                              </CardTitle>
                            </div>
                            <CardDescription className="truncate">
                              {file.mimeType}
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="pb-2 pt-0">
                            {file.description && (
                              <p className="text-sm text-muted-foreground truncate">
                                {file.description}
                              </p>
                            )}
                          </CardContent>
                          <CardFooter>
                            <div className="flex justify-between w-full">
                              {file.url && (
                                <Button
                                  dataTestId={`view-file-${file.id}`}
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    window.open(file.url, '_blank')
                                  }
                                >
                                  View
                                </Button>
                              )}
                              {file.downloadUrl && (
                                <Button
                                  dataTestId={`download-file-${file.id}`}
                                  variant="secondary"
                                  size="sm"
                                  onClick={() =>
                                    window.open(file.downloadUrl, '_blank')
                                  }
                                >
                                  <Download className="w-4 h-4 mr-1" />
                                  Download
                                </Button>
                              )}
                            </div>
                          </CardFooter>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Typography */}
        <section className="rounded-lg border bg-background p-6">
          <h2 className="relative -top-[38px] bg-background px-2.5 py-1.5 inline border rounded-md text-2xl font-semibold">
            Typography
          </h2>
          <div className="space-y-8">
            {/* Headings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Headings</h3>
              <div className="space-y-4">
                <h1 className="scroll-m-20 text-4xl font-bold tracking-tight">
                  The Joke Tax Chronicles
                </h1>
                <h2 className="scroll-m-20 text-3xl font-semibold tracking-tight">
                  The King's Plan
                </h2>
                <h3 className="scroll-m-20 text-2xl font-semibold tracking-tight">
                  The Joke Tax
                </h3>
                <h4 className="scroll-m-20 text-xl font-semibold tracking-tight">
                  People's Rebellion
                </h4>
              </div>
            </div>

            {/* Paragraph */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Paragraph</h3>
              <div className="space-y-4">
                <p className="leading-7">
                  Once upon a time, in a far-off land, there was a very lazy
                  king who spent all day lounging on his throne. One day, his
                  advisors came to him with a problem: the kingdom was running
                  out of money.
                </p>
                <p className="text-sm text-muted-foreground">
                  This is a smaller paragraph with muted text color, perfect for
                  secondary information.
                </p>
              </div>
            </div>

            {/* Inline Text */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Inline Text Styles</h3>
              <div className="space-y-2">
                <p>
                  <strong className="font-medium">Bold text</strong> in a
                  sentence.
                </p>
                <p>
                  <em>Italic text</em> in a sentence.
                </p>
                <p>
                  <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">
                    Inline code
                  </code>{' '}
                  in a sentence.
                </p>
                <p>
                  <span className="underline">Underlined text</span> in a
                  sentence.
                </p>
              </div>
            </div>

            {/* Lists */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Lists</h3>
              <div className="grid grid-cols-2 gap-8">
                <div className="space-y-2">
                  <p className="font-medium">Unordered List</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>First item</li>
                    <li>Second item</li>
                    <li>Third item</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <p className="font-medium">Ordered List</p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>First item</li>
                    <li>Second item</li>
                    <li>Third item</li>
                  </ol>
                </div>
              </div>
            </div>

            {/* Blockquote */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Blockquote</h3>
              <blockquote className="border-l-2 pl-6 italic">
                "After all," he said, "everyone enjoys a good joke, so it's only
                fair that they should pay for the privilege."
              </blockquote>
            </div>
          </div>
        </section>

        {/* Buttons */}
        <section className="rounded-lg border bg-background p-6">
          <h2 className="relative -top-[38px] bg-background px-2.5 py-1.5 inline border rounded-md text-2xl font-semibold">
            Buttons
          </h2>
          <div className="space-y-8">
            {/* Default Variants */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Default Size</h3>
              <div className="flex flex-wrap gap-4">
                <Button dataTestId="btn-default">Default</Button>
                <Button dataTestId="btn-secondary" variant="secondary">
                  Secondary
                </Button>
                <Button dataTestId="btn-destructive" variant="destructive">
                  Destructive
                </Button>
                <Button dataTestId="btn-outline" variant="outline">
                  Outline
                </Button>
                <Button dataTestId="btn-ghost" variant="ghost">
                  Ghost
                </Button>
                <Button dataTestId="btn-link" variant="link">
                  Link
                </Button>
              </div>
            </div>

            {/* With Icons */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">With Icons</h3>
              <div className="flex flex-wrap gap-4">
                <Button dataTestId="btn-download">
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
                <Button dataTestId="btn-dark-mode" variant="secondary">
                  <Moon className="mr-2 h-4 w-4" />
                  Dark Mode
                </Button>
                <Button dataTestId="btn-delete" variant="destructive">
                  <Trash className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </div>
            </div>

            {/* Icon Buttons */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Icon Only</h3>
              <div className="flex flex-wrap gap-4">
                <Button
                  dataTestId="btn-icon-plus"
                  size="icon"
                  variant="outline"
                >
                  <Plus className="h-4 w-4" />
                </Button>
                <Button
                  dataTestId="btn-icon-moon"
                  size="icon"
                  variant="secondary"
                >
                  <Moon className="h-4 w-4" />
                </Button>
                <Button
                  dataTestId="btn-icon-trash"
                  size="icon"
                  variant="destructive"
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Inputs */}
        <section className="rounded-lg border bg-background p-6">
          <h2 className="relative -top-[38px] bg-background px-2.5 py-1.5 inline border rounded-md text-2xl font-semibold">
            Inputs
          </h2>
          <div className="space-y-8">
            {/* Input */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Input</h3>
              <div className="grid gap-4 max-w-sm">
                <Input placeholder="Default input" />
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input className="pl-9" placeholder="With icon" />
                </div>
                <Input disabled placeholder="Disabled input" />
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" placeholder="With label" />
                </div>
              </div>
            </div>

            {/* Select */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Select</h3>
              <div className="grid gap-4 max-w-sm">
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">Option 1</SelectItem>
                    <SelectItem value="2">Option 2</SelectItem>
                    <SelectItem value="3">Option 3</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Checkbox */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Checkbox</h3>
              <div className="flex flex-col gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox id="terms" />
                  <label htmlFor="terms">Accept terms and conditions</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="disabled" disabled />
                  <label htmlFor="disabled">Disabled</label>
                </div>
              </div>
            </div>

            {/* Textarea */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Textarea</h3>
              <div className="grid gap-4 max-w-sm">
                <Textarea placeholder="Type your message here." />
                <Textarea disabled placeholder="Disabled textarea" />
              </div>
            </div>
          </div>
        </section>

        {/* Data Display */}
        <section className="rounded-lg border bg-background p-6">
          <h2 className="relative -top-[38px] bg-background px-2.5 py-1.5 inline border rounded-md text-2xl font-semibold">
            Data Display
          </h2>
          <div className="space-y-8">
            {/* Avatar */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Avatar</h3>
              <div className="flex gap-4">
                <Avatar>
                  <AvatarImage src="https://github.com/shadcn.png" />
                  <AvatarFallback>CN</AvatarFallback>
                </Avatar>
                <Avatar>
                  <AvatarImage src="https://github.com/shadcn.png" />
                  <AvatarFallback>
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
              </div>
            </div>

            {/* Badge */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Badge</h3>
              <div className="flex gap-4">
                <Badge>Default</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="destructive">Destructive</Badge>
              </div>
            </div>

            {/* Card */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Card</h3>
              <div className="max-w-sm">
                <Card>
                  <CardHeader>
                    <CardTitle>Card Title</CardTitle>
                    <CardDescription>Card Description</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p>Card Content</p>
                  </CardContent>
                  <CardFooter>
                    <Button dataTestId="btn-card-action">Action</Button>
                  </CardFooter>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Feedback */}
        <section className="rounded-lg border bg-background p-6">
          <h2 className="relative -top-[38px] bg-background px-2.5 py-1.5 inline border rounded-md text-2xl font-semibold">
            Feedback
          </h2>
          <div className="space-y-8">
            {/* Alert */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Alert</h3>
              <div className="space-y-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Default Alert</AlertTitle>
                  <AlertDescription>
                    This is a default alert message.
                  </AlertDescription>
                </Alert>
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error Alert</AlertTitle>
                  <AlertDescription>
                    This is an error alert message.
                  </AlertDescription>
                </Alert>
              </div>
            </div>

            {/* Progress */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Progress</h3>
              <div className="space-y-4 max-w-sm">
                <Progress value={33} progressColor="bg-emerald-500" />
                <Progress value={66} progressColor="bg-rose-500" />
                <Progress value={100} progressColor="bg-sky-500" />
              </div>
            </div>
          </div>
        </section>

        {/* Navigation */}
        <section className="rounded-lg border bg-background p-6">
          <h2 className="relative -top-[38px] bg-background px-2.5 py-1.5 inline border rounded-md text-2xl font-semibold">
            Navigation
          </h2>
          <div className="space-y-8">
            {/* Tabs */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Tabs</h3>
              <Tabs defaultValue="account">
                <TabsList>
                  <TabsTrigger value="account">Account</TabsTrigger>
                  <TabsTrigger value="password">Password</TabsTrigger>
                </TabsList>
                <TabsContent value="account">Account settings</TabsContent>
                <TabsContent value="password">Password settings</TabsContent>
              </Tabs>
            </div>

            {/* Command */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Command</h3>
              <Command>
                <CommandInput placeholder="Type a command or search..." />
                <CommandList>
                  <CommandEmpty>No results found.</CommandEmpty>
                  <CommandGroup heading="Suggestions">
                    <CommandItem>
                      <Search className="mr-2 h-4 w-4" />
                      <span>Search</span>
                    </CommandItem>
                    <CommandItem>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </CommandItem>
                  </CommandGroup>
                </CommandList>
              </Command>
            </div>
          </div>
        </section>

        {/* Overlay */}
        <section className="rounded-lg border bg-background p-6">
          <h2 className="relative -top-[38px] bg-background px-2.5 py-1.5 inline border rounded-md text-2xl font-semibold">
            Overlay
          </h2>
          <div className="space-y-8">
            <div className="flex gap-4">
              <Dialog>
                <DialogTrigger asChild>
                  <Button dataTestId="btn-dialog-open">Open Dialog</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Dialog Title</DialogTitle>
                    <DialogDescription>
                      This is a dialog description.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">Dialog content goes here</div>
                </DialogContent>
              </Dialog>

              <Drawer direction="right">
                <DrawerTrigger asChild>
                  <Button dataTestId="btn-drawer-open">Open Drawer</Button>
                </DrawerTrigger>
                <DrawerContent>
                  <DrawerHeader>
                    <DrawerTitle>Drawer Title</DrawerTitle>
                    <DrawerDescription>
                      This is a drawer description.
                    </DrawerDescription>
                  </DrawerHeader>
                  <div className="p-4">Drawer content goes here</div>
                </DrawerContent>
              </Drawer>

              <Sheet>
                <SheetTrigger asChild>
                  <Button dataTestId="btn-sheet-open">Open Sheet</Button>
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader>
                    <SheetTitle>Sheet Title</SheetTitle>
                    <SheetDescription>
                      This is a sheet description.
                    </SheetDescription>
                  </SheetHeader>
                  <div className="py-4">Sheet content goes here</div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </section>

        {/* Disclosure */}
        <section className="rounded-lg border bg-background p-6">
          <h2 className="relative -top-[38px] bg-background px-2.5 py-1.5 inline border rounded-md text-2xl font-semibold">
            Disclosure
          </h2>
          <div className="space-y-8">
            {/* Accordion */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Accordion</h3>
              <Accordion type="single" collapsible>
                <AccordionItem value="item-1">
                  <AccordionTrigger>Is it accessible?</AccordionTrigger>
                  <AccordionContent>
                    Yes. It adheres to the WAI-ARIA design pattern.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-2">
                  <AccordionTrigger>Is it styled?</AccordionTrigger>
                  <AccordionContent>
                    Yes. It comes with default styles that matches your theme.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>

            {/* Collapsible */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Collapsible</h3>
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <Button dataTestId="btn-collapsible-toggle" variant="ghost">
                    <ChevronRight className="h-4 w-4" />
                    Toggle
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <div className="p-4">Collapsible content goes here</div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          </div>
        </section>
      </div>
    </ScrollArea>
  )
}
