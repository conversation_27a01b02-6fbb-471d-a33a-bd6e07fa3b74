import { atomWithStorage } from 'jotai/utils'
import type { EventStatus } from 'prime-front-service-client'
interface DefaultViewState {
  f?: string[]
  s?: string[]
  selected_columns?: string[]
  view?: string
}

export const DEFAULT_VIEW_STATE_KEY = 'defaultViewState'
export const DEFAULT_CONTAINERS_VIEW_STATE_KEY = 'defaultContainersViewState'
export const STORED_EVENTS_KEY = 'app_events'
export const SHOWN_NOTIFICATIONS_KEY = 'shown_notifications'

const INSIGHTS_VIEW_KEY = 'insights.selectedView'
const INSIGHTS_TYPE_KEY = 'insights.type'

const DATE_RANGE_KEY = 'casesChart.dateRange'

const ISSUE_TYPE_KEY = 'topContainer.issueType'

const TOP_CONTAINERS_VIEW_KEY = 'topContainers.selectedView'

export interface StoredEvent {
  id: string
  read: boolean
  status: EventStatus
}
export const defaultViewAtom = atomWithStorage<DefaultViewState | null>(
  DEFAULT_VIEW_STATE_KEY,
  null
)

export const defaultContainersViewAtom =
  atomWithStorage<DefaultViewState | null>(
    DEFAULT_CONTAINERS_VIEW_STATE_KEY,
    null
  )

export const storedEventsAtom = atomWithStorage<StoredEvent[]>(
  STORED_EVENTS_KEY,
  []
)
export const shownNotificationsAtom = atomWithStorage<string[]>(
  SHOWN_NOTIFICATIONS_KEY,
  []
)

interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
  files?: string[]
}

export interface ChatSession {
  messages: ChatMessage[]
  sessionId: string
  sourceId?: number
  containerId?: string
  docId?: number
}

export const CHAT_SESSIONS_KEY = 'chat_sessions'

export const chatSessionsAtom = atomWithStorage<Record<string, ChatSession>>(
  CHAT_SESSIONS_KEY,
  {}
)

export const caseChartDateRangeAtom = atomWithStorage<string>(
  DATE_RANGE_KEY,
  'sevenDays'
)
export const insightsSelectedViewAtom = atomWithStorage<string>(
  INSIGHTS_VIEW_KEY,
  'default-view'
)
export const insightsSelectedTypeAtom = atomWithStorage<'mitre' | 'linddun'>(
  INSIGHTS_TYPE_KEY,
  'mitre'
)

export const topContainersIssueTypeAtom = atomWithStorage<string>(
  ISSUE_TYPE_KEY,
  'Epic'
)

export const topContainersSelectedViewAtom = atomWithStorage<string>(
  TOP_CONTAINERS_VIEW_KEY,
  'default-view'
)
