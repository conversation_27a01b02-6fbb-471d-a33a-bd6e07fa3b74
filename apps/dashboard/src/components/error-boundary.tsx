import { Link } from 'react-router'
import { HomeIcon, LogIn, TriangleAlertIcon } from 'lucide-react'
import { Button } from '@libs/ui'
import { t } from 'i18next'
import { useEffect } from 'react'

interface ErrorBoundaryProps {
  isLoginError?: boolean
}
export const ErrorBoundary = ({ isLoginError }: ErrorBoundaryProps) => {
  useEffect(() => {
    localStorage.clear()
  }, [])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 p-4">
      <TriangleAlertIcon size={50} color="red" />
      <h1 className="text-2xl font-bold my-4">{t('somethingWentWrong')}</h1>
      <p className="text-gray-600 mb-4">
        {t('errors.anUnexpectedError')}. {t('pleaseTryAgainLater')}.
      </p>
      <Link to="/">
        {isLoginError ? (
          <Button
            variant="default"
            className="gap-4 capitalize"
            dataTestId="go-to-login"
          >
            <LogIn size={18} />
            {t('goToLogin')}
          </Button>
        ) : (
          <Button
            variant="default"
            className="gap-4 capitalize"
            dataTestId="go-to-home"
          >
            <HomeIcon size={18} />
            {t('goToHome')}
          </Button>
        )}
      </Link>
    </div>
  )
}
