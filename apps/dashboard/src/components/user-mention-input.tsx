import React, { useState, useRef } from 'react'
import { useGetUsers } from '../api/use-users-api'
import type { UserDataResponse } from 'prime-front-service-client'
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Command,
  CommandList,
  CommandItem,
} from '@libs/ui'
import { t } from 'i18next'
import { delay } from 'msw'

interface UserMentionInputProps {
  value: string
  onChange: (value: string) => void
}

const DELAY_DURATION = 300

export const UserMentionInput = ({
  value,
  onChange,
}: UserMentionInputProps) => {
  const [showPopover, setShowPopover] = useState(false)
  const [filteredUsers, setFilteredUsers] = useState<UserDataResponse[]>([])
  const { data: users } = useGetUsers()
  const inputRef = useRef<HTMLInputElement>(null)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value
    onChange(inputValue)

    const lastWord = inputValue.split(' ').pop()

    if (lastWord?.startsWith('@') && users && users.length > 0) {
      const searchTerm = lastWord.slice(1).toLowerCase()
      const filtered = users.filter((user) =>
        `${user.first_name} ${user.last_name} ${user.email_address}`
          .toLowerCase()
          .includes(searchTerm)
      )
      setFilteredUsers(filtered)
      setShowPopover(true)
    } else {
      setShowPopover(false)
    }
  }

  const handleUserSelect = async (user: UserDataResponse) => {
    if (inputRef.current) {
      const cursorPosition = inputRef.current.selectionStart || 0
      const textBeforeCursor = value.slice(0, cursorPosition)
      const textAfterCursor = value.slice(cursorPosition)

      const fullName =
        user.first_name && user.last_name
          ? `${user.first_name} ${user.last_name}`
          : user.email_address

      const mention = `${fullName}`
      const updatedText =
        `${textBeforeCursor}${mention} ${textAfterCursor}`.trim()

      onChange(updatedText)
      setShowPopover(false)

      await delay(DELAY_DURATION)

      const newCursorPosition = textBeforeCursor.length + mention.length + 1
      inputRef.current.setSelectionRange(newCursorPosition, newCursorPosition)
      inputRef.current.focus()
    }
  }

  return (
    <div className="relative w-full">
      <Popover open={showPopover}>
        <PopoverTrigger asChild>
          <Input
            ref={inputRef}
            value={value}
            onChange={handleInputChange}
            className="w-full bg-white"
            id="comments"
            type="text"
            placeholder="Type.."
          />
        </PopoverTrigger>
        {showPopover && (
          <PopoverContent className="w-72">
            {filteredUsers.length > 0 ? (
              <Command>
                <CommandList>
                  {filteredUsers.map((user) => (
                    <CommandItem
                      key={user.user_id}
                      onSelect={() => handleUserSelect(user)}
                      className="cursor-pointer hover:bg-gray-100 py-2"
                    >
                      {user.first_name || user.last_name
                        ? `${user.first_name} ${user.last_name}`
                        : user.email_address}
                    </CommandItem>
                  ))}
                </CommandList>
              </Command>
            ) : (
              <p>{t('noUsersFound')}</p>
            )}
          </PopoverContent>
        )}
      </Popover>
    </div>
  )
}
