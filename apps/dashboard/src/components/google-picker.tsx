import { Button } from '@libs/ui'
import {
  useGooglePicker,
  type GooglePickerFile,
} from '../hooks/use-google-picker'

export interface GooglePickerProps {
  buttonText?: string
  buttonVariant?:
    | 'default'
    | 'secondary'
    | 'outline'
    | 'destructive'
    | 'ghost'
    | 'link'
  className?: string
  mimeTypes?: string[]
  multiSelect?: boolean
  viewId?: string
  dataTestId?: string
  onSelect: (files: GooglePickerFile[]) => void
  onCancel?: () => void
}

export const GooglePicker = ({
  buttonText = 'Select from Google Drive',
  buttonVariant = 'default',
  className,
  mimeTypes = ['application/pdf', 'image/jpeg', 'image/png'],
  multiSelect = true,
  viewId = 'DOCS',
  dataTestId = 'google-picker-button',
  onSelect,
  onCancel = () => console.log('File selection cancelled'),
}: GooglePickerProps) => {
  const handleFileSelect = (files: GooglePickerFile[]) => {
    onSelect(files)
  }

  const { openPicker, isReady, isLoading } = useGooglePicker({
    viewId,
    mimeTypes,
    multiSelect,
    onSelect: handleFileSelect,
    onCancel,
  })

  return (
    <div className={className}>
      <Button
        dataTestId={dataTestId}
        variant={buttonVariant}
        onClick={openPicker}
        disabled={!isReady || isLoading}
      >
        {isLoading ? 'Loading...' : buttonText}
      </Button>
    </div>
  )
}
