import { type ReactNode, useEffect } from 'react'
import { <PERSON>aster, TooltipProvider, useOnLine } from '@libs/ui'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { I18nextProvider } from 'react-i18next'
import i18n from '../i18n'
import { useLDClient } from 'launchdarkly-react-client-sdk'
import { getUserInfo } from '../auth/auth-utils'
import { BeamerIntegration } from '../hooks/use-beamer'
import { NuqsAdapter } from 'nuqs/adapters/react-router/v7'

export const queryClient = new QueryClient()

export const Providers = ({ children }: { children: ReactNode }) => {
  useOnLine()
  const userInfo = getUserInfo()
  const ldClient = useLDClient()

  useEffect(() => {
    // TODO: fix this workaround by using error boundary component
    if (userInfo) {
      const userInfoKeys = Object.keys(userInfo)
      if (
        userInfoKeys.includes('accountId') ||
        userInfoKeys.includes('userId')
      ) {
        localStorage.clear()
        window.location.reload()
      }
      ldClient?.identify({
        kind: 'user',
        key: userInfo?.account_id,
        email: userInfo?.user_id,
        accountId: userInfo?.account_id,
        domain: userInfo?.user_id?.split('@')?.[1],
      })
    }
  }, [ldClient, userInfo])

  return (
    <NuqsAdapter>
      <I18nextProvider i18n={i18n}>
        <QueryClientProvider client={queryClient}>
          <Toaster position="top-right" theme="light" richColors closeButton />
          <TooltipProvider delayDuration={200}>{children}</TooltipProvider>
          <BeamerIntegration />
        </QueryClientProvider>
      </I18nextProvider>
    </NuqsAdapter>
  )
}
