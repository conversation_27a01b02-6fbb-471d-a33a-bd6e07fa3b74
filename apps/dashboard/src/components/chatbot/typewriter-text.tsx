import { useEffect, useState } from 'react'
import { formatMessageMarkdown } from './chatbot-utils'

const WORD_DELAY = 5
const CHUNK_SIZE = 1

export const TypewriterText = ({
  content,
  isNewMessage,
}: {
  content: string
  isNewMessage: boolean
}) => {
  const [displayedContent, setDisplayedContent] = useState(
    isNewMessage ? '' : content
  )
  const [isComplete, setIsComplete] = useState(!isNewMessage)

  useEffect(() => {
    if (!isNewMessage || content === displayedContent) {
      setIsComplete(true)
      return
    }

    setIsComplete(false)

    const currentLength = displayedContent.length
    const nextChars = content.slice(currentLength, currentLength + CHUNK_SIZE)

    if (!nextChars) {
      setIsComplete(true)
      return
    }

    const timer = setTimeout(() => {
      setDisplayedContent(content.slice(0, currentLength + CHUNK_SIZE))
    }, WORD_DELAY)

    return () => clearTimeout(timer)
  }, [content, displayedContent, isNewMessage])

  if (isComplete) {
    return (
      <div className="overflow-x-auto">{formatMessageMarkdown(content)}</div>
    )
  }

  return <div className="whitespace-pre-wrap">{displayedContent}</div>
}
