import Markdown from 'react-markdown'
import { <PERSON>rism as Synta<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-syntax-highlighter'
import { oneDark as Theme } from 'react-syntax-highlighter/dist/esm/styles/prism'

export const formatMessageMarkdown = (content: string) => {
  return (
    <Markdown
      components={{
        h1: ({ children }) => (
          <h1 className="text-lg font-bold mt-4 mb-2">{children}</h1>
        ),
        h2: ({ children }) => (
          <h2 className="text-md font-semibold mt-3 mb-2">{children}</h2>
        ),
        p: ({ children }) => <p className="mb-2">{children}</p>,
        ul: ({ children }) => (
          <ul className="list-disc pl-4 mb-2 space-y-1">{children}</ul>
        ),
        ol: ({ children }) => (
          <ol className="list-decimal pl-4 mb-2 space-y-1">{children}</ol>
        ),
        li: ({ children }) => <li className="mb-1">{children}</li>,
        strong: ({ children }) => (
          <strong className="font-semibold">{children}</strong>
        ),
        blockquote: ({ children }) => (
          <blockquote className="border-l-2 border-slate-300 pl-4 my-2 italic">
            {children}
          </blockquote>
        ),
        code: ({
          className,
          children,
          ...props
        }: React.HTMLAttributes<HTMLElement>) => {
          const match = /language-(\w+)/.exec(className || '')
          const language = match ? match[1] : 'text'
          const isInline = !match && !className

          return !isInline ? (
            <div className="relative">
              <SyntaxHighlighter
                language={language}
                customStyle={{
                  margin: '1em 0',
                  borderRadius: '0.5rem',
                  fontSize: '12px',
                }}
                {...props}
                style={Theme}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            </div>
          ) : (
            <code className="bg-slate-200 rounded px-1 py-0.5" {...props}>
              {children}
            </code>
          )
        },
      }}
    >
      {content}
    </Markdown>
  )
}
