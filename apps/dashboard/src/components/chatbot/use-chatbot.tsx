/* eslint-disable max-lines */
import { useState, useEffect, useMemo, useCallback } from 'react'

import {
  useAiApiAddNewConversation,
  useAiApiDeleteConversation,
  useAiApiGetConversation,
  useAiApiHistory,
  useAiApiQuery,
} from '../../api/use-ai-api'
import type {
  ChatLocation,
  ConversationMessageOutput,
} from 'prime-front-service-client'

interface ChatbotProps {
  container_id: number
  doc_id: number
  chat_location: ChatLocation
}

export const useChatbot = ({
  container_id,
  doc_id,
  chat_location,
}: ChatbotProps) => {
  const [sessionId, setSessionId] = useState<number | null>(null)
  const [messages, setMessages] = useState<ConversationMessageOutput[]>([])

  const chat_location_identifier = useMemo(() => {
    if (chat_location === 'container') {
      return container_id
    } else if (chat_location === 'design_doc') {
      return doc_id
    }
  }, [container_id, doc_id, chat_location])

  const {
    data: historyData,
    // isLoading: isLoadingHistory,
    // isPending: isPendingHistory,
    isFetching: isFetchingHistory,
    isError: isErrorHistory,
    isSuccess: isSuccessHistory,
    isFetchedAfterMount: isFetchedAfterMountHistory,
  } = useAiApiHistory({
    chat_location: chat_location,
    chat_location_identifier,
  })

  const { mutate: addNewConversation } = useAiApiAddNewConversation()

  const {
    data: conversationData,
    refetch: refetchConversation,
    isSuccess: isSuccessConversation,
    isRefetching: isRefetchingConversation,
    isPending: isPendingConversation,
    // isLoading: isLoadingConversation,
    // isFetching: isFetchingConversation,
    isFetchedAfterMount: isFetchedAfterMountConversation,
  } = useAiApiGetConversation({
    conversation_id: sessionId as number,
  })

  const agentApiQuery = useAiApiQuery()
  const agentApiDeleteSession = useAiApiDeleteConversation()

  const handleAddNewConversation = useCallback(() => {
    addNewConversation(
      {
        chat_location: chat_location,
        chat_location_identifier: chat_location_identifier as number,
      },
      {
        onSuccess: (data) => {
          setSessionId(data.conversation_id)
          refetchConversation()
        },
      }
    )
  }, [
    addNewConversation,
    chat_location,
    chat_location_identifier,
    refetchConversation,
  ])

  useEffect(() => {
    if (historyData) {
      const sessionId = historyData?.results?.[0]?.conversation_id
      if (sessionId) {
        setSessionId(historyData?.results?.[0].conversation_id)
        refetchConversation()
      }
    }
  }, [historyData, refetchConversation])

  useEffect(() => {
    // if no sessions exist, create a new conversation
    if (historyData?.results?.length === 0) {
      handleAddNewConversation()
    }
  }, [handleAddNewConversation, historyData?.results?.length])

  useEffect(() => {
    if (conversationData) {
      const messages = conversationData.chat_messages || []
      setMessages(messages)
    }
  }, [conversationData])

  const isLoading = useMemo(() => {
    if (isFetchingHistory) return true
    if (isPendingConversation) return true
    if (!isSuccessConversation) return true
    if (!isSuccessHistory) return true
    if (!isFetchedAfterMountHistory) return true
    if (!isFetchedAfterMountConversation) return true

    return false
  }, [
    isFetchedAfterMountConversation,
    isFetchedAfterMountHistory,
    isFetchingHistory,
    isPendingConversation,
    isSuccessConversation,
    isSuccessHistory,
  ])

  return {
    sessionId,
    setSessionId,
    messages,
    setMessages,
    isErrorHistory,
    isSuccessHistory,
    isRefetchingConversation,
    isSuccessConversation,
    agentApiQuery,
    agentApiDeleteSession,
    handleAddNewConversation,
    isLoading,
  }
}
