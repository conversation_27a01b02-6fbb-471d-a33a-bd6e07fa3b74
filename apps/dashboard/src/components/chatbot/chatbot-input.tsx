'use client'

import { cn } from '@libs/common'
import { Button, Textarea } from '@libs/ui'
import { ArrowUpIcon } from 'lucide-react'
import type React from 'react'
import { createContext, useContext } from 'react'

interface ChatInputContextValue {
  value?: string
  onChange?: React.ChangeEventHandler<HTMLTextAreaElement>
  onSubmit?: () => void
  loading?: boolean
  onStop?: () => void
  variant?: 'default' | 'unstyled'
  rows?: number
}

const ChatInputContext = createContext<ChatInputContextValue>({})

interface ChatInputProps extends Omit<ChatInputContextValue, 'variant'> {
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'unstyled'
  rows?: number
}

function ChatInput({
  children,
  className,
  variant = 'default',
  value,
  onChange,
  onSubmit,
  loading,
  onStop,
  rows = 1,
}: ChatInputProps) {
  const contextValue: ChatInputContextValue = {
    value,
    onChange,
    onSubmit,
    loading,
    onStop,
    variant,
    rows,
  }

  return (
    <ChatInputContext.Provider value={contextValue}>
      <div
        className={cn(
          variant === 'default' &&
            'flex flex-col items-end w-full p-2 rounded-2xl border border-input  focus-within:ring-1 focus-within:ring-ring focus-within:outline-none',
          variant === 'unstyled' && 'flex items-start gap-2 w-full',
          className
        )}
      >
        {children}
      </div>
    </ChatInputContext.Provider>
  )
}

ChatInput.displayName = 'ChatInput'

interface ChatInputTextAreaProps extends React.ComponentProps<typeof Textarea> {
  value?: string
  onChange?: React.ChangeEventHandler<HTMLTextAreaElement>
  onSubmit?: () => void
  variant?: 'default' | 'unstyled'
}

function ChatInputTextArea({
  onSubmit: onSubmitProp,
  value: valueProp,
  onChange: onChangeProp,
  className,
  variant: variantProp,
  ...props
}: ChatInputTextAreaProps) {
  const context = useContext(ChatInputContext)
  const value = valueProp ?? context.value ?? ''
  const onChange = onChangeProp ?? context.onChange
  const onSubmit = onSubmitProp ?? context.onSubmit
  const rows = context.rows ?? 3

  // Convert parent variant to textarea variant unless explicitly overridden
  const variant =
    variantProp ?? (context.variant === 'default' ? 'unstyled' : 'default')

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!onSubmit) {
      return
    }
    if (e.key === 'Enter' && !e.shiftKey) {
      if (typeof value !== 'string' || value.trim().length === 0) {
        return
      }
      e.preventDefault()
      onSubmit()
    }
  }

  return (
    <Textarea
      {...props}
      value={value}
      onChange={onChange}
      onKeyDown={handleKeyDown}
      className={cn(
        'max-h-[400px] min-h-0 resize-none overflow-x-hidden',
        variant === 'unstyled' &&
          'border-none focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none',
        className
      )}
      rows={rows}
    />
  )
}

ChatInputTextArea.displayName = 'ChatInputTextArea'

interface ChatInputSubmitProps extends React.ComponentProps<typeof Button> {
  onSubmit?: () => void
  loading?: boolean
  onStop?: () => void
}

function ChatInputSubmit({
  onSubmit: onSubmitProp,
  loading: loadingProp,
  className,
  ...props
}: ChatInputSubmitProps) {
  const context = useContext(ChatInputContext)
  const loading = loadingProp ?? context.loading
  const onSubmit = onSubmitProp ?? context.onSubmit

  const isDisabled =
    loading ||
    typeof context.value !== 'string' ||
    context.value.trim().length === 0

  return (
    <Button
      className={cn(
        'shrink-0 rounded-full p-1.5 h-fit border dark:border-zinc-600',
        className
      )}
      disabled={isDisabled}
      onClick={(event) => {
        event.preventDefault()
        if (!isDisabled) {
          onSubmit?.()
        }
      }}
      {...props}
    >
      <ArrowUpIcon />
    </Button>
  )
}

ChatInputSubmit.displayName = 'ChatInputSubmit'

export { ChatInput, ChatInputTextArea, ChatInputSubmit }
