import { useState } from 'react'
import {
  Button,
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
} from '@libs/ui'
import { PlusIcon } from 'lucide-react'
import type { PolicyMetadata } from 'prime-front-service-client/src/models'
import { t } from 'i18next'

interface ChatInputAddContextProps {
  data: PolicyMetadata[]
  selectedFiles: string[]
  setSelectedFiles: (files: string[]) => void
}

export function ChatInputAddContext({
  data,
  selectedFiles,
  setSelectedFiles,
}: ChatInputAddContextProps) {
  const [open, setOpen] = useState(false)
  const handleToggleFile = (fileId: string, checked: boolean) => {
    const updatedFiles = checked
      ? [...selectedFiles, fileId]
      : selectedFiles.filter((f) => f !== fileId)

    setSelectedFiles(updatedFiles)
  }

  return (
    <div className="flex flex-col gap-2">
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            dataTestId="add-context"
            variant="outline"
            className="rounded-full p-1.5 border w-9 h-9"
          >
            <PlusIcon />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="max-h-[400px] overflow-y-auto"
        >
          <h1 className="font-medium px-2 my-1 capitalize">{t('policies')}</h1>
          {data?.map((option) => {
            const name = decodeURIComponent(option.name).trim()
            return (
              <DropdownMenuCheckboxItem
                key={option.id}
                checked={selectedFiles.includes(option.id.toString())}
                onCheckedChange={(checked) =>
                  handleToggleFile(option.id.toString(), !!checked)
                }
              >
                {name}
              </DropdownMenuCheckboxItem>
            )
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
