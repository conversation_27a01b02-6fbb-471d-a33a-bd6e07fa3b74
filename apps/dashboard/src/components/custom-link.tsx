import React from 'react'
import type { LinkProps as RouterLinkProps, To } from 'react-router'
import { NavLink } from 'react-router'
import queryString from 'query-string'
import type { UseUrlStateOptions } from '@libs/ui'
import { useUrlState } from '@libs/ui'
import { useUrlStateDefaultOptions } from '../pages/workroom/hooks/use-selected-columns'

export type MergeState = Record<string, any>
export type UrlStateTo = string | To

interface CustomLinkProps extends Omit<RouterLinkProps, 'to'> {
  to: UrlStateTo
  preserveState?: boolean
  mergeState?: MergeState
  urlStateOptions?: UseUrlStateOptions
}

export const CustomLink: React.FC<CustomLinkProps> = ({
  to,
  preserveState = true,
  mergeState = {},
  urlStateOptions,
  className,
  ...props
}) => {
  const [urlState, setUrlState] = useUrlState(
    {},
    urlStateOptions || useUrlStateDefaultOptions
  )

  const handleClick = (
    event: React.MouseEvent<HTMLAnchorElement, MouseEvent>
  ) => {
    event.preventDefault()

    let pathname = ''
    let search: string | undefined

    if (typeof to === 'string') {
      // prettier-ignore
      [pathname, search] = to.split('?')
    } else {
      pathname = to.pathname || ''
      search = to.search
    }

    let newState = preserveState ? { ...urlState } : {}
    if (search) {
      newState = { ...newState, ...queryString.parse(search) }
    }
    newState = { ...newState, ...mergeState }

    setUrlState({
      ...newState,
      __path: pathname, // Use a special key to indicate the new pathname
    })
  }

  const constructTo = () => {
    if (typeof to === 'string') {
      const [pathname, search] = to.split('?')
      return { pathname, search: search ? `?${search}` : '' }
    }
    return to
  }

  return (
    <NavLink
      to={constructTo()}
      onClick={handleClick}
      className={className}
      {...props}
    />
  )
}
