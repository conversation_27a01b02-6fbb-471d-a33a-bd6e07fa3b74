'use client'

import { Bell, BellIcon, CheckCircle, Clock, XCircle } from 'lucide-react'
import { Button, Popover, <PERSON>overContent, PopoverTrigger } from '@libs/ui'
import { useGetAppEvents } from '../api/use-events-api'
import { toast } from 'sonner'
import { useAtom } from 'jotai'
import { storedEventsAtom, shownNotificationsAtom } from '../config/store'
import { cn } from '@libs/common'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useNavigate } from 'react-router'

const MAX_TOASTS = 3

export default function NotificationPopover() {
  const navigate = useNavigate()
  const [open, setOpen] = useState(false)
  const { data: events, isError } = useGetAppEvents()
  const [storedEvents, setStoredEvents] = useAtom(storedEventsAtom)
  const [shownNotifications, setShownNotifications] = useAtom(
    shownNotificationsAtom
  )
  const previousEventsRef = useRef<typeof events>()

  const sortedEvents = useMemo(() => {
    if (!events) return []

    return [...events].sort((a, b) => {
      const dateA = a.created_at ? new Date(a.created_at).getTime() : 0
      const dateB = b.created_at ? new Date(b.created_at).getTime() : 0
      return dateB - dateA
    })
  }, [events])

  useEffect(() => {
    if (!events || events === previousEventsRef.current) return

    previousEventsRef.current = events

    const existingEventIds = new Set(storedEvents.map((e) => String(e.id)))
    const newStoredEvents = [...storedEvents]
    const newShownNotifications = [...shownNotifications]
    let hasNewEvents = false

    const unshownCompletedEvents = events
      .filter(
        (event) =>
          event.status === 'completed' &&
          !shownNotifications.includes(event?.id || '')
      )
      .sort((a, b) => {
        const dateA = a.created_at ? new Date(a.created_at).getTime() : 0
        const dateB = b.created_at ? new Date(b.created_at).getTime() : 0
        return dateB - dateA
      })
      .slice(0, MAX_TOASTS)

    events.forEach((event) => {
      if (!existingEventIds.has(event?.id || '')) {
        newStoredEvents.push({
          id: event?.id || '',
          status: event.status,
          read: false,
        })
        hasNewEvents = true
      }
    })

    unshownCompletedEvents.forEach((event) => {
      toast.info(event.message.title, {
        description: event.message.description,
        action: event?.action
          ? {
              label: 'View',
              onClick: () => navigate(event?.action || '/'),
            }
          : undefined,
        duration: 8000,
      })
      newShownNotifications.push(event?.id || '')
    })

    const remainingEvents = events.filter(
      (event) =>
        event.status === 'completed' &&
        !shownNotifications.includes(event?.id || '') &&
        !unshownCompletedEvents.includes(event)
    )

    remainingEvents.forEach((event) => {
      newShownNotifications.push(event?.id || '')
    })

    if (hasNewEvents) {
      setStoredEvents(newStoredEvents)
    }

    if (newShownNotifications.length > shownNotifications.length) {
      setShownNotifications(newShownNotifications)
    }
  }, [
    events,
    storedEvents,
    shownNotifications,
    setStoredEvents,
    setShownNotifications,
    navigate,
  ])

  const handleMarkAsRead = useCallback(
    (eventId: string) => {
      setStoredEvents((current) =>
        current.map((event) =>
          event.id === eventId ? { ...event, read: true } : event
        )
      )
    },
    [setStoredEvents]
  )

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="link"
          className="text-white relative h-12 w-12 p-0 rounded-lg"
          dataTestId="notification-button"
        >
          <BellIcon className="h-6 w-6" />
          {storedEvents.some((e) => !e.read) && (
            <span className="absolute top-2 right-2 h-2 w-2 rounded-full bg-destructive" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-96 min-h-80 p-0 overflow-hidden border-none"
        align="start"
      >
        <div className="bg-gradient-to-r from-slate-500 to-zinc-500 px-5 py-4 text-white">
          <h3 className="text-base font-bold">Events</h3>
        </div>
        <div className="max-h-[300px] overflow-y-auto">
          {isError || !sortedEvents.length ? (
            <div className="flex flex-col items-center justify-center py-10 px-4 text-center">
              <div className="mb-3 rounded-full bg-slate-100 p-3 dark:bg-slate-800">
                <Bell className="h-8 w-8 text-slate-400" />
              </div>
              <p className="text-sm font-medium text-slate-500">
                All caught up!
              </p>
              <p className="text-xs text-slate-400">
                No notifications to display
              </p>
            </div>
          ) : (
            sortedEvents.map((event) => {
              const isRead =
                storedEvents.find((e) => e.id === event.id)?.read ?? false

              return (
                <div
                  key={event.id}
                  className="border-t cursor-pointer hover:bg-gray-50"
                  onClick={() => handleMarkAsRead(event?.id || '')}
                >
                  <div className="p-4">
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center justify-between gap-2">
                        <div className="font-semibold text-sm">
                          {event.message.title}
                        </div>
                        <div className="relative">
                          {!isRead && (
                            <div className="h-2 w-2 rounded-full bg-blue-500" />
                          )}
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {event.message.description}
                      </p>

                      <div className="mt-2 flex items-center gap-2">
                        {event.status === 'completed' && (
                          <span className="flex items-center gap-1 text-xs font-medium text-emerald-500">
                            <CheckCircle className="h-3 w-3" /> Completed
                          </span>
                        )}
                        {event.status === 'failed' && (
                          <span className="flex items-center gap-1 text-xs font-medium text-rose-500">
                            <XCircle className="h-3 w-3" /> Failed
                          </span>
                        )}
                        {event.status === 'in_progress' && (
                          <span className="flex items-center gap-1 text-xs font-medium text-amber-500">
                            <Clock className="h-3 w-3" /> In Progress
                          </span>
                        )}
                        <div className="ml-auto text-xs text-slate-400">
                          {event.progress}%
                        </div>
                      </div>

                      <div className="mt-1 h-1 w-full overflow-hidden rounded-full bg-slate-100 dark:bg-slate-800">
                        <div
                          className={cn(
                            'h-full rounded-full',
                            event.status === 'completed'
                              ? 'bg-emerald-500'
                              : event.status === 'failed'
                              ? 'bg-rose-500'
                              : 'bg-amber-500'
                          )}
                          style={{ width: `${event.progress}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )
            })
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}
