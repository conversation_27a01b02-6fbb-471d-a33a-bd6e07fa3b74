/* eslint-disable max-lines */
import {
  FileIcon,
  Home,
  LogOutIcon,
  MegaphoneIcon,
  Settings,
  ShieldAlert,
  SquareKanbanIcon,
  TablePropertiesIcon,
  PaletteIcon,
} from 'lucide-react'
import { NavLink, useLocation, useNavigate } from 'react-router'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Button,
  Logo,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@libs/ui'
import { t } from 'i18next'
import {
  defaultFilterParams,
  defaultPsvFilterParams,
  defaultSortParam,
  defaultContainersSortParam,
  getParsedArrayOfObjects,
} from '../router/router'
import { staticColumns } from '../pages/workroom/components'
import {
  defaultView<PERSON>tom,
  defaultContainersViewAtom,
  chatSessions<PERSON><PERSON>,
} from '../config/store'
import { useAtomValue } from 'jotai'
import { cn } from '@libs/common'
import { containersStaticColumns } from '../pages/containers/components/container-columns'
import { serializePsv, serializeWorkroom } from '../router/router-utils'

import { useFlagsWrapper } from '../hooks/use-flags-wrapper'
import NotificationPopover from './notification-popover'
import { useAiApiBackupConversation } from '../api/use-ai-api'
import { useEffect } from 'react'

const navLinkClassName =
  'aria-[current=page]:text-slate-700 aria-[current=page]:bg-white flex h-12 w-12 items-center justify-center rounded-lg transition-colors  bg-slate-600 hover:opacity-90'

const navIconClassName = 'h-6 w-6'

export const Sidebar = () => {
  const chatSessions = useAtomValue(chatSessionsAtom)
  const { mutate: backupChatSessions } = useAiApiBackupConversation()

  useEffect(() => {
    if (Object.keys(chatSessions).length === 0) {
      return
    }
    backupChatSessions(chatSessions)
  }, [backupChatSessions, chatSessions])

  const navigate = useNavigate()
  const defaultView = useAtomValue(defaultViewAtom)
  const defaultContainersView = useAtomValue(defaultContainersViewAtom)

  const { showBeamer, notificationEvents, designKitPage } = useFlagsWrapper()

  const location = useLocation()
  const isSettingsActive = location.pathname.startsWith('/settings')

  const serializedWorkroomSearch = serializeWorkroom({
    f: defaultView?.f
      ? getParsedArrayOfObjects(defaultView.f)
      : defaultFilterParams,
    s: defaultView?.s
      ? getParsedArrayOfObjects(defaultView.s)
      : [defaultSortParam],
    selected_columns: defaultView?.selected_columns || staticColumns,
  })

  const serializedPsvSearch = serializePsv({
    f: defaultPsvFilterParams,
  })

  const serializedContainersSearch = serializeWorkroom({
    f: defaultContainersView?.f
      ? getParsedArrayOfObjects(defaultContainersView.f)
      : [
          {
            field: 'provider_fields.issuetype',
            op: 'eq',
            value: ['Epic'],
          },
        ],
    s: defaultContainersView?.s
      ? getParsedArrayOfObjects(defaultContainersView.s)
      : [defaultContainersSortParam],
    selected_columns:
      defaultContainersView?.selected_columns || containersStaticColumns,
  })

  return (
    <aside
      data-testid="sidebar"
      className="fixed inset-y-0 left-0 hidden w-[84px] flex-col border-r sm:flex z-40 bg-slate-700 text-white"
    >
      <NavLink
        to="/"
        className="mt-6 mb-4 hover:opacity-80 flex justify-center "
      >
        <Logo />
      </NavLink>

      <nav className="flex flex-col items-center gap-4 px-2 sm:py-5">
        <Tooltip>
          <TooltipTrigger asChild>
            <NavLink to="/" className={navLinkClassName}>
              <Home className={navIconClassName} />
              <span className="sr-only capitalize">{t('home')}</span>
            </NavLink>
          </TooltipTrigger>
          <TooltipContent side="right">
            <span className="capitalize">{t('home')}</span>
          </TooltipContent>
        </Tooltip>
        {
          <Tooltip>
            <TooltipTrigger>
              <NavLink
                to={{ pathname: '/psv', search: serializedPsvSearch }}
                data-testid="psv-link"
                className={navLinkClassName}
              >
                <ShieldAlert className={navIconClassName} />
                <span className="sr-only capitalize">
                  {t('securityViolations')}
                </span>
              </NavLink>
            </TooltipTrigger>
            <TooltipContent side="right">
              <span className="capitalize">{t('securityViolations')}</span>
            </TooltipContent>
          </Tooltip>
        }
        <Tooltip>
          <TooltipTrigger>
            <NavLink
              to={{ pathname: '/workroom', search: serializedWorkroomSearch }}
              data-testid="workroom-link"
              className={navLinkClassName}
            >
              <SquareKanbanIcon className={navIconClassName} />
              <span className="sr-only capitalize">{t('workroom')}</span>
            </NavLink>
          </TooltipTrigger>
          <TooltipContent side="right">
            <span className="capitalize">{t('workroom')}</span>
          </TooltipContent>
        </Tooltip>
        {
          <Tooltip>
            <TooltipTrigger>
              <NavLink
                to={{
                  pathname: '/containers',
                  search: serializedContainersSearch,
                }}
                data-testid="containers-link"
                className={navLinkClassName}
              >
                <TablePropertiesIcon className={navIconClassName} />
                <span className="sr-only capitalize">{t('containers')}</span>
              </NavLink>
            </TooltipTrigger>
            <TooltipContent side="right">
              <span className="capitalize">{t('containersView')}</span>
            </TooltipContent>
          </Tooltip>
        }

        {
          <Tooltip>
            <TooltipTrigger>
              <NavLink
                to={{
                  pathname: '/design-reviews',
                  search: '?tab=default',
                }}
                data-testid="documents-link"
                className={navLinkClassName}
              >
                <FileIcon className={navIconClassName} />
                <span className="sr-only capitalize">
                  {t('securityReviews')}
                </span>
              </NavLink>
            </TooltipTrigger>
            <TooltipContent side="right">
              <span className="capitalize">{t('securityReviews')}</span>
            </TooltipContent>
          </Tooltip>
        }

        {designKitPage && (
          <Tooltip>
            <TooltipTrigger>
              <NavLink
                to="/design-kit"
                data-testid="design-kit-link"
                className={navLinkClassName}
              >
                <PaletteIcon className={navIconClassName} />
                <span className="sr-only capitalize">{t('designKit')}</span>
              </NavLink>
            </TooltipTrigger>
            <TooltipContent side="right">
              <span className="capitalize">{t('designKit')}</span>
            </TooltipContent>
          </Tooltip>
        )}
      </nav>

      <nav className="mt-auto flex flex-col items-center gap-4 px-2 sm:py-5">
        {showBeamer && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                id="beamer-button"
                dataTestId="beamer-button"
                className={cn(
                  'beamer-button text-white relative h-12 w-12 p-0 rounded-lg'
                )}
              >
                <MegaphoneIcon className={navIconClassName} />
                <span className="sr-only capitalize">{t('whatsNew')}</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <span className="capitalize">{t('whatsNew')}</span>
            </TooltipContent>
          </Tooltip>
        )}

        {notificationEvents && <NotificationPopover />}

        <Tooltip>
          <TooltipTrigger asChild>
            <NavLink
              data-testid="settings-link"
              to="/settings/sources"
              className={cn(
                navLinkClassName,
                isSettingsActive && 'text-slate-700 bg-white'
              )}
            >
              <Settings className={navIconClassName} />
              <span className="sr-only capitalize">{t('settings')}</span>
            </NavLink>
          </TooltipTrigger>
          <TooltipContent side="right">
            <span className="capitalize">{t('settings')}</span>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="flex items-center justify-center">
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="link"
                    className="text-white w-12 h-12 p-0"
                    dataTestId="logout-button"
                  >
                    <LogOutIcon className={navIconClassName} />
                    <span className="sr-only">{t('logout')}</span>
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>{t('areYouSure')}</AlertDialogTitle>
                    <AlertDialogDescription>
                      {t('logoutActionMessage')}
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                    <AlertDialogAction onClick={() => navigate('/logout')}>
                      {t('continue')}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </span>
          </TooltipTrigger>
          <TooltipContent side="right">{t('logout')}</TooltipContent>
        </Tooltip>
      </nav>
    </aside>
  )
}
