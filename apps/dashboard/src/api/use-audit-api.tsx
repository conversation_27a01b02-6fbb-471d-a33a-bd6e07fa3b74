import { toast } from 'sonner'
import { useQuery } from '@tanstack/react-query'
import { auditApi } from './clients'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const useAuditApi = () =>
  useQuery({
    queryKey: ['getAuditLogs'],
    queryFn: async () => {
      try {
        return await auditApi.getAuditEvents()
      } catch (error) {
        toast.error('Failed to fetch audit logs')
        throw new Error('Failed to fetch audit logs')
      }
    },
    ...defaultQueryConfig,
  })
