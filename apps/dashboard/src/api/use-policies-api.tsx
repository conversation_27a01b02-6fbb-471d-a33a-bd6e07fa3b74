import { useMutation, useQuery } from '@tanstack/react-query'
import { policiesApi } from './clients'
import type { UpdatePolicyRequest } from 'prime-front-service-client'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

// get policies
export const useGetPolicies = () =>
  useQuery({
    queryKey: ['policies'],
    queryFn: async () => {
      return await policiesApi.getPolicies()
    },
    ...defaultQueryConfig,
  })

// upload policies
export const useUploadPolicies = () =>
  useMutation({
    mutationKey: ['uploadPolicies'],
    mutationFn: async (files: Blob[]) => {
      return await policiesApi.uploadPolicies({
        policy_files: files,
      })
    },
    ...defaultQueryConfig,
  })

// delete policies
export const useDeletePolicies = () =>
  useMutation({
    mutationKey: ['deletePolicies'],
    mutationFn: async (policy_ids: number[]) => {
      return await policiesApi.deletePolicies({
        PolicyIds: {
          ids: policy_ids,
        },
      })
    },
    ...defaultQueryConfig,
  })

// update policy
export const useUpdatePolicy = () =>
  useMutation({
    mutationKey: ['updatePolicy'],
    mutationFn: async (policy: UpdatePolicyRequest) => {
      return await policiesApi.updatePolicy(policy)
    },
    ...defaultQueryConfig,
  })
