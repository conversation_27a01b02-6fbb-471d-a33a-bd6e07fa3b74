import { toast } from 'sonner'
import { notificationsApi } from './clients'
import { useMutation, useQuery } from '@tanstack/react-query'
import type {
  AddNotificationRequest,
  DeleteNotificationRequest,
  UpdateNotificationRequest,
} from 'prime-front-service-client'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const useGetNotifications = (refetchNotificationsCount: number) =>
  useQuery({
    queryKey: ['getNotifications', refetchNotificationsCount],
    queryFn: async () => {
      try {
        return await notificationsApi.getAccountNotifications()
      } catch (error) {
        toast.error('Failed to fetch notifications')
        throw new Error('Failed to fetch notifications')
      }
    },
    ...defaultQueryConfig,
  })

export const useGetNotificationTypes = () =>
  useQuery({
    queryKey: ['getNotificationTypes'],
    queryFn: async () => {
      try {
        return await notificationsApi.getNotificationTypes()
      } catch (error) {
        toast.error('Failed to fetch notification types')
        throw new Error('Failed to fetch notification types')
      }
    },
    ...defaultQueryConfig,
  })

export const useAddNotification = () =>
  useMutation({
    mutationKey: ['addNotification'],
    mutationFn: async (notification: AddNotificationRequest) => {
      return await notificationsApi.addNotification(notification)
    },
  })

export const useUpdateNotification = () =>
  useMutation({
    mutationKey: ['updateNotification'],
    mutationFn: async (notification: UpdateNotificationRequest) => {
      return await notificationsApi.updateNotification(notification)
    },
  })

export const useDeleteNotification = () =>
  useMutation({
    mutationKey: ['deleteNotification'],
    mutationFn: async (notification: DeleteNotificationRequest) => {
      return await notificationsApi.deleteNotification(notification)
    },
  })
