import { useMutation, useQuery } from '@tanstack/react-query'
import { integrationApi } from './clients'
import { toast } from 'sonner'
import type { SlackAuthCallbackRequest } from 'prime-front-service-client/src/apis/IntegrationApi'
import type { SlackOAuthResponse } from 'prime-front-service-client'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const useIntegrationGetAuthLink = () =>
  useQuery({
    queryKey: ['integrationGetAuthLink'],
    queryFn: async () => {
      try {
        return await integrationApi.getAuthLink()
      } catch (error) {
        toast.error('Failed to fetch integrations')
        throw new Error('Failed to fetch integrations')
      }
    },
    ...defaultQueryConfig,
  })

export const useSlackAuthCallback = ({
  code,
  state,
}: SlackAuthCallbackRequest) =>
  useQuery({
    queryKey: ['slackAuthCallback'],
    queryFn: async () => {
      if (!code || !state) {
        return null
      }
      try {
        return await integrationApi.slackAuthCallback({
          code,
          state,
        })
      } catch (error) {
        toast.error('Failed to fetch integrations')
        throw new Error('Failed to fetch integrations')
      }
    },
    ...defaultQueryConfig,
  })

export const useSlackGetToken = () =>
  useQuery({
    queryKey: ['slackGetToken'],
    queryFn: async () => {
      try {
        return await integrationApi.getToken()
      } catch (error: any) {
        if (error?.response?.status === 404) {
          return null
        } else {
          toast.error('Failed to fetch integrations')
          throw new Error('Failed to fetch integrations')
        }
      }
    },
    ...defaultQueryConfig,
  })

export const useGetSlackChannels = (
  token: SlackOAuthResponse | null | undefined
) =>
  useQuery({
    queryKey: ['slackChannels', token],
    queryFn: async () => {
      if (!token) throw new Error('No token available')
      try {
        return await integrationApi.getSlackChannels()
      } catch (error) {
        toast.error('Failed to fetch integrations')
        throw new Error('Failed to fetch integrations')
      }
    },
    enabled: !!token,
    ...defaultQueryConfig,
  })

// revoke token
export const useSlackRevokeToken = () =>
  useMutation({
    mutationKey: ['slackRevokeToken'],
    mutationFn: async () => {
      try {
        return await integrationApi.revokeToken()
      } catch (error) {
        toast.error('Failed to revoke token')
        throw new Error('Failed to revoke token')
      }
    },
  })

// post: refresh channels
export const useSlackRefreshChannels = () =>
  useMutation({
    mutationKey: ['slackRefreshChannels'],
    mutationFn: async () => {
      return await integrationApi.refreshSlackChannels({
        force: true,
      })
    },
  })
