import { useMutation, useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'

import { configApi } from './clients'
import type { UpdateRequest } from 'prime-front-service-client/src/apis/ConfigApi'
import type { AccountSettingsUpdate } from 'prime-front-service-client/src/models/AccountSettingsUpdate'
import type { QueryView, ViewType } from 'prime-front-service-client'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const useGetConfig = () =>
  useQuery({
    queryKey: ['getConfigAccount'],
    queryFn: async () => {
      try {
        return await configApi.get({})
      } catch (error) {
        toast.error('Failed to fetch config')
        throw new Error('Failed to fetch config')
      }
    },
    ...defaultQueryConfig,
  })

export const useGetJiraFields = (source_id?: number) =>
  useQuery({
    queryKey: ['getJiraFields'],
    queryFn: async () => {
      try {
        if (source_id) {
          return await configApi.getJiraFields({
            source_id,
          })
        }
      } catch (error) {
        toast.error('Failed to fetch jira fields')
        throw new Error('Failed to fetch jira fields')
      }
    },
    enabled: source_id !== undefined,
    ...defaultQueryConfig,
  })

export const useUpdateConfig = () =>
  useMutation({
    mutationKey: ['updateConfig'],
    mutationFn: async (securityConfigUpdate: UpdateRequest) => {
      return await configApi.update(securityConfigUpdate)
    },
  })

export const useGetFilterPresets = (view_type: ViewType = 'cases') =>
  useQuery({
    queryKey: ['getFilterPresets', view_type],
    queryFn: async () => {
      try {
        return await configApi.queryViews({
          view_type,
        })
      } catch (error) {
        toast.error('Failed to fetch filter presets')
        throw new Error('Failed to fetch filter presets')
      }
    },
    ...defaultQueryConfig,
  })

export const useAddFilterPresets = () =>
  useMutation({
    mutationKey: ['addFilterPresets'],
    mutationFn: async (params: {
      queryView: QueryView
      view_type?: ViewType
    }) => {
      return await configApi.addQueryView({
        QueryView: params.queryView,
        view_type: params.view_type || 'cases',
      })
    },
  })

export const useDeleteCasesView = () =>
  useMutation({
    mutationKey: ['deleteFilterPresets'],
    mutationFn: async (params: { query_id: string; view_type?: ViewType }) => {
      return await configApi.deleteQueryView({
        query_id: params.query_id,
        view_type: params.view_type || 'cases',
      })
    },
  })

export const useAddOrUpdateQueryCasesView = () =>
  useMutation({
    mutationKey: ['updateFilterPresets'],
    mutationFn: async (params: {
      queryView: QueryView
      view_type?: ViewType
    }) => {
      return await configApi.addOrUpdateQueryView({
        QueryView: params.queryView,
        query_id: params.queryView.query_id,
        view_type: params.view_type || 'cases',
      })
    },
  })

export const useGetDashboardConfig = () =>
  useQuery({
    queryKey: ['getDashboardConfig'],
    queryFn: async () => {
      try {
        return await configApi.dashboardConfig()
      } catch (error) {
        toast.error('Failed to fetch dashboard config')
        throw new Error('Failed to fetch dashboard config')
      }
    },
    ...defaultQueryConfig,
  })

export const useAddQueryCasesViewToDashboard = () =>
  useMutation({
    mutationKey: ['addQueryCasesViewToDashboard'],
    mutationFn: async (params: { query_id: string; view_type?: ViewType }) => {
      return await configApi.addQueryViewToDashboard({
        query_id: params.query_id,
        view_type: params.view_type || 'cases',
      })
    },
  })

export const useDeleteQueryCasesViewFromDashboard = () =>
  useMutation({
    mutationKey: ['deleteQueryCasesViewFromDashboard'],
    mutationFn: async (query_id: string) => {
      return await configApi.deleteQueryViewFromDashboard({
        query_id,
      })
    },
  })

export const useGetWorkroomFields = (is_container_view?: boolean | null) =>
  useQuery({
    queryKey: ['getWorkroomFields'],
    queryFn: async () => {
      try {
        return await configApi.getWorkroomFields({
          ...(is_container_view ? { is_container_view } : {}),
        })
      } catch (error) {
        toast.error('Failed to fetch workroom fields')
        throw new Error('Failed to fetch workroom fields')
      }
    },
    ...defaultQueryConfig,
  })

export const useSetLoginSettings = () =>
  useMutation({
    mutationKey: ['setLoginSettings'],
    mutationFn: async (AccountSettingsUpdate: AccountSettingsUpdate) => {
      return await configApi.setLoginSettings({ AccountSettingsUpdate })
    },
  })

export const useGetLoginSettings = () =>
  useQuery({
    queryKey: ['getLoginSettings'],
    queryFn: async () => {
      try {
        return await configApi.getLoginSettings()
      } catch (error: any) {
        if (error?.response?.status?.toString().startsWith('5')) {
          throw new Error('Failed to fetch account Login Settings')
        }
        return null
      }
    },
  })

export const useGetAccountSamlConfigurationDetails = () =>
  useQuery({
    queryKey: ['getAccountSamlConfigurationDetails'],
    queryFn: async () => {
      try {
        return (await configApi.getLoginSettings()).saml.client_details
      } catch (error) {
        throw new Error('Failed to fetch account SAML configuration')
      }
    },
    ...defaultQueryConfig,
  })
