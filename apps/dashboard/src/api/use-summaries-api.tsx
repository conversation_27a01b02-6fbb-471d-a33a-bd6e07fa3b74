import { useQuery } from '@tanstack/react-query'
import { summariesApi } from './clients'
import { toast } from 'sonner'
import type { GetSummaryForIssueRequest } from 'prime-front-service-client'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const useGetSummaryForIssue = (
  params: GetSummaryForIssueRequest,
  enabled = false
) => {
  return useQuery({
    queryKey: ['getSummaryForIssue', params.source_id, params.issue_id],
    queryFn: async () => {
      try {
        return await summariesApi.getSummaryForIssue(params)
      } catch (error) {
        toast.error('Failed to fetch summary')
        throw new Error('Failed to fetch summary')
      }
    },
    ...defaultQueryConfig,
    enabled,
  })
}
