import { useMutation, useQuery } from '@tanstack/react-query'
import { designDocsApi } from './clients'
import { toast } from 'sonner'
import type {
  DesignDocType,
  AttachContextToDesignDocReq,
} from 'prime-front-service-client'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const errorsMap = {
  generateAlreadyRunning: "JobFlowAlreadyRunningError('General error')",
  uploadLinkAlreadyRunning: 'Job already exists',
}

export const useDesignDocs = (doc_source_type?: DesignDocType) =>
  useQuery({
    queryKey: ['designDocs', doc_source_type],
    queryFn: async () => {
      try {
        return await designDocsApi.getDesignDocs({
          doc_source_type,
        })
      } catch (error) {
        toast.error('Failed to fetch design docs')
        throw new Error('Failed to fetch design docs')
      }
    },
    ...defaultQueryConfig,
  })

export const useDesignDocById = (id: number) =>
  useQuery({
    queryKey: ['designDocById', id],
    queryFn: async () => {
      try {
        return await designDocsApi.getDesignDocById({
          doc_id: id,
        })
      } catch (error) {
        toast.error('Failed to fetch design doc')
        throw new Error('Failed to fetch design doc')
      }
    },
    ...defaultQueryConfig,
  })

export const useStartSecurityReviewContainer = () =>
  useMutation({
    mutationKey: ['startSecurityReviewContainer'],
    mutationFn: async ({
      source_id,
      container_issue_id,
    }: {
      source_id: number
      container_issue_id: string
    }) => {
      return await designDocsApi.startSecurityReviewContainer({
        source_id,
        container_issue_id,
      })
    },
  })

export const useReprocessDesignDoc = () =>
  useMutation({
    mutationKey: ['reprocessDesignDoc'],
    mutationFn: async ({ doc_id }: { doc_id: number }) => {
      return await designDocsApi.reprocessDesignDoc({
        doc_id,
      })
    },
  })

export const useUploadDesignDocs = () =>
  useMutation({
    mutationKey: ['uploadDesignDocs'],
    mutationFn: async ({
      files,
      process_as_one,
    }: {
      files: Blob[]
      process_as_one?: boolean
    }) => {
      return await designDocsApi.uploadNewDesignDocs({
        design_doc_file: files,
        process_as_one,
      })
    },
    ...defaultQueryConfig,
  })

export const useUpdateDesignDoc = () =>
  useMutation({
    mutationKey: ['updateDesignDoc'],
    mutationFn: async ({
      doc_id,
      design_doc_file,
    }: {
      doc_id: number
      design_doc_file: Blob
    }) => {
      return await designDocsApi.updateDesignDoc({
        doc_id,
        design_doc_file,
      })
    },
    ...defaultQueryConfig,
  })

export const useStartSecurityReviewForConfluencePage = () =>
  useMutation({
    mutationKey: ['startSecurityReviewForConfluencePage'],
    mutationFn: async (confluence_url: string) => {
      return await designDocsApi.startSecurityReviewForConfluencePage({
        BodyStartSecurityReviewForConfluencePage: {
          confluence_url,
        },
      })
    },
  })

export const useStartSecurityReviewForGooglePage = () =>
  useMutation({
    mutationKey: ['startSecurityReviewForGooglePage'],
    mutationFn: async (google_url: string) => {
      return await designDocsApi.startSecurityReviewForGooglePage({
        BodyStartSecurityReviewForGooglePage: {
          google_url,
        },
      })
    },
  })

//startSecurityReviewForUlrs
export const useStartSecurityReviewForUrls = () =>
  useMutation({
    mutationKey: ['startSecurityReviewForUrls'],
    mutationFn: async ({
      urls,
      process_as_one,
    }: {
      urls: string[]
      process_as_one?: boolean
    }) => {
      return await designDocsApi.startSecurityReviewForUlrs({
        DesignDocByMultipleUrlsReq: {
          urls,
          process_as_one: process_as_one ?? false,
        },
      })
    },
  })

export const useAttachContextToDesignDoc = () =>
  useMutation({
    mutationKey: ['attachContextToDesignDoc'],
    mutationFn: async ({
      doc_id,
      AttachContextToDesignDocReq,
    }: {
      doc_id: number
      AttachContextToDesignDocReq: AttachContextToDesignDocReq
    }) => {
      return await designDocsApi.attachContextToDesignDoc({
        doc_id,
        AttachContextToDesignDocReq,
      })
    },
  })

export const useGetSecurityReviewByCaseId = (case_id: number) =>
  useQuery({
    queryKey: ['getSecurityReviewByCaseId', case_id],
    queryFn: async () => {
      return await designDocsApi.getSecurityReviewByCaseId({
        case_id,
      })
    },
    enabled: !!case_id,
    ...defaultQueryConfig,
  })
