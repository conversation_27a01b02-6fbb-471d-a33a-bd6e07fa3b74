import { useMutation, useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import { usersApi } from './clients'
import type { UserInviteRequest } from 'prime-front-service-client/src/models'
import type {
  DeleteUserRequest,
  ResetUserPasswordRequest,
} from 'prime-front-service-client'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const useGetUsers = () =>
  useQuery({
    queryKey: ['getUsers'],
    queryFn: async () => {
      try {
        return await usersApi.getUsers()
      } catch (error) {
        toast.error('Failed to fetch users')
        throw new Error('Failed to fetch users')
      }
    },
    ...defaultQueryConfig,
  })

export const useInviteUser = () =>
  useMutation({
    mutationKey: ['inviteUser'],
    mutationFn: async (userInviteRequest: UserInviteRequest) => {
      return await usersApi.inviteUser({ UserInviteRequest: userInviteRequest })
    },
  })

export const useDeleteUser = () =>
  useMutation({
    mutationKey: ['deleteUser'],
    mutationFn: async (deleteInviteRequest: DeleteUserRequest) => {
      return await usersApi.deleteUser(deleteInviteRequest)
    },
  })

export const useResetUserPassword = () =>
  useMutation({
    mutationKey: ['resetUserPassword'],
    mutationFn: async (resetUserPasswordRequest: ResetUserPasswordRequest) => {
      return await usersApi.resetUserPassword(resetUserPasswordRequest)
    },
  })
