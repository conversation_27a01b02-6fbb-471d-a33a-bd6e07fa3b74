/* eslint-disable max-lines */
import { useMutation, useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import { casesAPI, labelsApi } from './clients'
import type { CaseStatus } from 'prime-front-service-client/src/models'
import type {
  BulkUpdateCasesRequest,
  ImplementationStatusUpdate,
  RiskScoreCategory,
} from 'prime-front-service-client'
import { atom, useAtomValue } from 'jotai'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const refetchCasesAtom = atom(0)

export const refetchLabelsAtom = atom(0)

export const useGetCasesForAccount = (params: {
  limit?: number
  offset?: number
  filters?: string[]
  sort?: string[]
  queryPresetId?: string
}) =>
  useQuery({
    queryKey: [
      'getCasesForAccount',
      params.limit,
      params.offset,
      params.filters,
      params.sort,
      params.queryPresetId,
    ],
    queryFn: async () => {
      try {
        return await casesAPI.getCasesForAccount({
          limit: params.limit,
          offset: params.offset,
          f: params.filters,
          s: params.sort,
          query_preset_id: params.queryPresetId,
        })
      } catch (error) {
        toast.error('Failed to fetch cases')
        throw new Error('Failed to fetch cases')
      }
    },
    ...defaultQueryConfig,
  })

export const useGetCasesForSource = (
  source_id: number,
  params: { limit?: number; offset?: number }
) =>
  useQuery({
    queryKey: ['getCasesForSource', source_id, params],
    queryFn: async () => {
      try {
        return await casesAPI.getCasesForAccountAndSource({
          source_id,
          limit: params.limit,
          offset: params.offset,
        })
      } catch (error) {
        toast.error('Failed to fetch cases for source')
        throw new Error('Failed to fetch cases for source')
      }
    },
    ...defaultQueryConfig,
  })

// Get a single case by ID
export const useGetCase = (source_id: number, issue_id: string) =>
  useQuery({
    queryKey: ['getCaseById', source_id, issue_id],
    queryFn: async () => {
      try {
        return await casesAPI.getCase({
          source_id,
          issue_id,
        })
      } catch (error) {
        toast.error('Failed to fetch case details')
        throw new Error('Failed to fetch case details')
      }
    },
    ...defaultQueryConfig,
    refetchInterval: 10000,
  })

// Add a comment to a case
export const useAddCommentToCase = () =>
  useMutation({
    mutationKey: ['addCommentToCase'],
    mutationFn: async ({
      source_id,
      issue_id,
      comment,
    }: {
      source_id: number
      issue_id: string
      comment: string
    }) => {
      return await casesAPI.addComment({
        source_id,
        issue_id,
        comment,
      })
    },
  })

// Update recommendations for a case
export const useUpdateRecommendation = () =>
  useMutation({
    mutationKey: ['updateRecommendation'],
    mutationFn: async ({
      source_id,
      issue_id,
      recommendationStatusUpdate,
    }: {
      source_id: number
      issue_id: string
      recommendationStatusUpdate: Array<ImplementationStatusUpdate>
    }) => {
      return await casesAPI.updateRecommendation({
        source_id,
        issue_id,
        ImplementationStatusUpdate: recommendationStatusUpdate,
      })
    },
  })

// Update the status of a case
export const useUpdateCaseStatus = () =>
  useMutation({
    mutationKey: ['updateCaseStatus'],
    mutationFn: async ({
      source_id,
      issue_id,
      status,
      dismissed_reason,
    }: {
      source_id: number
      issue_id: string
      status: CaseStatus
      dismissed_reason?: string
    }) => {
      return await casesAPI.updateStatus({
        source_id,
        issue_id,
        status,
        dismissed_reason,
      })
    },
  })

export const useWriteBack = () =>
  useMutation({
    mutationKey: ['writeBack'],
    mutationFn: async ({
      source_id,
      issue_id,
    }: {
      source_id: number
      issue_id: string
    }) => {
      return await casesAPI.writeBack({
        source_id,
        issue_id,
      })
    },
  })

export const useUpdateRiskScoreCategory = () =>
  useMutation({
    mutationKey: ['updateRiskScoreCategory'],
    mutationFn: async ({
      source_id,
      issue_id,
      risk_score_category,
    }: {
      source_id: number
      issue_id: string
      risk_score_category: RiskScoreCategory
    }) => {
      return await casesAPI.updateRiskScoreCategory({
        source_id,
        issue_id,
        risk_score_category,
      })
    },
  })

export const useGetCasesSearchAutocomplete = (params: {
  field: string
  value: string
}) =>
  useQuery({
    queryKey: ['getCasesSearchAutocomplete', params.value, params.field],
    queryFn: async () => {
      try {
        return await casesAPI.autocomplete({
          field: params.field,
          value: params.value,
        })
      } catch (error) {
        toast.error('Failed to fetch cases search autocomplete')
        throw new Error('Failed to fetch cases search autocomplete')
      }
    },
    ...defaultQueryConfig,
    enabled: false,
  })

export const useGetLabels = () => {
  const refetchLabels = useAtomValue(refetchLabelsAtom)
  return useQuery({
    queryKey: ['getLabels', refetchLabels],
    queryFn: async () => {
      try {
        return await labelsApi.getAccountLabels()
      } catch (error) {
        toast.error('Failed to fetch labels')
        throw new Error('Failed to fetch labels')
      }
    },
    ...defaultQueryConfig,
  })
}
export const useAddLabel = () =>
  useMutation({
    mutationKey: ['addLabel'],
    mutationFn: async ({
      source_id,
      issue_id,
      labels,
    }: {
      source_id: number
      issue_id: string
      labels: string[]
    }) => {
      return await casesAPI.setLabels({
        source_id,
        issue_id,
        Labels: {
          labels,
        },
      })
    },
  })

export const useBulkUpdateCases = () =>
  useMutation({
    mutationKey: [''],
    mutationFn: async ({
      source_id,
      bulkUpdateCasesRequest,
    }: {
      source_id: number
      bulkUpdateCasesRequest: BulkUpdateCasesRequest
    }) => {
      return await casesAPI.bulkUpdateCases({
        source_id,
        BulkUpdateCasesRequest: bulkUpdateCasesRequest,
      })
    },
  })

export const useAddWatcher = () =>
  useMutation({
    mutationKey: ['addWatcher'],
    mutationFn: async ({
      source_id,
      issue_id,
    }: {
      source_id: number
      issue_id: string
    }) => {
      return await casesAPI.addWatcher({ issue_id, source_id })
    },
  })

export const useGenerateRecommendationsForConcernIds = () =>
  useMutation({
    mutationKey: ['generateRecommendationsForConcernIds'],
    mutationFn: async ({
      source_id,
      issue_id,
      concern_ids,
    }: {
      source_id: number
      issue_id: string
      concern_ids: number[]
    }) => {
      return await casesAPI.generateRecommendationsForConcernIds({
        source_id,
        issue_id,
        GenerateRecommendationsForConcernIdsRequest: {
          concern_ids,
        },
      })
    },
  })

// Search
export const useSearchCases = (params: { value: string; limit?: number }) =>
  useQuery({
    queryKey: ['searchCases', params.value, params.limit],
    queryFn: async () => {
      try {
        return await casesAPI.autocompleteSearchGlobalCases({
          value: params.value,
          limit: params.limit ?? 10,
        })
      } catch (error) {
        toast.error('Failed to search cases')
        throw new Error('Failed to search cases')
      }
    },
    ...defaultQueryConfig,
    enabled: false,
  })
