import { useMutation, useQuery } from '@tanstack/react-query'
import { mitigationsApi } from './clients'
import { toast } from 'sonner'
import type { CreateMitigationsOperationRequest } from 'prime-front-service-client'
import { apiConfig } from '@libs/common'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

// get all mitigations
export const useGetMitigations = (security_review_id: number) =>
  useQuery({
    queryKey: ['getMitigations', security_review_id],
    queryFn: async () => {
      if (!security_review_id) {
        console.log('No security review id')
        return []
      }
      try {
        return await mitigationsApi.getMitigations({
          security_review_id,
        })
      } catch (error) {
        toast.error('Failed to fetch mitigations')
        throw new Error('Failed to fetch mitigations')
      }
    },
    ...defaultQueryConfig,
  })

// create mitigations
export const useCreateMitigation = () =>
  useMutation({
    mutationKey: ['createMitigation'],
    mutationFn: async (mitigation: CreateMitigationsOperationRequest) => {
      const response = await fetch(
        `${apiConfig.basePath}/security-reviews/${mitigation.security_review_id}/mitigations`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(mitigation.CreateMitigationsRequest),
          credentials: 'include',
        }
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return response
    },
  })

// export all mitigations "/security-reviews/{security_review_id}/mitigations/export"
export const useExportMitigations = () =>
  useMutation({
    mutationKey: ['exportMitigations'],
    mutationFn: async (params: {
      security_review_id: number
      should_export_to_csv?: boolean
      should_export_to_pdf?: boolean
    }) => {
      const {
        security_review_id,
        should_export_to_csv = true,
        should_export_to_pdf = true,
      } = params

      const response = await fetch(
        `${apiConfig.basePath}/security-reviews/${security_review_id}/mitigations/export`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            should_export_to_csv,
            should_export_to_pdf,
          }),
        }
      )

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(
          `Failed to export mitigations: ${response.status} ${errorText}`
        )
      }

      // Handle file download
      const blob = await response.blob()
      const contentDisposition = response.headers.get('content-disposition')
      const filename = contentDisposition
        ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
        : `mitigations-export-${security_review_id}.${
            should_export_to_pdf ? 'pdf' : 'csv'
          }`

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)

      return { success: true, filename }
    },
  })
