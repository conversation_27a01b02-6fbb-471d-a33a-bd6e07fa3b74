import { useQuery } from '@tanstack/react-query'
import { healthApi } from './clients'
import { toast } from 'sonner'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const useIsServiceAlive = () =>
  useQuery({
    queryKey: ['isServicesAlive'],
    queryFn: async () => {
      try {
        return await healthApi.isServicesAlive()
      } catch (error) {
        toast.error('Failed to fetch isServicesAlive')
        throw new Error('Failed to fetch isServicesAlive')
      }
    },
    ...defaultQueryConfig,
  })

export const useIsAlive = () =>
  useQuery({
    queryKey: ['isIsAlive'],
    queryFn: async () => {
      try {
        return await healthApi.isAlive()
      } catch (error) {
        toast.error('Failed to fetch isAlive')
        throw new Error('Failed to fetch isAlive')
      }
    },
    ...defaultQueryConfig,
  })
