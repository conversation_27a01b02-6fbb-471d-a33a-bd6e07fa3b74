import { classificationsApi } from './clients'
import { useMutation, useQuery } from '@tanstack/react-query'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const useGenAiOrchestrate = () =>
  useMutation({
    mutationKey: ['genAiOrchestrate'],
    mutationFn: async (source_id: number) => {
      return await classificationsApi.classification({
        source_id,
      })
    },
  })

export const useGenAiStep1 = () =>
  useMutation({
    mutationKey: ['genAiStep1'],
    mutationFn: async (source_id: number) => {
      return await classificationsApi.classification({ source_id })
    },
  })

export const useGetCasesByStatus = (
  dateRange: {
    startDate: Date
    endDate: Date
  },
  chartKey: string
) =>
  useQuery({
    queryKey: ['getCasesByStatus', chartKey],
    queryFn: async () => {
      return await classificationsApi.getCasesByStatus({
        start: dateRange.startDate,
        end: dateRange.endDate,
      })
    },
    ...defaultQueryConfig,
  })

export const useGetCasesByMitre = (query_id?: string) =>
  useQuery({
    queryKey: ['getCasesByMitre'],
    queryFn: async () => {
      return await classificationsApi.getCasesByMitre(
        query_id
          ? {
              query_id,
            }
          : {}
      )
    },
    ...defaultQueryConfig,
  })

export const useGetCasesByLindunn = (query_id?: string) =>
  useQuery({
    queryKey: ['getCasesByLinddun'],
    queryFn: async () => {
      return await classificationsApi.getCasesByLinddun(
        query_id
          ? {
              query_id,
            }
          : {}
      )
    },
    ...defaultQueryConfig,
  })
