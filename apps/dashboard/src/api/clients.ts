import type {
  <PERSON>rror<PERSON>ontext,
  RequestContext,
  ResponseContext,
} from 'prime-front-service-client'
import {
  AuthApi,
  CasesApi,
  ClassificationsApi,
  ConfigApi,
  Configuration,
  HealthApi,
  SourcesApi,
  UsersApi,
  AuditApi,
  IntegrationApi,
  LabelsApi,
  JobsApi,
  PsvApi,
  SummariesApi,
  NotificationsApi,
  DesignDocsApi,
  PoliciesApi,
  AIApi,
  MitigationsApi,
} from 'prime-front-service-client'
import { EventsApi } from 'prime-front-service-client/dist/apis/EventsApi'
import { apiConfig } from '@libs/common'
import { clearAuthCache } from '../auth/auth-utils'
import type {
  ConfigurationParameters,
  Middleware,
} from 'prime-front-service-client/src/runtime'

const authMiddleware: Middleware = {
  post: async (context: ResponseContext) => {
    if (context.response.status === 401) {
      clearAuthCache()
      window.location.href = '/login'
      return context.response
    }
  },
  onError: async (context: ErrorContext) => {
    if (context?.response?.status === 401) {
      clearAuthCache()
      window.location.href = '/login'
      return context.response
    }
  },
}

const customHeadersMiddleware: Middleware = {
  pre: async (context: RequestContext) => {
    context.init.headers = {
      ...context.init.headers,
      'x-prime-correlation-id': crypto.randomUUID(),
    }
    return context
  },
}

const commonConfig: ConfigurationParameters = {
  basePath: apiConfig.basePath,
  credentials: 'include',
  middleware: [authMiddleware, customHeadersMiddleware],
}

export const healthApi = new HealthApi(new Configuration(commonConfig))

export const sourcesApi = new SourcesApi(new Configuration(commonConfig))

export const classificationsApi = new ClassificationsApi(
  new Configuration(commonConfig)
)

export const authAPI = new AuthApi(new Configuration(commonConfig))

export const casesAPI = new CasesApi(new Configuration(commonConfig))

export const configApi = new ConfigApi(new Configuration(commonConfig))

export const usersApi = new UsersApi(new Configuration(commonConfig))

export const jobsApi = new JobsApi(new Configuration(commonConfig))

export const auditApi = new AuditApi(new Configuration(commonConfig))

export const integrationApi = new IntegrationApi(
  new Configuration(commonConfig)
)
export const labelsApi = new LabelsApi(new Configuration(commonConfig))

export const psvApi = new PsvApi(new Configuration(commonConfig))

export const summariesApi = new SummariesApi(new Configuration(commonConfig))

export const notificationsApi = new NotificationsApi(
  new Configuration(commonConfig)
)

export const designDocsApi = new DesignDocsApi(new Configuration(commonConfig))

export const eventsApi = new EventsApi(new Configuration(commonConfig))

export const policiesApi = new PoliciesApi(new Configuration(commonConfig))

export const aiApi = new AIApi(new Configuration(commonConfig))

export const mitigationsApi = new MitigationsApi(
  new Configuration(commonConfig)
)
