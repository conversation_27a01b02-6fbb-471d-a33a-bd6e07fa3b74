import { useMutation, useQuery } from '@tanstack/react-query'
import { psvApi } from './clients'
import { toast } from 'sonner'
import type {
  BulkUpdatePsvRequest,
  GetPsvRequest,
  PsvStatus,
} from 'prime-front-service-client'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const useGetPsv = (params: {
  source_id?: number
  limit?: number
  offset?: number
  f?: string[]
  s?: string[]
}) =>
  useQuery({
    queryKey: ['getPsv', params.limit, params.offset, params.f, params.s],
    queryFn: async () => {
      try {
        return await psvApi.getPsv(params as GetPsvRequest)
      } catch (error) {
        toast.error('Failed to fetch psv')
        throw new Error('Failed to fetch psv')
      }
    },
    ...defaultQueryConfig,
    staleTime: 20000,
  })

export const useUpdatePsvStatus = () =>
  useMutation({
    mutationKey: ['updatePsvStatus'],
    mutationFn: async ({
      psv_id,
      new_status,
      dismissed_reason,
    }: {
      psv_id: number
      new_status: PsvStatus
      dismissed_reason?: string
    }) => {
      return await psvApi.updatePsvStatus({
        psv_id,
        new_status,
        dismissed_reason,
      })
    },
  })

export const useBulkUpdatePsvStatus = () =>
  useMutation({
    mutationKey: ['bulkUpdatePsvStatus'],
    mutationFn: async (updateRequest: BulkUpdatePsvRequest) => {
      return await psvApi.bulkUpdatePsvStatus({
        BulkUpdatePsvRequest: updateRequest,
      })
    },
  })

export const useGetPsvExport = () =>
  useQuery({
    queryKey: ['getPsvExport'],
    queryFn: async () => {
      try {
        return await psvApi.exportPsv()
      } catch (error) {
        toast.error('Failed to fetch psv export')
        throw new Error('Failed to fetch psv export')
      }
    },
    ...defaultQueryConfig,
    enabled: false,
  })

export const usePsvCount = () =>
  useQuery({
    queryKey: ['psvCount'],
    queryFn: async () => {
      try {
        return await psvApi.psvCount()
      } catch (error) {
        toast.error('Failed to fetch security violations')
        throw new Error('Failed to fetch security violations')
      }
    },
    ...defaultQueryConfig,
  })
