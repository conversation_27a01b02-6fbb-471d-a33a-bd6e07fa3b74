import { useMutation, useQuery } from '@tanstack/react-query'
import { aiApi } from './clients'
import type {
  AddNewConversationRequest,
  AttachContentRequest,
  BackupConversationRequest,
  DeleteConversationRequest,
  GetConversationRequest,
  GetHistoryRequest,
  QueryRequest,
} from 'prime-front-service-client'
import { CHAT_SESSIONS_KEY, type ChatSession } from '../config/store'

export const useAiApiQuery = () =>
  useMutation({
    mutationKey: ['aiApiQuery'],
    mutationFn: async (params: QueryRequest) => {
      return await aiApi.query(params)
    },
  })

export const useAiApiAttachContent = () =>
  useMutation({
    mutationKey: ['aiApiAttachContent'],
    mutationFn: async (params: AttachContentRequest) => {
      return await aiApi.attachContent(params)
    },
  })

export const useAiApiDeleteConversation = () =>
  useMutation({
    mutationKey: ['aiApiDeleteConversation'],
    mutationFn: async (params: DeleteConversationRequest) => {
      return await aiApi.deleteConversation(params)
    },
  })

export const useAiApiHistory = (params: GetHistoryRequest) =>
  useQuery({
    queryKey: ['aiApiHistory', params.chat_location],
    queryFn: async () => {
      const res = await aiApi.getHistory({
        chat_location: params.chat_location,
        chat_location_identifier: params.chat_location_identifier,
      })

      return res
    },
    refetchOnWindowFocus: false,
    staleTime: 0,
    gcTime: 0,
  })

export const useAiApiGetConversation = (params: GetConversationRequest) =>
  useQuery({
    queryKey: ['aiApiGetConversation', params.conversation_id],
    queryFn: async () => {
      if (!params?.conversation_id) {
        return null
      }
      return await aiApi.getConversation({
        conversation_id: params.conversation_id,
      })
    },
    enabled: !!params.conversation_id,
    refetchOnWindowFocus: false,
    staleTime: 0,
    gcTime: 0,
  })

//addNewConversation
export const useAiApiAddNewConversation = () =>
  useMutation({
    mutationKey: ['aiApiAddNewConversation'],
    mutationFn: async (params: AddNewConversationRequest) => {
      return await aiApi.addNewConversation({
        chat_location: params.chat_location,
        chat_location_identifier: params.chat_location_identifier,
      })
    },
  })

// backupConversation
export const useAiApiBackupConversation = () =>
  useMutation({
    mutationKey: ['aiApiBackupConversation'],
    mutationFn: async (chatSessions: Record<string, ChatSession>) => {
      const keys = Object.keys(chatSessions)

      const paramsObjectArray: BackupConversationRequest[] = []
      keys.forEach((key) => {
        const chatSession = chatSessions[key]
        const chat_location_identifier =
          Number(chatSession.containerId) || Number(chatSession.docId)

        if (!chat_location_identifier) return
        if (Number.isNaN(chat_location_identifier)) return
        if (!chatSession) return
        if (!chatSession.messages || chatSession.messages.length === 0) return
        if (!chatSession.docId && !chatSession.containerId) return
        if (!chatSession.sessionId) return
        if (chatSession.messages.length === 0) return
        if (chatSession.messages[0].role !== 'user') return
        if (chatSession.sourceId) return

        paramsObjectArray.push({
          chat_location: chatSession.containerId ? 'container' : 'design_doc',
          chat_location_identifier: chat_location_identifier,
          ConversationInput: {
            conversation_id: Date.now(),
            chat_messages: chatSession.messages.map((message, index) => ({
              sender: message.role,
              timestamp: new Date(),
              extra_context_items: null,
              content: message.content,
              msg_id: index,
            })),
            created_at: new Date(),
            updated_at: new Date(),
          },
        })
      })

      const promises = paramsObjectArray.map((params) => {
        return aiApi.backupConversation(params)
      })

      const results = await Promise.allSettled(promises)
      const success = results.filter((result) => result.status === 'fulfilled')
      const failed = results.filter((result) => result.status === 'rejected')
      const successCount = success.length
      const failedCount = failed.length

      console.log(`Successfully backed up ${successCount} conversations`)
      console.log(`Failed to back up ${failedCount} conversations`)
      if (failedCount > 0) {
        console.error('Failed to back up conversations:', failed)
      } else {
        console.log('All conversations backed up successfully')
        localStorage.removeItem(CHAT_SESSIONS_KEY)
      }
    },
  })
