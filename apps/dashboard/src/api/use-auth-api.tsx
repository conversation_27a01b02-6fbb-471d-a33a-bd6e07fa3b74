import { useMutation, useQuery } from '@tanstack/react-query'
import { authAPI } from './clients'
import { toast } from 'sonner'
import type { MfaSetupRequest } from 'prime-front-service-client'
import { apiConfig } from '@libs/common'
import type {
  NewPasswordRequiredRequest,
  UserLoginRequest,
} from 'prime-front-service-client/src/models'
import type { MfaChallengeResponseRequest } from 'prime-front-service-client/src/apis/AuthApi'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const useIdentify = () =>
  useMutation({
    mutationKey: ['identify'],
    mutationFn: async (email: string) => {
      return await authAPI.identify({ email })
    },
  })

export const newPasswordRequired = async (
  newPasswordRequiredRequest: NewPasswordRequiredRequest
) => {
  const response = await fetch(`${apiConfig.basePath}/auth/new-password-req`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    redirect: 'manual',
    body: JSON.stringify({
      email: newPasswordRequiredRequest['email'],
      session: newPasswordRequiredRequest['session'],
      new_password: newPasswordRequiredRequest['new_password'],
    }),
  })
  if (!response.ok) {
    new Error(`Error: ${response.statusText}`)
  }
  if (response.status === 500) {
    throw new Error('Password reset failed')
  }
  if (response.url && response.type === 'opaqueredirect') {
    toast.success('Password reset complete, you are redirected to home page')
    window.location.replace('/')
  }
  return response
}

export const mfaChallenge = async (
  mfaChallengeResponseRequest: MfaChallengeResponseRequest
) => {
  const queryParams = new URLSearchParams(
    Object.entries(mfaChallengeResponseRequest).reduce(
      (acc: Record<string, string>, [key, value]) => {
        acc[key] = String(value)
        return acc
      },
      {}
    )
  ).toString()
  const response = await fetch(
    `${apiConfig.basePath}/auth/mfa/challenge?${queryParams}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      redirect: 'manual',
    }
  )
  if (!response.ok) {
    new Error(`Error: ${response.statusText}`)
  }
  if (response.url && response.type === 'opaqueredirect') {
    window.location.replace('/')
  }
  if (response.status === 500) {
    throw new Error('incorrectCode')
  }
  return response
}

export const mfaSetup = async (mfaSetupRequest: MfaSetupRequest) => {
  const queryParams = new URLSearchParams(
    Object.entries(mfaSetupRequest).reduce(
      (acc: Record<string, string>, [key, value]) => {
        acc[key] = String(value)
        return acc
      },
      {}
    )
  ).toString()
  const response = await fetch(
    `${apiConfig.basePath}/auth/mfa/setup?${queryParams}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      redirect: 'manual',
    }
  )
  if (!response.ok) {
    new Error(`Error: ${response.statusText}`)
  }
  if (response.status === 500) {
    throw new Error('incorrectCode')
  }
  if (response.url) {
    toast.success('User setup complete, you are redirected to login page')
    window.location.replace('/')
  }
  return response
}

export const login = async (loginRequest: UserLoginRequest) => {
  const response = await fetch(`${apiConfig.basePath}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(loginRequest),
    credentials: 'include',
    redirect: 'manual',
  })

  if (response.type === 'opaqueredirect') {
    window.location.replace('/')
  } else {
    if (response.status === 200) {
      return response
    } else if (response.status === 401 || response.status === 500) {
      throw new Error('incorrectUserOrPassword')
    }
  }
  return response
}
export const useLogout = () =>
  useMutation({
    mutationKey: ['logout'],
    mutationFn: async () => {
      return await authAPI.logout()
    },
  })

export const useGetUserInfo = () =>
  useQuery({
    queryKey: ['getUserInfo'],
    queryFn: async () => {
      try {
        return await authAPI.getUserInfo()
      } catch (error) {
        toast.error('Failed to get account details')
        throw new Error('Failed to get account details')
      }
    },
    ...defaultQueryConfig,
  })
