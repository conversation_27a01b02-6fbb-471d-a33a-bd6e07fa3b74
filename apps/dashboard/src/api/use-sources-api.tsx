import { useMutation, useQuery } from '@tanstack/react-query'
import { sourcesApi } from './clients'
import { toast } from 'sonner'
import type {
  AddJiraSourceRequest,
  UpdateJiraSourceRequest,
} from 'prime-front-service-client/src/apis/SourcesApi'
import type { GetProjectsRequest } from 'prime-front-service-client/src/apis/SourcesApi'
import type { GetJiraProjectsBySourceIdRequest } from 'prime-front-service-client'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

export const errorsMap = {
  addSourceTokenErrorType: 'E1601-InvalidTokenError',
  fetchAlreadyRunning: "JobFlowAlreadyRunningError('General error')",
}

export const useAddJiraSource = () =>
  useMutation({
    mutationKey: ['addJiraSource'],
    mutationFn: async (jiraSource: AddJiraSourceRequest) => {
      return await sourcesApi.addJiraSource(jiraSource)
    },
  })

export const useUpdateJiraSource = () =>
  useMutation({
    mutationKey: ['updateJiraSource'],
    mutationFn: async (jiraSource: UpdateJiraSourceRequest) => {
      return await sourcesApi.updateJiraSource(jiraSource)
    },
  })

export const useFetchJiraSource = () =>
  useMutation({
    mutationKey: ['fetchJiraSource'],
    mutationFn: async (source_id: number) => {
      return await sourcesApi.fetchJiraSource({
        source_id,
      })
    },
  })

export const useDeleteSource = () =>
  useMutation({
    mutationKey: ['deleteSource'],
    mutationFn: async (source_id: number) => {
      return await sourcesApi.deleteSource({
        source_id,
      })
    },
  })

export const useGetProjects = () =>
  useMutation({
    mutationKey: ['getProjects'],
    mutationFn: async (
      getProjectsRequest: GetProjectsRequest,
      check_access = true
    ) => {
      return await sourcesApi.getProjects({
        ...getProjectsRequest,
        check_access,
      })
    },
  })

export const useGetJiraProjectsBySourceId = () =>
  useMutation({
    mutationKey: ['getJiraProjectsBySourceId'],
    mutationFn: async (
      getJiraProjectsBySourceIdRequest: GetJiraProjectsBySourceIdRequest,
      check_access = true
    ) => {
      return await sourcesApi.getJiraProjectsBySourceId({
        ...getJiraProjectsBySourceIdRequest,
        check_access,
      })
    },
  })

export const useGetAllSources = (refetchCount: number) =>
  useQuery({
    queryKey: ['getAllSources', refetchCount],
    queryFn: async () => {
      try {
        return await sourcesApi.getAllSources()
      } catch (error) {
        toast.error('Failed to fetch sources')
        throw new Error('Failed to fetch sources')
      }
    },
    ...defaultQueryConfig,
  })

export const useGetSource = (source_id: number, enabled?: boolean) =>
  useQuery({
    queryKey: ['getSource', source_id],
    queryFn: async () => {
      try {
        return await sourcesApi.getSource({ source_id })
      } catch (error) {
        toast.error('Failed to fetch source')
        throw new Error('Failed to fetch source')
      }
    },
    ...defaultQueryConfig,
    enabled: !!source_id && enabled !== false,
  })

export const useBuildProviderFieldsDataJob = () =>
  useMutation({
    mutationKey: ['buildProviderFieldsDataJob'],
    mutationFn: async (source_id: number) => {
      return await sourcesApi.buildProviderFieldsDataJob({
        source_id,
      })
    },
  })

export const useAddGoogleSource = () =>
  useMutation({
    mutationKey: ['addGoogleSource'],
    mutationFn: async () => {
      return await sourcesApi.addGoogleSource()
    },
  })
