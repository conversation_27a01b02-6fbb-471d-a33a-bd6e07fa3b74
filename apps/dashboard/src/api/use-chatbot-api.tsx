import { useQuery } from '@tanstack/react-query'
import { aiApi } from './clients'
import type { LLmJobData } from 'prime-front-service-client'

export const useLlmRequestStatus = (llm_request_id: string | null) =>
  useQuery<LLmJobData | null, Error>({
    queryKey: ['llmRequestStatus', llm_request_id],
    queryFn: async () => {
      if (!llm_request_id) return null
      return await aiApi.getLlmRequestStatus({
        request_id: llm_request_id,
      })
    },
    enabled: !!llm_request_id,
    refetchInterval: (query) => {
      const data = query.state.data as LLmJobData | null

      if (query.state.error) {
        return false // Do not refetch if there's an error
      }

      // Only refetch if data exists and is not ended
      return data?.is_ended === false ? 1000 : false
    },
    // Add stale time to prevent unnecessary refetches
    staleTime: 0,

    retry: (failureCount) => {
      if (failureCount >= 1) return false
      return true
    },
  })
