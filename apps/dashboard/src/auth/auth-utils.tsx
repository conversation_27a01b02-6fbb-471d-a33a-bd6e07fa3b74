import type { LoaderFunctionArgs } from 'react-router'
import { Outlet, redirect } from 'react-router'
import { Providers } from '../components/providers'
import { Sidebar } from '../components/sidebar'
import { authAPI } from '../api/clients'
import { datadogRum } from '@datadog/browser-rum'
import type { UserInformation } from 'prime-front-service-client/src/models/UserInformation'

interface AuthCacheProps {
  isAuthenticated: boolean
  timestamp: number
}

let authCache: AuthCacheProps | null = null

const AUTH_CACHE_DURATION = 5 * 60 * 1000
const USER_INFO_KEY = 'userInfo'

const getCachedAuthStatus = (): boolean | null => {
  if (authCache && Date.now() - authCache.timestamp < AUTH_CACHE_DURATION) {
    return authCache.isAuthenticated
  }
  return null
}

export const setUserInfo = (userInfo: UserInformation) => {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
}

export const getUserInfo = (): UserInformation | null => {
  const userInfo = localStorage.getItem(USER_INFO_KEY)
  return userInfo ? JSON.parse(userInfo) : null
}

const setCachedAuthStatus = (isAuthenticated: boolean): void => {
  authCache = { isAuthenticated, timestamp: Date.now() }
}

export const clearAuthCache = () => {
  authCache = null
}

const isLoginMock = import.meta.env.VITE_USE_LOGIN_MOCK === 'true'

const isAuthenticated = async (): Promise<boolean> => {
  if (isLoginMock) {
    return localStorage.getItem('isAuthenticated') === 'true'
  }

  const cachedStatus = getCachedAuthStatus()
  if (cachedStatus !== null) {
    return cachedStatus
  }

  try {
    const res = await authAPI.getUserInfo()

    datadogRum.setUser({
      email: res.user_id,
      accountId: res.account_id,
    })

    if (res) {
      setUserInfo(res)
    }
    setCachedAuthStatus(true)
    return true
  } catch (error) {
    setCachedAuthStatus(false)
    return false
  }
}

export async function privateRouteLoader({ request }: LoaderFunctionArgs) {
  const isAuth = await isAuthenticated()
  const url = new URL(request.url)

  if (isAuth) {
    const redirectUrl = localStorage.getItem('redirect')

    if (redirectUrl) {
      localStorage.removeItem('redirect')
      return redirect(redirectUrl)
    }

    return null
  } else {
    localStorage.setItem('redirect', url.href)
    return redirect(`/login?from=${url.pathname}`)
  }
}

export async function loginRouteLoader() {
  const isAuth = await isAuthenticated()
  if (isAuth) {
    return redirect(`/`)
  }
  return null
}

export async function logoutLoader() {
  if (isLoginMock) {
    localStorage.removeItem('isAuthenticated')
  } else {
    try {
      await authAPI.logout()
    } catch (error) {
      console.error('🎈 Failed to logout', error)
    }
  }
  datadogRum.clearUser()
  localStorage.removeItem(USER_INFO_KEY)

  authCache = null
  return redirect(`/login`)
}

export const ProtectedLayout = () => {
  return (
    <Providers>
      <div className="flex min-h-screen w-full flex-col bg-slate-50">
        <Sidebar />
        <main className="routes-wrapper flex flex-col sm:gap-4 pl-[84px]">
          <Outlet />
        </main>
      </div>
    </Providers>
  )
}

export const PublicLayout = () => {
  return (
    <Providers>
      <div className="flex min-h-screen w-full flex-col bg-slate-100">
        <Outlet />
      </div>
    </Providers>
  )
}
