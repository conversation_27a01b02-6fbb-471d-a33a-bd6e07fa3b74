import { useRef, useEffect } from 'react'

/**
 * Hook to track if a component is currently unmounting
 *
 * @returns boolean indicating if the component is in the process of unmounting
 *
 * @example
 * ```tsx
 * const MyComponent = () => {
 *   const isUnmounting = useIsUnmounting()
 *
 *   useEffect(() => {
 *     const interval = setInterval(() => {
 *       if (!isUnmounting.current) {
 *         // Safe to update state
 *         setState(newValue)
 *       }
 *     }, 1000)
 *
 *     return () => clearInterval(interval)
 *   }, [isUnmounting])
 *
 *   return <div>Content</div>
 * }
 * ```
 */
export const useIsUnmounting = () => {
  const isUnmounting = useRef(false)

  useEffect(() => {
    // Reset on mount
    isUnmounting.current = false

    return () => {
      // Set to true when component starts unmounting
      isUnmounting.current = true
    }
  }, [])

  return isUnmounting
}
