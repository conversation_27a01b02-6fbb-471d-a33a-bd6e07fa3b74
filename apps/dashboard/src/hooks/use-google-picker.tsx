import { useEffect, useState, useCallback } from 'react'
import { useAtom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'

const googleAuthTokenAtom = atomWithStorage<string | null>(
  'googleAuthToken',
  null
)

interface GooglePickerOptions {
  viewId?: string
  mimeTypes?: string[]
  multiSelect?: boolean
  onSelect?: (files: GooglePickerFile[]) => void
  onCancel?: () => void
}

export interface GooglePickerFile {
  id: string
  name: string
  mimeType: string
  iconUrl: string
  url: string
  embedUrl?: string
  downloadUrl?: string
  lastEditedUtc?: number
  description?: string
  sizeBytes?: number
}

export const useGooglePicker = (options: GooglePickerOptions = {}) => {
  const [googleAuthToken, setGoogleAuthToken] = useAtom(googleAuthTokenAtom)
  const [isGapiLoaded, setIsGapiLoaded] = useState(false)
  const [isGisLoaded, setIsGisLoaded] = useState(false)
  const [isPickerLoaded, setIsPickerLoaded] = useState(false)
  const [isReady, setIsReady] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const apiKey = import.meta.env.VITE_GOOGLE_DRIVE_API_KEY as string
  const clientId = import.meta.env.VITE_GOOGLE_OAUTH__CLIENT_ID as string
  const scope = 'https://www.googleapis.com/auth/drive.readonly'

  // Memoized default options
  const {
    viewId = 'DOCS',
    mimeTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'application/vnd.google-apps.document',
    ],
    multiSelect = true,
    onSelect = (files) => console.log('Files selected:', files),
    onCancel = () => console.log('File selection cancelled'),
  } = options

  // Load Google API Library (gapi)
  useEffect(() => {
    if (window.gapi || isGapiLoaded) return

    setIsLoading(true)
    const script = document.createElement('script')
    script.src = 'https://apis.google.com/js/api.js'
    script.onload = () => {
      window.gapi.load('picker', () => {
        setIsGapiLoaded(true)
        setIsPickerLoaded(true)
        setIsLoading(false)
      })
    }
    document.body.appendChild(script)

    return () => {
      if (script.parentNode) {
        document.body.removeChild(script)
      }
    }
  }, [isGapiLoaded])

  // Load Google Identity Services (GIS)
  useEffect(() => {
    if (window.google?.accounts?.oauth2 || isGisLoaded) return

    setIsLoading(true)
    const script = document.createElement('script')
    script.src = 'https://accounts.google.com/gsi/client'
    script.onload = () => {
      setIsGisLoaded(true)
      setIsLoading(false)
    }
    script.onerror = () => {
      console.error('Failed to load Google Identity Services')
      setIsLoading(false)
    }
    document.body.appendChild(script)

    return () => {
      if (script.parentNode) {
        document.body.removeChild(script)
      }
    }
  }, [isGisLoaded])

  // Set ready state when all required libraries are loaded
  useEffect(() => {
    if (isGapiLoaded && isGisLoaded && isPickerLoaded) {
      setIsReady(true)
    }
  }, [isGapiLoaded, isGisLoaded, isPickerLoaded])

  // Handle authorization with Google Identity Services
  const authorize = useCallback(async () => {
    if (!isGisLoaded) return null

    try {
      const tokenClient = window?.google?.accounts?.oauth2.initTokenClient({
        client_id: clientId,
        scope,
        callback: (tokenResponse: any) => {
          if (tokenResponse.error) {
            console.error('Token error:', tokenResponse)
            onCancel()
            return
          }

          setGoogleAuthToken(tokenResponse.access_token)
        },
      })

      // Request token
      return new Promise<string | null>((resolve) => {
        tokenClient.requestAccessToken({
          prompt: 'consent',
          callback: (response: any) => {
            if (response.error) {
              resolve(null)
              return
            }
            resolve(response.access_token)
          },
        })
      })
    } catch (error) {
      console.error('Google authorization failed:', error)
      onCancel()
      return null
    }
  }, [clientId, isGisLoaded, onCancel, setGoogleAuthToken, scope])

  // Create and show Google Picker
  const openPicker = useCallback(async () => {
    if (!isReady) return

    setIsLoading(true)
    let token = googleAuthToken

    if (!token) {
      token = await authorize()
      if (!token) {
        setIsLoading(false)
        return
      }
    }

    // Configure view
    const view = new window.google.picker.View(
      window.google.picker.ViewId[viewId]
    )
    if (mimeTypes.length) {
      view.setMimeTypes(mimeTypes.join(','))
    }

    // Create and render Picker
    const picker = new window.google.picker.PickerBuilder()
      .addView(view)
      .enableFeature(window.google.picker.Feature.NAV_HIDDEN)
      .enableFeature(
        window.google.picker.Feature.MULTISELECT_ENABLED,
        multiSelect
      )
      .setOAuthToken(token)
      .setDeveloperKey(apiKey)
      .setCallback((data: any) => {
        if (data.action === window.google.picker.Action.PICKED) {
          const files: GooglePickerFile[] = data.docs.map((doc: any) => ({
            id: doc.id,
            name: doc.name,
            mimeType: doc.mimeType,
            iconUrl: doc.iconUrl,
            url: doc.url,
            embedUrl: doc.embedUrl,
            downloadUrl: doc.downloadUrl,
            lastEditedUtc: doc.lastEditedUtc,
            description: doc.description,
            sizeBytes: doc.sizeBytes,
          }))
          onSelect(files)
        } else if (data.action === window.google.picker.Action.CANCEL) {
          onCancel()
        }
        setIsLoading(false)
      })
      .build()

    picker.setVisible(true)
  }, [
    apiKey,
    authorize,
    googleAuthToken,
    isReady,
    mimeTypes,
    multiSelect,
    onCancel,
    onSelect,
    viewId,
  ])

  return {
    openPicker,
    isReady,
    isLoading,
    isAuthenticated: !!googleAuthToken,
    authorize,
  }
}

// Add types for global window object
declare global {
  interface Window {
    gapi: any
    google: {
      picker: {
        Action: {
          PICKED: string
          CANCEL: string
        }
        Feature: {
          NAV_HIDDEN: string
          MULTISELECT_ENABLED: string
        }
        PickerBuilder: any
        View: any
        ViewId: Record<string, string>
      }
      accounts?: {
        oauth2: {
          initTokenClient: (config: any) => any
          TokenResponse: any
        }
      }
    }
  }
}
