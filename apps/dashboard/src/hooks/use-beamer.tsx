import { useEffect } from 'react'
import { useFlagsWrapper } from './use-flags-wrapper'

declare global {
  interface Window {
    Beamer?: {
      init: (productId: string) => void
      destroy: () => void
      hide: () => void
      show: () => void
      toggle: () => void
    }
    beamer_config?: {
      product_id: string
      selector?: string
    }
  }
}

const beamerProductId = import.meta.env?.['VITE_BEAMER_PRODUCT_ID']

export const useBeamer = () => {
  const { showBeamer } = useFlagsWrapper()

  const isLoginPage = window.location.pathname.includes('/login') // Adjust path as needed

  useEffect(() => {
    const initBeamer = async () => {
      if (showBeamer) {
        // check if login page
        if (isLoginPage) {
          window.Beamer?.destroy()
          return
        }
        // Create and inject the Beamer config script
        const configScript = document.createElement('script')
        configScript.text = `
          var beamer_config = {
            product_id: '${beamerProductId}',
            selector: '#beamer-button'
          };
        `
        document.head.appendChild(configScript)

        // Create and inject the Beamer embed script
        const embedScript = document.createElement('script')
        embedScript.src = 'https://app.getbeamer.com/js/beamer-embed.js'
        embedScript.defer = true
        document.head.appendChild(embedScript)

        // Cleanup function
        return () => {
          document.head.removeChild(configScript)
          document.head.removeChild(embedScript)
        }
      } else {
        // remove Beamer script if the flag is off
        const beamerScript = document.querySelector(
          'script[src*="beamer-embed"]'
        )
        if (beamerScript) {
          window.location.reload()
        }
      }
    }

    initBeamer()
  }, [showBeamer])
}

// Component to use Beamer
export const BeamerIntegration = () => {
  useBeamer()
  return null
}
