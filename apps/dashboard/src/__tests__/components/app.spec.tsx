import { render, screen } from '@testing-library/react'

import { LOGIN_COOKIE_KEY } from '@libs/common'
import { describe, expect } from 'vitest'
import { createMemoryRouter, RouterProvider } from 'react-router'
import { Providers } from '../../components/providers'
import { LoginPage } from '../../pages'
import { App } from '../../components/app'

// A workaround to solve matchMedia issue in tests
// Error: Uncaught [TypeError: window.matchMedia is not a function]
global.matchMedia =
  global.matchMedia ||
  function (query) {
    return {
      matches: false,
      media: query,
      onchange: null,
      addEventListener: vitest.fn(),
      removeEventListener: vitest.fn(),
      dispatchEvent: vitest.fn(),
    }
  }

beforeEach(() => {
  global.fetch = ((originalFetch) => (url, options) => {
    const modifiedOptions = {
      ...options,
      headers: {
        ...options?.headers,
        Cookie: `${LOGIN_COOKIE_KEY}="p18Iq+qwdrTzgAx8MF7vEtJkipjkCSMvSIAwSCyzV9l8shhg0CnCLnxS1GH5bwrlaSaIAAjEwnDzhPUqpBCZMcm6E6tmsezCiLBIcGSRCA8BqqWmXLLCAd+7AZwF3NEwyEkMFjw5cEoahE05OXZOl719UqA1ZTilsAILT7OMJaoL4GtUeVNxze/VlUgy9pRhsq4LfsxuCoG6xyIsv3/iTxgtFvqAVAhwDbSOUnrT1UJpUN3nRQ7OzFoGyZFYo2Pp9cbou56Qw+pDdX9RncWNhTKsVmlEDN8gkpAIdhyJKy1kOXbQu+KqR5hDbqbalB9uzNnHzpQT/FVnxvSQuuObM+HukaFlOpShesrTOEOOUdUCjZ387pGnu0XEqqMhyigJ4WjBbP780MVfZMRydfOAGgLki7CGfrbVrv4AKvDwhEhmZ+GOaO27u25zGsO6eS7e/QORIv20U45E5yueVMYWd/wEJN9tcTFWUXXX7xBg+x2SN0s3YZiBz7OHS2Bxo+QGremqKmBFGlso0r1phOxeXH06xnkdaLtU3gVMvFGjUjEW7mPu778XzQaglgVb4Gx5NF0xQuikF1Hrgy4BMwv4knTUi5yUGz6A24IlzMPzZlB+x4W2TJS6N3MNcuTLTX2xR7lMysblVzAd6VtUY5vIYV6F1i4vQtNjLcxw/hGD2vk="`,
      },
    }
    return originalFetch(url, modifiedOptions)
  })(fetch)
})

describe('Login page test', () => {
  it('should render successfully', async () => {
    const { baseElement } = render(<App />)
    expect(baseElement).toBeTruthy()
  })

  it('renders correctly', () => {
    const router = createMemoryRouter([
      { path: '/', element: <LoginPage /> },
      { path: '/login', element: <LoginPage /> },
    ])

    render(<RouterProvider router={router} />, {
      wrapper: Providers,
    })

    screen.debug()
    expect(screen.getByText(/Login/i)).toBeDefined()
  })
})
