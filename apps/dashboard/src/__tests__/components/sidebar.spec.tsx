import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render, fireEvent, screen } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import { Sidebar } from '../../components/sidebar'
import { Providers } from '../../components/providers'

describe('Sidebar Component', () => {
  it('renders correctly', () => {
    render(
      <MemoryRouter>
        <Sidebar />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    expect(screen.getByText('Home')).toBeInTheDocument()
    expect(screen.getByText('Workroom')).toBeInTheDocument()
    expect(screen.getByText('Settings')).toBeInTheDocument()
    expect(screen.getByText('Logout')).toBeInTheDocument()
  })

  it('navigates to home when home link is clicked', () => {
    const { getByText } = render(
      <MemoryRouter initialEntries={['/']}>
        <Sidebar />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    fireEvent.click(getByText('Home'))
  })

  it('shows tooltip on hover', async () => {
    render(
      <MemoryRouter>
        <Sidebar />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    fireEvent.mouseOver(screen.getByText('Home'))
    expect(await screen.findByText('Home')).toBeVisible()
  })

  it('logout process works correctly', async () => {
    const { getByText } = render(
      <MemoryRouter>
        <Sidebar />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    fireEvent.click(getByText('Logout'))
    fireEvent.click(getByText('Continue'))
  })
})
