import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { BrowserRouter as Router } from 'react-router'
import React from 'react'
import { ErrorBoundary } from '../../components/error-boundary'

describe('ErrorBoundary Component', () => {
  it('renders the error message and icon correctly', () => {
    render(
      <Router>
        <ErrorBoundary />
      </Router>
    )

    expect(screen.getByText('Something went wrong')).toBeInTheDocument()
    expect(
      screen.getByText(
        'An unexpected error has occurred. Please try again later.'
      )
    ).toBeInTheDocument()

    const svgElement = document.querySelector('svg.lucide-triangle-alert')
    expect(svgElement).toBeInTheDocument()
  })

  it('renders the Go to Home button correctly', () => {
    render(
      <Router>
        <ErrorBoundary />
      </Router>
    )

    const button = screen.getByRole('button', { name: /go to home/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Go to Home')
  })
})
