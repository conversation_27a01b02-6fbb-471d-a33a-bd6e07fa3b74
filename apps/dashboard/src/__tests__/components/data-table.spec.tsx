import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { DataTable } from '../../pages/workroom/components/data-table'
import { Providers } from '../../components/providers'
import { MemoryRouter } from 'react-router'

describe.skip('DataTable component', () => {
  it('renders without crashing', () => {
    render(
      <MemoryRouter>
        <DataTable />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
  })

  it('renders loading state when isLoading prop is true', () => {
    const { getByTestId } = render(
      <MemoryRouter>
        <DataTable />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByTestId('loading-spinner')).toBeInTheDocument()
  })
})
