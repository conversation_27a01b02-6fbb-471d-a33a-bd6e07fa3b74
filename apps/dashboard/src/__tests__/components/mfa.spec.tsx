import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { vi } from 'vitest'
import { Mfa } from '../../pages/login/components/mfa'
import { MemoryRouter } from 'react-router'
import { Providers } from '../../components/providers'
import type { AuthLoginResponse } from 'prime-front-service-client'

/* eslint-disable @typescript-eslint/no-empty-function */
class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

vi.mock('qrcode', async (importOriginal) => {
  const actual: object = await importOriginal()
  return {
    ...actual,
    default: {
      toCanvas: vi.fn(),
    },
  }
})

describe('Mfa Component', () => {
  const mfaDetails: AuthLoginResponse = {
    shared_code: 'test-shared-code',
    session: 'test-session',
    email: '<EMAIL>',
    response_type: 'MFA_REQUIRED',
  }
  const email = '<EMAIL>'
  const handleMfa = vi.fn()
  const isPending = false

  beforeEach(() => {
    global.ResizeObserver = ResizeObserver
    handleMfa.mockClear()
  })

  it('renders correctly', () => {
    render(
      <Mfa
        mfaDetails={mfaDetails}
        email={email}
        isPending={isPending}
        handleMfa={handleMfa}
        mfaError={''}
      />
    )

    expect(
      screen.getByText('Set up Two-Factor Authentication')
    ).toBeInTheDocument()
    expect(
      screen.getByText(
        'In your 2FA-compatible app, please scan the following QR code:'
      )
    ).toBeInTheDocument()
  })

  it('renders the QR code canvas', () => {
    const { getByTestId } = render(
      <MemoryRouter>
        <Mfa
          mfaDetails={mfaDetails}
          email={email}
          isPending={isPending}
          handleMfa={handleMfa}
          mfaError={''}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    expect(getByTestId('qr-code')).toBeInTheDocument()
  })

  it('calls handleMfa with the correct arguments when button is clicked', async () => {
    const { getAllByRole, getByRole } = render(
      <MemoryRouter>
        <Mfa
          mfaDetails={mfaDetails}
          email={email}
          isPending={isPending}
          handleMfa={handleMfa}
          mfaError={''}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    const inputSlots = getAllByRole('textbox')
    inputSlots.forEach((slot, index) => {
      fireEvent.change(slot, { target: { value: String(index + 1) } })
    })

    fireEvent.click(getByRole('button'))

    await waitFor(() => {
      expect(handleMfa).toHaveBeenCalledWith('test-shared-code', {
        email: '<EMAIL>',
        session: 'test-session',
        code: '1',
      })
    })
  })

  it('displays the loader when isPending is true', () => {
    const { getByTestId } = render(
      <MemoryRouter>
        <Mfa
          mfaDetails={mfaDetails}
          email={email}
          isPending={true}
          handleMfa={handleMfa}
          mfaError={''}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    expect(getByTestId('loading-spinner')).toBeInTheDocument()
  })
})
