import React from 'react'
import '@testing-library/jest-dom/vitest'
import { describe, expect, it } from 'vitest'
import { render } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import type {
  ExternalCase,
  ExternalIssueAnalysis,
} from 'prime-front-service-client'
import { CaseStatus } from 'prime-front-service-client'
import { vi } from 'vitest'
import { CaseInfo } from '../../pages/workroom-details/components/case-info'
import { Providers } from '../../components/providers'

const mockCaseInfo: ExternalCase = {
  title: 'Sample Issue',
  status: CaseStatus.open,
  comments: [],
  labels: ['two', 'three', 'seven', 'six'],
  provider_fields: {
    issue_name: 'Sample Issue',
    created_at: new Date().toLocaleDateString(),
    issue_id: 'ISSUE-123',
    creator: '<PERSON>',
    assignee: '<PERSON>',
    status: 'Open',
    issue_link: 'http://example.com',
    creator_email: null,
    assignee_email: null,
  },
  issue_analysis: {
    issue_links: [],
    short_ai_summary: '<p>Sample Ticket Summary</p>',
  } as unknown as ExternalIssueAnalysis,
} as unknown as ExternalCase

describe('CaseInfo', () => {
  it('renders issue name', () => {
    const { getByText } = render(
      <MemoryRouter>
        <CaseInfo
          successComment={false}
          caseInfo={mockCaseInfo}
          pendingComment={false}
          setIssueComment={vi.fn()}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('Sample Issue')).toBeInTheDocument()
  })

  it('renders ticket summary when present', () => {
    const { getByText } = render(
      <MemoryRouter>
        <CaseInfo
          successComment={false}
          caseInfo={mockCaseInfo}
          pendingComment={false}
          setIssueComment={vi.fn()}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('Expected Outcome')).toBeInTheDocument()
    expect(getByText('Sample Ticket Summary')).toBeInTheDocument()
  })

  it.skip('renders JiraIssueDetails component', () => {
    const { getByText } = render(
      <MemoryRouter>
        <CaseInfo
          successComment={false}
          caseInfo={mockCaseInfo}
          pendingComment={false}
          setIssueComment={vi.fn()}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('ISSUE-123')).toBeInTheDocument()
    expect(getByText('John Doe')).toBeInTheDocument()
    expect(getByText('Jane Doe')).toBeInTheDocument()
    expect(getByText('Open')).toBeInTheDocument()
  })

  it('renders comment input and button when status is not Done', () => {
    const { getByPlaceholderText, getByText } = render(
      <MemoryRouter>
        <CaseInfo
          successComment={false}
          caseInfo={mockCaseInfo}
          pendingComment={false}
          setIssueComment={vi.fn()}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByPlaceholderText('Type..')).toBeInTheDocument()
    expect(getByText('Activity Log')).toBeInTheDocument()
  })
})
