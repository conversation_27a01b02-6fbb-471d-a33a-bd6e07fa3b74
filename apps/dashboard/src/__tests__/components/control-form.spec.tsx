import React from 'react'
import { render } from '@testing-library/react'
import '@testing-library/jest-dom'
import type { ExternalControl } from 'prime-front-service-client'
import { ImplementationStatus } from 'prime-front-service-client'
import type { Mock } from 'vitest'
import { MemoryRouter } from 'react-router'
import { ControlForm } from '../../pages/workroom-details/components/control-form'
import { Providers } from '../../components/providers'

const mockControl: ExternalControl = {
  name: 'ABC',
  description: 'Test Control',
  id: '1',
  control_names: [],
  framework: 'PRIME',
  implementations: [
    {
      control_id: '1',
      controls: { '1': ['1'] },
      concern_id: 1,
      status: ImplementationStatus.approved,
      id: 1,
      recommendation: 'Recommendation 1',
    },
    {
      status: ImplementationStatus.dismissed,
      id: 2,
      recommendation: 'Recommendation 2',
      control_id: '1',
      controls: { '1': ['1'] },
      concern_id: 1,
    },
    {
      status: ImplementationStatus.unknown,
      id: 3,
      recommendation: 'Recommendation 3',
      control_id: '1',
      controls: { '1': ['1'] },
      concern_id: 1,
    },
  ],
}

describe('ControlForm', () => {
  let selectedRecommendations: number[]
  let onSelectionChange: Mock<any, any>

  beforeEach(() => {
    selectedRecommendations = []
    onSelectionChange = vi.fn()
  })

  test('renders control name', () => {
    const { getByText } = render(
      <MemoryRouter>
        <ControlForm
          concern_id={1}
          showSnippet={false}
          refetch={vi.fn()}
          control={mockControl}
          selectedRecommendations={selectedRecommendations}
          onSelectionChange={onSelectionChange}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('ABC')).toBeInTheDocument()
  })

  test('renders recommendations', () => {
    const { getByText } = render(
      <MemoryRouter>
        <ControlForm
          concern_id={1}
          showSnippet={false}
          refetch={vi.fn()}
          control={mockControl}
          selectedRecommendations={selectedRecommendations}
          onSelectionChange={onSelectionChange}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('Recommendation 1')).toBeInTheDocument()
    expect(getByText('Recommendation 2')).toBeInTheDocument()
    expect(getByText('Recommendation 3')).toBeInTheDocument()
  })

  test('renders checkboxes', () => {
    const { getAllByRole } = render(
      <MemoryRouter>
        <ControlForm
          concern_id={1}
          showSnippet={true}
          control={mockControl}
          selectedRecommendations={selectedRecommendations}
          refetch={vi.fn()}
          onSelectionChange={onSelectionChange}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getAllByRole('checkbox')).toHaveLength(1)
  })
})
