import '@testing-library/jest-dom/vitest'
import { vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { UsersTable } from '../../pages/settings/users-management/components/users-table'
import { Providers } from '../../components/providers'
import { MemoryRouter } from 'react-router'

// const server = setupServer()
//
// beforeAll(() => server.listen())
// afterEach(() => server.resetHandlers())
// afterAll(() => server.close())

vi.mock('i18next', async () => {
  // eslint-disable-next-line @typescript-eslint/consistent-type-imports
  const actual = await vi.importActual<typeof import('i18next')>('i18next')
  return {
    ...actual,
    t: (key: string) => key,
  }
})

vi.mock('sonner', async () => {
  // eslint-disable-next-line @typescript-eslint/consistent-type-imports
  const actual = await vi.importActual<typeof import('sonner')>('sonner')
  return {
    ...actual,
    toast: {
      success: vi.fn(),
      error: vi.fn(),
    },
  }
})

describe.skip('UsersTable', () => {
  test('renders users after loading', async () => {
    render(
      <MemoryRouter>
        <UsersTable
          data={[]}
          refetch={() => {
            // eslint-disable-next-line @typescript-eslint/no-empty-function
          }}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    await waitFor(() => {
      expect(screen.getByText('user')).toBeInTheDocument()
    })

    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('Tony Stark')).toBeInTheDocument()
  })
})
