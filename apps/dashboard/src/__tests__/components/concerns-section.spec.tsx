import React from 'react'
import '@testing-library/jest-dom/vitest'
import { describe, expect, vi } from 'vitest'
import { render } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import { Providers } from '../../components/providers'
import { ConcernsSection } from '../../pages/workroom-details/components/concerns-section'
import { ConcernType } from 'prime-front-service-client/src/models/ConcernType'

describe('ConcernsSection', () => {
  test('renders component', () => {
    const { baseElement, getByText, queryByText } = render(
      <MemoryRouter>
        <ConcernsSection
          frameworkConcerns={{
            NIST: [
              {
                long_description: 'long',
                short_description: 'short',
                id: 1,
                controls: [],
                methodology: {
                  type: ConcernType.Linddun,
                  category: '',
                },
              },
              {
                long_description: 'long',
                short_description: 'short1',
                id: 2,
                controls: [],
                methodology: {
                  type: ConcernType.Linddun,
                  category: '',
                },
              },
              {
                long_description: 'long',
                short_description: 'short2',
                id: 3,
                controls: [],
                methodology: {
                  type: ConcernType.Linddun,
                  category: '',
                },
              },
            ],
          }}
          primeConcerns={[
            {
              long_description: 'long',
              short_description: 'short',
              id: 1,
              methodology: {
                type: ConcernType.Mitre,
                category: '',
              },
              recommendations: [],
            },
            {
              long_description: 'long',
              short_description: 'short1',
              id: 2,
              methodology: {
                type: ConcernType.Mitre,
                category: '',
              },
              recommendations: [],
            },
            {
              long_description: 'long',
              short_description: 'short2',
              id: 3,
              methodology: {
                type: ConcernType.Linddun,
                category: '',
              },
              recommendations: [],
            },
          ]}
          summary={true}
          caseDone={true}
          onUpdateRecommendations={vi.fn()}
          updateCaseStatus={vi.fn()}
          refetch={vi.fn()}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(baseElement).toBeInTheDocument()
    expect(
      getByText('Review Concerns & Confirm Recommendations')
    ).toBeInTheDocument()
    expect(queryByText('long')).not.toBeInTheDocument()
  })

  test('renders component with collapse', () => {
    const { getAllByRole } = render(
      <MemoryRouter>
        <ConcernsSection
          frameworkConcerns={{
            NIST: [
              {
                long_description: 'long',
                short_description: 'short',
                id: 1,
                methodology: {
                  type: ConcernType.Linddun,
                  category: '',
                },
                controls: [],
              },
              {
                long_description: 'long',
                short_description: 'short1',
                id: 2,
                methodology: {
                  type: ConcernType.Linddun,
                  category: '',
                },
                controls: [],
              },
              {
                long_description: 'long',
                short_description: 'short2',
                id: 3,
                methodology: {
                  type: ConcernType.Linddun,
                  category: '',
                },
                controls: [],
              },
            ],
          }}
          primeConcerns={[
            {
              long_description: 'long',
              short_description: 'short',
              id: 1,
              methodology: {
                type: ConcernType.Mitre,
                category: '',
              },
              recommendations: [],
            },
            {
              long_description: 'long',
              short_description: 'short1',
              id: 2,
              methodology: {
                type: ConcernType.Mitre,
                category: '',
              },
              recommendations: [],
            },
            {
              long_description: 'long',
              short_description: 'short2',
              id: 3,
              methodology: {
                type: ConcernType.Linddun,
                category: '',
              },
              recommendations: [],
            },
          ]}
          caseDone={false}
          onUpdateRecommendations={vi.fn()}
          updateCaseStatus={vi.fn()}
          summary={true}
          refetch={vi.fn()}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getAllByRole('heading')).toHaveLength(3)
  })
})
