import '@testing-library/jest-dom/vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { SourceItem } from '../../pages/settings/sources/components/source-item'
import { vi } from 'vitest'
import { Providers } from '../../components/providers'
import i18n from '../../i18n'
import { initReactI18next } from 'react-i18next'
import { translationResources } from '@libs/common'
import { useFetchJiraSource } from '../../api/use-sources-api'
import { MemoryRouter } from 'react-router'
import { toast } from 'sonner'

i18n.use(initReactI18next).init({
  resources: translationResources,
  lng: 'en', // default language
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false, // React already safes from XSS
  },
})

vi.mock('sonner', async () => {
  // eslint-disable-next-line @typescript-eslint/consistent-type-imports
  const actual = await vi.importActual<typeof import('sonner')>('sonner')
  return {
    ...actual,
    toast: {
      success: vi.fn(),
      error: vi.fn(),
    },
  }
})

vi.mock('../../api/use-sources-api', () => ({
  useFetchJiraSource: vi.fn(),
}))

vi.mock('i18next', async () => {
  // eslint-disable-next-line @typescript-eslint/consistent-type-imports
  const actual = await vi.importActual<typeof import('i18next')>('i18next')
  return {
    ...actual,
    t: (key: string) => key,
  }
})

describe('SourcesTableItem', () => {
  const mockSource = {
    id: 1,
    info: {
      jira_url: 'http://jira.com',
      email: '<EMAIL>',
      jql: 'project = TEST',
    },
    sourceType: 'Jira',
  }

  const mockMutate = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useFetchJiraSource as any).mockReturnValue({
      mutate: mockMutate,
      isPending: false,
    })
  })

  it('renders source information correctly', () => {
    render(
      <MemoryRouter initialEntries={['/']}>
        <SourceItem source={mockSource as any} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    expect(
      screen.getByRole('link', { name: 'http://jira.com' })
    ).toBeInTheDocument()
  })

  it('calls mutate function when fetch button is clicked', () => {
    render(
      <MemoryRouter initialEntries={['/']}>
        <SourceItem source={mockSource as any} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    const fetchButton = screen.getByRole('button', { name: /fetch/i })
    fireEvent.click(fetchButton)

    expect(mockMutate).toHaveBeenCalledWith(mockSource.id, expect.any(Object))
  })

  it('shows success toast on successful fetch', async () => {
    mockMutate.mockImplementation((id, { onSuccess }) => {
      onSuccess({ status: 'Success' })
    })

    render(
      <MemoryRouter initialEntries={['/']}>
        <SourceItem source={mockSource as any} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    const fetchButton = screen.getByRole('button', { name: /fetch/i })
    fireEvent.click(fetchButton)

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Source status: Success')
    })
  })
})
