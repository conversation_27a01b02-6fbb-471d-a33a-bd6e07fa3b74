import React from 'react'
import { render } from '@testing-library/react'
import '@testing-library/jest-dom'
import { MemoryRouter } from 'react-router'
import { Providers } from '../../components/providers'
import { ImplementationStatus } from 'prime-front-service-client'
import type { ExternalControl } from 'prime-front-service-client'
import { ControlCard } from '../../pages/workroom-details/components/control-card'
import { expect } from 'vitest'

describe('ControlCard', () => {
  const mockControl: ExternalControl = {
    name: 'ABC',
    description: 'Test Control',
    control_names: [],
    framework: 'PRIME',
    id: '1',
    implementations: [
      {
        control_id: '1',
        controls: { '1': ['1'] },
        concern_id: 1,
        status: ImplementationStatus.approved,
        id: 1,
        recommendation: 'Recommendation 1',
      },
      {
        status: ImplementationStatus.dismissed,
        id: 2,
        recommendation: 'Recommendation 2',
        control_id: '1',
        controls: { '1': ['1'] },
        concern_id: 1,
      },
      {
        status: ImplementationStatus.unknown,
        id: 3,
        recommendation: 'Recommendation 3',
        control_id: '1',
        controls: { '1': ['1'] },
        concern_id: 1,
      },
    ],
  }

  test('renders the control name', () => {
    const { getByText } = render(
      <MemoryRouter>
        <ControlCard control={mockControl} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('ABC')).toBeInTheDocument()
    expect(getByText('- Test Control')).toBeInTheDocument()
  })

  test('renders only approved recommendations', () => {
    const { getByText, queryByText } = render(
      <MemoryRouter>
        <ControlCard control={mockControl} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('Recommendation 1')).toBeInTheDocument()
    expect(queryByText('Recommendation 2')).not.toBeInTheDocument()
  })
})
