import React from 'react'
import '@testing-library/jest-dom/vitest'
import { describe, expect } from 'vitest'
import { render } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import type {
  ExternalCase,
  ExternalIssueAnalysis,
} from 'prime-front-service-client'
import {
  CaseStatus,
  RiskScoreCategory,
  RiskFactorLevel,
} from 'prime-front-service-client'
import { CaseAssessment } from '../../pages/workroom-details/components/case-assessment'
import { Providers } from '../../components/providers'

const mockCaseInfo: ExternalCase = {
  account_id: '1',
  issue_analysis: {
    account_id: '1',
    risk_score_category: RiskScoreCategory.intervene,
    confidence: 85,
    long_assessment: 'Detailed analysis and reasoning here.',
    short_assessment: 'Detailed analysis and reasoning here.',
    risk_factors: {
      confidentiality_level: RiskFactorLevel.high,
      integrity_level: RiskFactorLevel.medium,
      availability_level: RiskFactorLevel.low,
      compliance_level: RiskFactorLevel.high,
      third_party_management_level: RiskFactorLevel.medium,
    },
  } as unknown as ExternalIssueAnalysis,
  status: CaseStatus.open,
} as unknown as ExternalCase

describe('CaseAssessment', () => {
  test('renders reasoning section when reasoning is present', () => {
    const { getByText } = render(
      <MemoryRouter>
        <CaseAssessment
          refetch={vi.fn()}
          caseInfo={mockCaseInfo}
          summary={true}
          reopenCase={vi.fn()}
          isReopenLoading={false}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(
      getByText('Detailed analysis and reasoning here.')
    ).toBeInTheDocument()
  })

  test.skip('renders risk factor by category header', () => {
    const { getByText } = render(
      <MemoryRouter>
        <CaseAssessment
          caseInfo={mockCaseInfo}
          refetch={vi.fn()}
          summary={true}
          reopenCase={vi.fn()}
          isReopenLoading={false}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('Risk Factors')).toBeInTheDocument()
  })
  test('does not render reasoning section when reasoning is absent', () => {
    const caseInfoWithoutReasoning = {
      ...mockCaseInfo,
      issue_analysis: {
        ...mockCaseInfo.issue_analysis,
        short_assessment: undefined,
      } as unknown as ExternalIssueAnalysis,
    }
    const { queryByText } = render(
      <MemoryRouter>
        <CaseAssessment
          caseInfo={caseInfoWithoutReasoning}
          refetch={vi.fn()}
          summary={true}
          reopenCase={vi.fn()}
          isReopenLoading={false}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    expect(queryByText('Why Was This Issue Flagged:')).not.toBeInTheDocument()
  })
})
