import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { TasksStatusList } from '../../pages/settings/tasks-status/components/tasks-status-list'
import { Providers } from '../../components/providers'
import { snakeCaseToCamelCase } from '@libs/common'

const mockData = [
  {
    name: 'fetcher',
    status: 'FAILED',
    error:
      '(400)\nReason: Bad Request\nHTTP response headers: <CIMultiDictProxy(\'Date\': \'Thu, 16 May 2024 12:17:08 GMT\', \'Server\': \'uvicorn\', \'Content-Length\': \'270\', \'Content-Type\': \'application/json\')>\nHTTP response body: {\n    "type": "E1301-JobSpawnerError", \n    "title": "E1301-JobSpawnerError", \n    "detail": "Failed to spawn job", \n    "exception": "JobSpawnerError(\'Failed to spawn job\')", \n    "url": "http://fetcher-service:8000/jobs/eu-central-1_nXDmmWoOj/fetch-job?source-id=28"\n}\n',
    id: 56,
    created_at: '2024-05-16T12:17:08.821613Z',
    progress: 100.0,
    source_id: '',
  },
  {
    name: 'orchestrator',
    status: 'FAILED',
    error: 'Task orchestrator failed (reason: task 56 failed)',
    id: 55,
    created_at: '2024-05-16T12:17:08.681183Z',
    progress: 100.0,
    source_id: '',
  },
]
const data = snakeCaseToCamelCase(mockData)

describe('TasksStatusList', () => {
  it('should render tasks with correct details', () => {
    render(<TasksStatusList data={data} />, {
      wrapper: Providers,
    })

    const taskTitles = screen.getAllByRole('heading')
    expect(taskTitles[0]).toHaveTextContent('fetcher')
    expect(taskTitles[1]).toHaveTextContent('orchestrator')
  })
})
