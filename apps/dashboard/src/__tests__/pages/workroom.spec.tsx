import '@testing-library/jest-dom/vitest'
import { describe, it, expect, vi } from 'vitest'
import { render } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import { WorkroomPage } from '../../pages'
import { Providers } from '../../components/providers'

vi.mock('../../pages/workroom/hooks/use-filters', () => ({
  useFilters: vi.fn(() => ({
    getFilter: vi.fn().mockReturnValue(null),
    deleteFilter: vi.fn(),
  })),
}))

describe.skip('WorkroomPage', () => {
  it('renders WorkroomPage component with correct tabs and data', () => {
    const { baseElement } = render(
      <MemoryRouter>
        <WorkroomPage />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(baseElement).toBeInTheDocument()
  })
})
