import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { fireEvent, render } from '@testing-library/react'
import { LoginPage } from '../../pages'
import { Providers } from '../../components/providers'
import { MemoryRouter } from 'react-router'

vi.mock('../../../hooks/use-auth-api')

describe('LoginPage Component', () => {
  it('renders LoginPage component correctly', () => {
    const {
      baseElement,
      getByText,
      getByPlaceholderText,
      queryByPlaceholderText,
    } = render(
      <MemoryRouter>
        <LoginPage />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(baseElement).toBeInTheDocument()
    expect(getByText('Login')).toBeInTheDocument()
    expect(getByPlaceholderText('Email')).toBeInTheDocument()
    expect(getByText('Continue')).toBeInTheDocument()
    expect(queryByPlaceholderText('Password')).not.toBeInTheDocument()
  })
  it('should show password field after valid email is submitted', async () => {
    vi.mock('../../../hooks/use-auth-api', async () => ({
      useIdentify: () => ({
        isPending: false,
        data: null,
        isSuccess: true,
        mutateAsync: vi.fn().mockResolvedValue({}),
      }),
    }))
    const { getByText, getByPlaceholderText } = render(
      <MemoryRouter>
        <LoginPage />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    const emailInput = getByPlaceholderText('Email')
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.click(getByText('Continue'))
    expect(getByPlaceholderText('Email')).toBeInTheDocument()
  })
})
