import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import { Providers } from '../../components/providers'
import { AddSourceForm } from '../../pages/settings/sources/components/add-source-form'

describe('AddSourcePage component', () => {
  it('renders AddSourcePage component', () => {
    const { baseElement } = render(
      <MemoryRouter>
        <AddSourceForm />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(baseElement).toBeInTheDocument()
  })
})
