import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { createMemoryRouter, RouterProvider } from 'react-router'
import { WorkroomDetailsPage } from '../../pages'
import { Providers } from '../../components/providers'
import type { ExternalCase } from 'prime-front-service-client'

const mockIssueDetailsData = {
  data: {
    issueAnalysis: {
      issueFields: {
        issueName: 'Test Issue',
        createdAt: new Date(),
        issueId: '123',
        creator: 'Test Creator',
        assignee: 'Test Assignee',
        status: 'Open',
      },
      ticketSummary: 'Test summary',
      reasoning: 'Test reasoning',
      keywords: ['test', 'keyword'],
      classification: {
        complianceLevel: 'High',
        confidentiality_level: 'High',
        thirdPartyManagementLevel: 'High',
        integrity_level: 'High',
        availability_level: 'High',
      },
      confidence: 0.8,
    },
    recommendations: {
      recommendations: {
        1: {
          id: 1,
          recommendation: 'Test Recommendation 1',
          status: 'Unknown',
        },
        2: {
          id: 2,
          recommendation: 'Test Recommendation 2',
          status: 'Unknown',
        },
      },
    },
    comments: 'Test comments',
    status: 'Open',
  } as unknown as ExternalCase,
  isPending: false,
  isError: false,
  isSuccess: true,
  refetch: vi.fn(),
}
const mockUpdateRecommendations = vi.fn()
const mockUpdateComment = vi.fn()
const useIssueDetails = vi.fn(() => mockIssueDetailsData)

describe('WorkroomDetailsPage', () => {
  beforeEach(() => {
    useIssueDetails.mockClear()
    mockUpdateRecommendations.mockClear()
    mockUpdateComment.mockClear()
  })

  it('renders WorkroomDetailsPage component with correct tabs and data', () => {
    const router = createMemoryRouter([
      { path: '*', element: <WorkroomDetailsPage /> },
    ])
    const { baseElement } = render(<RouterProvider router={router} />, {
      wrapper: Providers,
    })
    expect(baseElement).toBeInTheDocument()
  })

  it('renders loading spinner while data is pending', () => {
    useIssueDetails.mockReturnValueOnce({
      refetch: vi.fn(),
      data: undefined as unknown as ExternalCase,
      isPending: true,
      isError: false,
      isSuccess: false,
    })

    const router = createMemoryRouter([
      { path: '*', element: <WorkroomDetailsPage /> },
    ])
    const { getByTestId } = render(<RouterProvider router={router} />, {
      wrapper: Providers,
    })
    expect(getByTestId('loading-spinner')).toBeInTheDocument()
  })
})
