import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import { JiraAttributesPage } from '../../pages/settings/jira-attributes/page'
import { Providers } from '../../components/providers'

describe('JiraAttributesPage component', () => {
  it('renders JiraAttributesPage component', () => {
    const { baseElement } = render(
      <MemoryRouter>
        <JiraAttributesPage />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(baseElement).toBeInTheDocument()
  })
  it('shows loading when pending', () => {
    const { getByTestId, getByText } = render(
      <MemoryRouter>
        <JiraAttributesPage />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByTestId('loading-spinner')).toBeInTheDocument()
    expect(getByText('jira attributes')).toBeInTheDocument()
  })
})
