import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { MemoryRouter } from 'react-router'

import { NotFoundPage } from '../../pages'
import { Providers } from '../../components/providers'

describe('NotFoundPage', () => {
  it('renders page not found text', () => {
    render(
      <MemoryRouter>
        <NotFoundPage />
      </MemoryRouter>,
      { wrapper: Providers }
    )
    const pageNotFoundText = screen.getByText('Page not found')
    expect(pageNotFoundText).toBeInTheDocument()
  })

  it('renders go back home link', () => {
    render(
      <MemoryRouter>
        <NotFoundPage />
      </MemoryRouter>,
      { wrapper: Providers }
    )
    const goBackHomeLink = screen.getByRole('link', { name: 'Go back home' })
    expect(goBackHomeLink).toBeInTheDocument()
  })

  it('go back home link has correct destination', () => {
    render(
      <MemoryRouter>
        <NotFoundPage />
      </MemoryRouter>,
      { wrapper: Providers }
    )
    const goBackHomeLink = screen.getByRole('link', { name: 'Go back home' })
    expect(goBackHomeLink).toHaveAttribute('href', '/')
  })
})
