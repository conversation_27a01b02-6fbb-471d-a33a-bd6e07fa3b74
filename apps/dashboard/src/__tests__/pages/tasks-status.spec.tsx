import '@testing-library/jest-dom/vitest'
import { describe, it } from 'vitest'
import { render } from '@testing-library/react'
import { TasksStatusPage } from '../../pages'
import { Providers } from '../../components/providers'
import { MemoryRouter } from 'react-router'

describe('TasksStatusPage', () => {
  it('should render tasks status page with correct heading', async () => {
    render(
      <MemoryRouter>
        <TasksStatusPage />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
  })

  it('should render tasks status list component', () => {
    render(<TasksStatusPage />, {
      wrapper: Providers,
    })
  })
})
