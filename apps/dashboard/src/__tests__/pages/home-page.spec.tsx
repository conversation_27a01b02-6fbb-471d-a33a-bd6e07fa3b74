import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { createMemoryRouter, RouterProvider } from 'react-router'
import { HomePage } from '../../pages'
import { Providers } from '../../components/providers'

describe('HomePage', () => {
  it('renders HomePage component', () => {
    const router = createMemoryRouter([{ path: '*', element: <HomePage /> }])
    const { baseElement } = render(<RouterProvider router={router} />, {
      wrapper: Providers,
    })
    expect(baseElement).toBeInTheDocument()
  })
})
