import React from 'react'
import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import { Providers } from '../../components/providers'
import { SettingsLayout } from '../../pages/settings/layout'
describe('SettingsLayout component', () => {
  it('renders SettingsLayout component', () => {
    const { baseElement } = render(
      <MemoryRouter>
        <SettingsLayout />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(baseElement).toBeInTheDocument()
  })
})
