import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import { SourcesPage } from '../../pages'
import { Providers } from '../../components/providers'

describe('SourcesPage', () => {
  describe('Rendering', () => {
    it('renders SourcesPage component with correct tabs and data', () => {
      const { baseElement } = render(
        <MemoryRouter>
          <SourcesPage />
        </MemoryRouter>,
        {
          wrapper: Providers,
        }
      )
      expect(baseElement).toBeInTheDocument()
    })
    it('Renders loading state when data is pending', async () => {
      vi.mock('../../../hooks/use-api', () => ({
        useGetAllSources: () => ({
          data: null,
          isPending: true,
          refetch: vi.fn(),
          isRefetching: false,
        }),
        useAddJiraSource: vi.fn(),
      }))

      const { getByTestId } = render(
        <MemoryRouter>
          <SourcesPage />
        </MemoryRouter>,
        { wrapper: Providers }
      )

      expect(getByTestId('sources-skeleton')).toBeInTheDocument()
    })
  })
})
