import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import { ProtectedLayout, PublicLayout } from '../../auth/auth-utils'

describe('Layout Components', () => {
  it('should render ProtectedLayout correctly', () => {
    render(
      <MemoryRouter>
        <ProtectedLayout />
      </MemoryRouter>
    )
    expect(screen.getByTestId('sidebar')).toBeInTheDocument()
  })

  it('should render PublicLayout correctly', () => {
    render(
      <MemoryRouter>
        <PublicLayout />
      </MemoryRouter>
    )
  })
})
