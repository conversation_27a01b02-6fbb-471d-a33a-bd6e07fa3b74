rules:
  typescript:
    imports:
      prefer_type_imports: true
      group_order: [react, external, internal, relative]
    naming:
      variables: camelCase
      functions: camelCase
      types: PascalCase
    max_lines: 300

  react:
    components:
      style: functional
      hooks: required
    state:
      prefer: [jotai, react-query]

  api:
    pattern: react-query
    location: src/api
    naming: use{Resource}Api.tsx

  styling:
    framework: tailwind
    utility: cn
