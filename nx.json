{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)"], "sharedGlobals": []}, "nxCloudAccessToken": "ZTE5M2I2ZTEtNzU4MC00MGI5LTljZmUtOGExYmY3ZGEzYzZhfHJlYWQtd3JpdGU=", "workspaceLayout": {"appsDir": "apps", "libsDir": "libs"}, "targetDefaults": {"@nx/vite:build": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/js:tsc": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "plugins": [{"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "previewTargetName": "preview", "testTargetName": "test", "serveTargetName": "serve", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/next/plugin", "options": {"buildTargetName": "build", "devTargetName": "dev", "startTargetName": "start", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}}], "generators": {"@nx/next": {"application": {"style": "none", "linter": "eslint"}}, "@nx/react": {"library": {"style": "tailwind", "linter": "eslint", "unitTestRunner": "vitest"}, "application": {"babel": true, "style": "tailwind", "linter": "eslint", "bundler": "vite"}, "component": {"style": "tailwind"}}}, "neverConnectToCloud": true, "defaultBase": "origin/main"}