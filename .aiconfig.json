{"name": "PrimeSec Dashboard", "description": "Frontend monorepo for PrimeSec dashboard application", "schema_version": "1.0.0", "related_configs": {"github_copilot": ".github/copilot-instructions.md", "cursor": ".cursorrules"}, "settings": {"style": {"typescript": {"imports": "type imports preferred", "naming": "camelCase for variables/functions, PascalCase for types/interfaces", "max_lines": 300}, "react": {"components": "functional components with hooks", "styling": "tailwind with shadcn/ui components", "state": {"local": "useState", "global": "jotai", "server": "react-query"}}}, "patterns": {"api": {"location": "src/api", "naming": "use{Resource}Api.tsx", "mutations": "prefer mutate over mutateAsync", "queries": "useQuery with defaultQueryConfig"}, "components": {"location": "src/components", "styling": "tailwind with cn utility", "props": "type Props = { children?: ReactNode } & {...}"}}}}