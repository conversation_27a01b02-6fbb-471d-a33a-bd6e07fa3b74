// copilot-plugin.js
export const definePlugin = (api) => {
  // Register your OpenAPI spec as a context provider
  api.registerContextProvider({
    name: 'backend-api',
    description: 'FastAPI backend interfaces and endpoints',
    openapi: require('./node_modules/prime-front-service-client/openapi.json'),
    // Adjust the path above to where your openapi.json is located in the npm package
  })
}
