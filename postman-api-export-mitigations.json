{"info": {"name": "Export Mitigations API Call", "description": "API call for exporting mitigations from a security review", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Export Mitigations", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-prime-account-id", "value": "{{account_id}}", "type": "text", "description": "Prime Account ID (optional)"}, {"key": "x-prime-user-id", "value": "{{user_id}}", "type": "text", "description": "Prime User ID (optional)"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text", "description": "Authorization token (add if required)"}], "body": {"mode": "raw", "raw": "{\n  \"should_export_to_pdf\": true,\n  \"should_export_to_csv\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/security-reviews/{{security_review_id}}/mitigations/export", "host": ["{{base_url}}"], "path": ["security-reviews", "{{security_review_id}}", "mitigations", "export"]}, "description": "Export mitigations for a specific security review. This endpoint allows you to export mitigations in PDF and/or CSV format.\n\n**Path Parameters:**\n- `security_review_id` (integer, required): The ID of the security review\n\n**Headers:**\n- `x-prime-account-id` (string, optional): Prime Account ID\n- `x-prime-user-id` (string, optional): Prime User ID\n- `Authorization` (string): Bearer token for authentication\n\n**Request Body:**\n- `should_export_to_pdf` (boolean): Whether to export as PDF (default: false)\n- `should_export_to_csv` (boolean): Whether to export as CSV (default: false)\n\n**Response:**\n- 200: Successful response with exported data\n- 422: Validation error"}, "response": []}], "variable": [{"key": "base_url", "value": "https://api.primesec.dev", "description": "Base URL for the PrimeSec API (update with actual URL)"}, {"key": "security_review_id", "value": "123", "description": "ID of the security review to export mitigations from"}, {"key": "account_id", "value": "", "description": "Your Prime Account ID"}, {"key": "user_id", "value": "", "description": "Your Prime User ID"}, {"key": "access_token", "value": "", "description": "Your access token for authentication"}]}