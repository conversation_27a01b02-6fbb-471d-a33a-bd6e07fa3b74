name: build typeScript

inputs:
  tsconfig-path:
    required: false
    description: 'Path to the TypeScript configuration file'
    default: 'apps/dashboard/tsconfig.json'

  incremental:
    required: false
    description: 'Run with incremental mode'
    default: 'true'

description: |
  This action builds TypeScript using `tsc` for the specified configuration.

runs:
  using: 'composite'
  steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Install dependencies
      uses: primesec-ai/common/.github/actions/pnpm-install@main

    - name: build typeScript
      shell: bash
      run: |
        pnpm exec tsc -b ${{ inputs.tsconfig-path }} ${{ inputs.incremental == 'true' && '--incremental' || '' }}
