name: build frontend

inputs:
  run-tests:
    required: true
    description: 'run tests'
  app-name:
    required: true
    description: 'app name'
  api-mock:
    required: true
    description: 'api mock'
  login-mock:
    required: true
    description: 'login mock'
  vite-front-service-url:
    required: true
    description: 'vite front service url'
  vite-app-version:
    required: true
    description: 'app version'
  vite-datadog-application-id:
    required: true
    description: 'datadog application id'
  vite-datadog-client-token:
    required: true
    description: 'datadog client token'
  vite-datadog-service:
    required: true
    description: 'datadog service'
  vite-datadog-env:
    required: true
    description: 'datadog env'
  vite-launchdarkly-client-id:
    required: true
    description: 'launchdarkly client id'
  vite-launchdarkly-env-key:
    required: true
    description: 'launchdarkly env key'
  vite-beamer-product-id:
    required: true
    description: 'beamer product id'
  vite-magicbell-api-key:
    required: true
    description: 'magicbell api key'
  vite-fake-launchdarkly:
    required: true
    description: 'fake launchdarkly' 

description: |
  This action activates a virtualenv.
  It is based on

runs:
  using: 'composite'
  steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ env.ROLE_TO_ASSUME }}
        aws-region: ${{ env.AWS_REGION }}

    - name: login codeartifact
      shell: bash
      run: aws codeartifact login --tool npm --repository main-repo --domain primesec

    - name: install pnpm
      uses: primesec-ai/common/.github/actions/pnpm-install@main

    - name: Lint App
      if: ${{ inputs.run-tests == 'true' }}
      shell: bash
      run: pnpm exec nx affected -t lint

    - name: Run tests
      if: ${{ inputs.run-tests == 'true' }}
      shell: bash
      run: pnpm exec nx run-many --target=test --all --coverage

    - name: Build App
      shell: bash
      env:
        VITE_USE_API_MOCK: ${{ inputs.api-mock }}
        VITE_USE_LOGIN_MOCK: ${{ inputs.login-mock }}
        VITE_APP_VERSION: ${{ inputs.vite-app-version }}
        VITE_FRONT_SERVICE_URL: ${{ inputs.vite-front-service-url }}
        VITE_DATADOG_APPLICATION_ID: ${{ inputs.vite-datadog-application-id }}
        VITE_DATADOG_CLIENT_TOKEN: ${{ inputs.vite-datadog-client-token }}
        VITE_DATADOG_SERVICE: ${{ inputs.vite-datadog-service }}
        VITE_DATADOG_ENV: ${{ inputs.vite-datadog-env }}
        VITE_LAUNCHDARKLY_CLIENT_ID: ${{ inputs.vite-launchdarkly-client-id }}
        VITE_LAUNCHDARKLY_ENV_KEY: ${{ inputs.vite-launchdarkly-env-key }}
        VITE_BEAMER_PRODUCT_ID: ${{ inputs.vite-beamer-product-id }}
        VITE_MAGICBELL_API_KEY: ${{ inputs.vite-magicbell-api-key }}
        VITE_FAKE_LAUNCHDARKLY: ${{ inputs.vite-fake-launchdarkly }}

      run: |
        pnpm exec nx run-many --target=build \
        --all \
        --configuration=production
