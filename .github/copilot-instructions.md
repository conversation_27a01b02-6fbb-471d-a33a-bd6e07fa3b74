# PrimeSec Dashboard Development Guidelines

## Project Overview
Frontend monorepo for PrimeSec dashboard application using React, TypeScript, and NX.

## Key Technologies
- React with functional components and hooks
- State Management: Jotai
- Styling: Tailwind CSS
- API: TanStack Query
- Testing: Vitest, Playwright

## Code Style Guidelines

### TypeScript
- Use type imports: `import type { MyType } from './types'`
- Naming: camelCase for variables/functions, PascalCase for types/interfaces
- Max file length: 300 lines

### React Components
- Use functional components with hooks
- Prefer composition over inheritance
- Keep components focused and single-responsibility

### API Layer
- Use TanStack Query hooks in `src/api` directory
- Standard pattern for mutations:
```tsx
useMutation({
  mutationKey: ['resourceAction'],
  mutationFn: async (params) => await api.action(params)
})
```

### State Management
- Use Jotai for global state
- Prefer local state for component-specific data
- Use atomWithStorage for persistent state

## Common Patterns
1. Error Handling: Use toast notifications for user feedback
2. Form Management: Use react-hook-form
3. Styling: Use cn utility for conditional classes
4. Testing: Write unit tests with Vitest

## File Organization
- /apps/dashboard - Main application
- /libs/ui - Shared UI components
- /libs/common - Shared utilities

# PrimeSec Dashboard Patterns

## API Hooks
```tsx
// Standard query pattern
export const useResourceQuery = (params: ResourceParams) => {
  return useQuery({
    queryKey: ['resource', params],
    queryFn: () => api.getResource(params),
    ...defaultQueryConfig
  })
}

// Standard mutation pattern
export const useResourceMutation = () => {
  return useMutation({
    mutationKey: ['resource'],
    mutationFn: (data: ResourceData) => api.updateResource(data)
  })
}
```

## Component Pattern
```tsx
import type { ReactNode } from 'react'
import { cn } from '@libs/common'

interface Props {
  children?: ReactNode
  className?: string
}

export const Component = ({ children, className }: Props) => {
  return (
    <div className={cn("base-styles", className)}>
      {children}
    </div>
  )
}
```

## State Management
- Local state: `useState` for component-specific state
- Global state: `jotai` atoms in `src/store`
- Server state: `react-query` hooks in `src/api`

## Testing
- Unit tests with Vitest in `*.test.tsx` files
- E2E tests with Playwright in `e2e` directory
