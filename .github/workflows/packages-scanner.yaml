name: Package scan for vulnerabilities
on:
  workflow_call:

jobs:
  scan:
    runs-on: ubuntu-latest
    env:
      CMD_ARGS: --prod=only
      PNPM_CACHE_FOLDER: .cache/pnpm
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.8.0

      # fix for: Path Validation Error: Path(s) specified in the action for caching does not exist
      ## https://github.com/actions/setup-node/issues/1137#issuecomment-2513682178
      - name: Setup pnpm config
        run: pnpm config set store-dir "$PNPM_CACHE_FOLDER"
      # Makes sure the pnpm store path exists (which is exactly what setup-node cares about)
      - name: Verify PNPM Cache Directory
        run: |
          PNPM_STORE_PATH="$( pnpm store path --silent )"
          if [ ! -d "$PNPM_STORE_PATH" ]; then
            echo "PNPM store directory does not exist, creating it."
            mkdir -p "$PNPM_STORE_PATH"
          else
            echo "PNPM store directory exists."
          fi

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Scan for packages vulnerabilities
        run: |
          # Run the audit command and capture the output, avoiding exit code 1
          echo "Running pnpm audit with arguments: $CMD_ARGS"
          packages=$(pnpm audit $CMD_ARGS || true)
          # Log the entire audit result to see what is returned
          echo "Packages audit result:"
          echo "$packages"

          # If no vulnerabilities are found
          if echo "$packages" | grep -q "No known vulnerabilities"; then
            echo ":rocket: No vulnerabilities found in packages :rocket:" >> $GITHUB_STEP_SUMMARY
            exit 0
          fi

          # Extract the summary of vulnerabilities found
          summary_packages=$(echo "$packages" | grep Severity -A1 -B1)
          echo "Summary packages:"
          echo "$summary_packages" # Log the filtered summary of packages

          # Check if any vulnerabilities were found
          if [ -n "$summary_packages" ]; then
            echo "Vulnerabilities found in packages"
            echo "$packages" # Log full packages content
            echo "### :rotating_light: Vulnerabilities found in packages :rotating_light:" >> $GITHUB_STEP_SUMMARY
            echo "$summary_packages" >> $GITHUB_STEP_SUMMARY

            # Run the audit command with the --fix option to see if fixes are available
            j=$(pnpm audit $CMD_ARGS --fix --dry-run --json || true)
            echo "Audit results for fix available:"
            echo "$j" # Log the dry-run JSON audit results

            # Process the vulnerabilities and show any available fixes
            fixes=$(echo $j | jq '[.vulnerabilities[] | .fixAvailable] | unique')
            table=$(echo "$fixes" | jq -r '.[] | [.name, .version] | @tsv' | column -t -s $'\t')

            echo "Fix available for vulnerabilities"
            echo "### :vertical_traffic_light: Fix available for vulnerabilities :vertical_traffic_light:" >> $GITHUB_STEP_SUMMARY
            echo "$table" >> $GITHUB_STEP_SUMMARY

            exit 1
          fi
