name: Dashboard client

on:
  pull_request:
    branches:
      - 'main'

permissions:
  contents: read
  id-token: write

env:
  APP_NAME: dashboard
  REGION: eu-central-1
  NX_NO_CLOUD: true
  VITE_USE_API_MOCK: true
  VITE_USE_LOGIN_MOCK: true
  VITE_FRONT_SERVICE_URL: ''
  ROLE_TO_ASSUME: ${{ vars.DEV_ROLE_TO_ASSUME }}
  AWS_REGION: eu-central-1

jobs:
  scanner:
    if: github.event.pull_request.merged == false
    uses: ./.github/workflows/packages-scanner.yaml

  build:
    if: github.event.pull_request.merged == false
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: build frontend
        uses: ./.github/actions/build
        with:
          app-name: ${{ env.APP_NAME }}
          run-tests: 'true'

      - name: build typescript
        uses: ./.github/actions/build-typescript
        with:
          tsconfig-path: 'apps/dashboard/tsconfig.json'
          incremental: 'true'
