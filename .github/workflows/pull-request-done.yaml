name: Deploy

on:
  pull_request:
    types:
      - closed
    branches:
      - main

permissions:
  contents: write
  id-token: write

env:
  BUCKET_NAME: primesec-deployments
  ROLE_TO_ASSUME: ${{ vars.DEV_ROLE_TO_ASSUME }}
  AWS_REGION: eu-central-1

jobs:
  get-version:
    runs-on: ubuntu-latest
    outputs:
      version-name: ${{ steps.get-version.outputs.version-name }}
      api-domain: ${{ steps.get-api-domain.outputs.api-domain }}
    steps:
      - name: get version
        id: get-version
        uses: primesec-ai/common/.github/actions/version-name@main

      - name: get api domain
        id: get-api-domain
        shell: bash
        run: |
          full_version=${{ steps.get-version.outputs.version-name }}
          shortsha=$(echo $full_version | cut -d '-' -f 2)
          api_domain="https://pipelines-api-${shortsha}.primesec.dev"
          echo "api-domain=${api_domain}" >> $GITHUB_OUTPUT
          echo "api-domain=${api_domain}"

  build:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    needs: [get-version]
    env:
      APP_NAME: dashboard
      NX_NO_CLOUD: true
    strategy:
      fail-fast: false
      matrix:
        mode:
          [
            {
              api_mock: 'false',
              login_mock: 'false',
              type: 'stage',
              vite_front_service_url: 'https://api.primesec.dev',
              vite-datadog-application-id: '91b688d9-8673-4081-8c26-ca710267e023',
              vite-datadog-client-token: 'pub71b5f8cd07efc1ca05496406976451d3',
              vite-datadog-service: 'stage.primesec.dev',
              vite-datadog-env: 'stage',
              vite-launchdarkly-client-id: '66f96e52dd309608629f1502',
              vite-launchdarkly-env-key: 'stage',
              vite-beamer-product-id: 'nJEEAonz71681',
              vite-magicbell-api-key: '01a680bd47b0124aade05a071ec76a007b2264d6',
              vite-fake-launchdarkly: 'false'
            },
            {
              api_mock: 'false',
              login_mock: 'false',
              type: 'prod',
              vite_front_service_url: 'https://api.primesec.ai',
              vite-datadog-application-id: '17e6b822-12e8-49a4-9056-42bd10a1a34e',
              vite-datadog-client-token: 'pub5ba28a2f333055ab9e9ffbf4a7bd9a21',
              vite-datadog-service: 'app.primesec.ai',
              vite-datadog-env: 'prod',
              vite-launchdarkly-client-id: '66f96b148a82ef088f47f050',
              vite-launchdarkly-env-key: 'production',
              vite-beamer-product-id: 'nJEEAonz71681',
              vite-magicbell-api-key: '01a680bd47b0124aade05a071ec76a007b2264d6',
              vite-fake-launchdarkly: 'false'
            },
            {
              api_mock: 'false',
              login_mock: 'false',
              type: 'pipelines',
              vite_front_service_url: '${{ needs.get-version.outputs.api-domain }}',
              vite-launchdarkly-client-id: '66f96b148a82ef088f47f04f',
              vite-launchdarkly-env-key: 'test',
              vite-beamer-product-id: 'nJEEAonz71681',
              vite-magicbell-api-key: '01a680bd47b0124aade05a071ec76a007b2264d6',
              vite-fake-launchdarkly: 'true'
            },
          ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: build frontend
        uses: ./.github/actions/build
        with:
          app-name: ${{ env.APP_NAME }}
          run-tests: 'true'
          api-mock: ${{ matrix.mode.api_mock }}
          login-mock: ${{ matrix.mode.login_mock }}
          vite-front-service-url: ${{ matrix.mode.vite_front_service_url }}
          vite-app-version: '${{ needs.get-version.outputs.version-name }}'
          vite-datadog-application-id: ${{ matrix.mode.vite-datadog-application-id }}
          vite-datadog-client-token: ${{ matrix.mode.vite-datadog-client-token }}
          vite-datadog-service: ${{ matrix.mode.vite-datadog-service }}
          vite-datadog-env: ${{ matrix.mode.vite-datadog-env }}
          vite-launchdarkly-client-id: ${{ matrix.mode.vite-launchdarkly-client-id }}
          vite-launchdarkly-env-key: ${{ matrix.mode.vite-launchdarkly-env-key }}
          vite-beamer-product-id: ${{ matrix.mode.vite-beamer-product-id }}
          vite-magicbell-api-key: ${{ matrix.mode.vite-magicbell-api-key }}
          vite-fake-launchdarkly: ${{ matrix.mode.vite-fake-launchdarkly }}

      - name: upload compressed build to s3
        shell: bash
        run: |
          dict_name="${{ matrix.mode.type }}.tar.gz"
          tar -czvf ${dict_name} ./dist/apps/dashboard
          aws s3 cp ${dict_name} s3://${{ env.BUCKET_NAME }}/frontend/${{ needs.get-version.outputs.version-name }}/$dict_name
          echo "### uploaded frontend build to s3: s3://${{ env.BUCKET_NAME }}/frontend/${{ needs.get-version.outputs.version-name }}/${dict_name}! :rocket:" >> $GITHUB_STEP_SUMMARY

  open-merge-request:
    needs: [build, get-version]
    if: github.event.pull_request.merged == true
    uses: primesec-ai/common/.github/workflows/eagle-merge-request.yaml@main
    secrets:
      ACTIONS_APP_SECRET: ${{ secrets.ACTIONS_APP_SECRET }}
