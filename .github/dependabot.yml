# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file
# aws codeartifact get-authorization-token --domain primesec --domain-owner 797808101759 --query authorizationToken --output text
version: 2

registries:
  main-repo:
    type: npm-registry
    url: 'https://primesec-797808101759.d.codeartifact.eu-central-1.amazonaws.com/npm/main-repo/'
    username: aws
    password: ${{ secrets.CODE_ARTIFACT_TOKEN_PRIVATE_REPOS }}
    replaces-base: true

updates:
  - package-ecosystem: 'npm'
    directory: '/'
    registries:
      - main-repo
    assignees:
      - 'matan-prime'
      - 'danny-prime'
      - 'ruslan-primesec'
    groups:
      all-dependencies:
        patterns:
          - '*'
    schedule:
      interval: 'weekly'
      day: 'sunday'
      time: '10:00'
