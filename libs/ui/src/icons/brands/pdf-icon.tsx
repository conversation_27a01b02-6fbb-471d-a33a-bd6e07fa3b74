export const PdfIcon = (
  props: JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>
) => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M4.39341 1.33356C3.34965 1.33356 2.5 2.18382 2.5 3.22754V17.7728C2.5 18.8165 3.34965 19.6668 4.39341 19.6668H15.5157C16.5595 19.6668 17.4091 18.8165 17.4091 17.7728V5.81255C17.4091 5.81255 17.4389 5.46233 17.2869 5.12153C17.1443 4.80208 16.9121 4.57973 16.9121 4.57973L16.9103 4.57739L14.1509 1.87071C14.1493 1.86916 14.1478 1.86763 14.1463 1.86612C14.1463 1.86612 13.9107 1.64178 13.5614 1.49192C13.1502 1.31546 12.7264 1.33408 12.7264 1.33408L12.7327 1.3335L4.39341 1.33356Z"
        fill="#FF2116"
      />
      <path
        d="M4.39341 1.95573H12.7327C12.7348 1.95575 12.7369 1.95575 12.739 1.95573C12.739 1.95573 13.0725 1.95896 13.3164 2.06363C13.5511 2.16437 13.7187 2.31788 13.7187 2.31788L13.7199 2.31906L16.4724 5.01942C16.4724 5.01942 16.6383 5.19451 16.7187 5.37469C16.7835 5.52012 16.7875 5.7862 16.7875 5.7862C16.7873 5.79059 16.7871 5.79499 16.787 5.79939V17.7728C16.787 18.4828 16.2256 19.0447 15.5157 19.0447H4.39341C3.68349 19.0447 3.12215 18.4828 3.12215 17.7728V3.22759C3.12215 2.51763 3.68349 1.95573 4.39341 1.95573Z"
        fill="#F5F5F5"
      />
      <path
        d="M6.2227 12.249C5.79465 11.821 6.25777 11.2328 7.51463 10.6082L8.30546 10.2151L8.61355 9.54104C8.78301 9.17029 9.03591 8.56534 9.17556 8.19671L9.42948 7.52648L9.2545 7.03048C9.03935 6.42059 8.96263 5.50409 9.09914 5.17453C9.28404 4.72814 9.88969 4.77394 10.1295 5.25245C10.3167 5.62616 10.2976 6.30294 10.0756 7.15647L9.89362 7.8562L10.0539 8.12833C10.1421 8.27799 10.3996 8.63337 10.6262 8.91804L11.0521 9.44778L11.582 9.37863C13.2655 9.15895 13.8421 9.53231 13.8421 10.0676C13.8421 10.7433 12.5202 10.799 11.4101 10.0194C11.1604 9.84396 10.9889 9.6697 10.9889 9.6697C10.9889 9.6697 10.2934 9.81127 9.95105 9.90355C9.59762 9.99881 9.4213 10.0585 8.9036 10.2332C8.9036 10.2332 8.72195 10.497 8.60359 10.6888C8.16323 11.4022 7.64905 11.9935 7.28185 12.2087C6.87072 12.4496 6.43971 12.466 6.2227 12.249ZM6.89447 12.009C7.13509 11.8603 7.6221 11.2843 7.95923 10.7497L8.09572 10.5333L7.47422 10.8458C6.51433 11.3284 6.0752 11.7832 6.30355 12.0584C6.43183 12.2129 6.58528 12.2001 6.89447 12.009ZM13.1295 10.2589C13.3648 10.0941 13.3307 9.76188 13.0646 9.62794C12.8575 9.52371 12.6907 9.5023 12.1527 9.51024C11.8222 9.53277 11.2906 9.59939 11.2006 9.61966C11.2006 9.61966 11.4926 9.82147 11.6222 9.89565C11.7948 9.99422 12.2142 10.1772 12.5204 10.2709C12.8224 10.3634 12.9971 10.3536 13.1295 10.2589ZM10.6225 9.21692C10.48 9.06719 10.2379 8.7547 10.0843 8.52248C9.88342 8.25906 9.7827 8.07321 9.7827 8.07321C9.7827 8.07321 9.63589 8.54542 9.51544 8.82958L9.13967 9.75838L9.03073 9.96906C9.03073 9.96906 9.60993 9.77915 9.90462 9.70218C10.2167 9.62065 10.8502 9.49609 10.8502 9.49609L10.6225 9.21692ZM9.81456 5.97695C9.85089 5.67208 9.86631 5.36766 9.76835 5.21414C9.49666 4.91712 9.16872 5.16481 9.22427 5.87096C9.24296 6.10851 9.30197 6.5146 9.38087 6.76489L9.52432 7.21997L9.62528 6.87722C9.68081 6.68871 9.76599 6.28359 9.81456 5.97695Z"
        fill="#FF2116"
      />
      <path
        d="M6.95065 14.2313H7.64561C7.86767 14.2313 8.04792 14.2525 8.18637 14.295C8.32481 14.3361 8.44132 14.4286 8.5359 14.5726C8.63049 14.7151 8.67778 14.8872 8.67778 15.0887C8.67778 15.2737 8.6394 15.4327 8.56263 15.5657C8.48587 15.6986 8.38238 15.7946 8.25216 15.8535C8.12331 15.9125 7.92456 15.9419 7.65589 15.9419H7.41533V17.0378H6.95065V14.2313ZM7.41533 14.5911V15.5698H7.64561C7.85122 15.5698 7.99309 15.5314 8.07123 15.4546C8.15073 15.3779 8.19048 15.2531 8.19048 15.0804C8.19048 14.9516 8.16444 14.8474 8.11235 14.7679C8.06026 14.687 8.00269 14.6377 7.93964 14.6199C7.87795 14.6007 7.77994 14.5911 7.64561 14.5911L7.41533 14.5911ZM9.03348 14.2313H9.6647C9.97038 14.2313 10.2144 14.2854 10.3967 14.3937C10.5804 14.502 10.7188 14.663 10.812 14.8769C10.9066 15.0907 10.9539 15.3278 10.9539 15.5883C10.9539 15.8624 10.9114 16.1071 10.8264 16.3223C10.7428 16.5362 10.6139 16.7089 10.4399 16.8405C10.2671 16.972 10.0204 17.0378 9.69966 17.0378H9.03348V14.2313ZM9.49816 14.6034V16.6657H9.69143C9.96147 16.6657 10.1575 16.5725 10.2795 16.3861C10.4015 16.1983 10.4625 15.9481 10.4625 15.6356C10.4625 14.9475 10.2055 14.6034 9.69143 14.6034H9.49816ZM11.4001 14.2313H12.9586V14.6034H11.8647V15.4423H12.7406V15.8145H11.8647V17.0378H11.4001V14.2313Z"
        fill="#2C2C2C"
      />
    </svg>
  )
}
