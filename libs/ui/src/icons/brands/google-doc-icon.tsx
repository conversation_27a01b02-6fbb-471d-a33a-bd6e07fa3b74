export const GoogleDocIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1012_141)">
        <mask
          id="mask0_1012_141"
          maskUnits="userSpaceOnUse"
          x="4"
          y="1"
          width="16"
          height="22"
        >
          <path
            d="M13.9423 1H5.49135C4.67111 1 4 1.675 4 2.5V21.5C4 22.325 4.67111 23 5.49135 23H18.4163C19.2366 23 19.9077 22.325 19.9077 21.5V7L13.9423 1Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_1012_141)">
          <path
            d="M13.9423 1H5.49135C4.67111 1 4 1.675 4 2.5V21.5C4 22.325 4.67111 23 5.49135 23H18.4163C19.2366 23 19.9077 22.325 19.9077 21.5V7L16.4279 4.5L13.9423 1Z"
            fill="#4285F4"
          />
        </g>
        <mask
          id="mask1_1012_141"
          maskUnits="userSpaceOnUse"
          x="4"
          y="1"
          width="16"
          height="22"
        >
          <path
            d="M13.9423 1H5.49135C4.67111 1 4 1.675 4 2.5V21.5C4 22.325 4.67111 23 5.49135 23H18.4163C19.2366 23 19.9077 22.325 19.9077 21.5V7L13.9423 1Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask1_1012_141)">
          <path
            d="M14.3785 6.56128L19.9077 12.1213V7.00003L14.3785 6.56128Z"
            fill="url(#paint0_linear_1012_141)"
          />
        </g>
        <mask
          id="mask2_1012_141"
          maskUnits="userSpaceOnUse"
          x="4"
          y="1"
          width="16"
          height="22"
        >
          <path
            d="M13.9423 1H5.49135C4.67111 1 4 1.675 4 2.5V21.5C4 22.325 4.67111 23 5.49135 23H18.4163C19.2366 23 19.9077 22.325 19.9077 21.5V7L13.9423 1Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask2_1012_141)">
          <path
            d="M7.97693 17H15.9308V16H7.97693V17ZM7.97693 19H13.9423V18H7.97693V19ZM7.97693 12V13H15.9308V12H7.97693ZM7.97693 15H15.9308V14H7.97693V15Z"
            fill="#F1F1F1"
          />
        </g>
        <mask
          id="mask3_1012_141"
          maskUnits="userSpaceOnUse"
          x="4"
          y="1"
          width="16"
          height="22"
        >
          <path
            d="M13.9423 1H5.49135C4.67111 1 4 1.675 4 2.5V21.5C4 22.325 4.67111 23 5.49135 23H18.4163C19.2366 23 19.9077 22.325 19.9077 21.5V7L13.9423 1Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask3_1012_141)">
          <path
            d="M13.9423 1V5.5C13.9423 6.32875 14.6097 7 15.4337 7H19.9077L13.9423 1Z"
            fill="#A1C2FA"
          />
        </g>
        <mask
          id="mask4_1012_141"
          maskUnits="userSpaceOnUse"
          x="4"
          y="1"
          width="16"
          height="22"
        >
          <path
            d="M13.9423 1H5.49135C4.67111 1 4 1.675 4 2.5V21.5C4 22.325 4.67111 23 5.49135 23H18.4163C19.2366 23 19.9077 22.325 19.9077 21.5V7L13.9423 1Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask4_1012_141)">
          <path
            d="M5.49135 1C4.67111 1 4 1.675 4 2.5V2.625C4 1.8 4.67111 1.125 5.49135 1.125H13.9423V1H5.49135Z"
            fill="white"
            fillOpacity="0.2"
          />
        </g>
        <mask
          id="mask5_1012_141"
          maskUnits="userSpaceOnUse"
          x="4"
          y="1"
          width="16"
          height="22"
        >
          <path
            d="M13.9423 1H5.49135C4.67111 1 4 1.675 4 2.5V21.5C4 22.325 4.67111 23 5.49135 23H18.4163C19.2366 23 19.9077 22.325 19.9077 21.5V7L13.9423 1Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask5_1012_141)">
          <path
            d="M18.4164 22.875H5.49136C4.67112 22.875 4.00002 22.2 4.00002 21.375V21.5C4.00002 22.325 4.67112 23 5.49136 23H18.4164C19.2366 23 19.9077 22.325 19.9077 21.5V21.375C19.9077 22.2 19.2366 22.875 18.4164 22.875Z"
            fill="#1A237E"
            fillOpacity="0.2"
          />
        </g>
        <mask
          id="mask6_1012_141"
          maskUnits="userSpaceOnUse"
          x="4"
          y="1"
          width="16"
          height="22"
        >
          <path
            d="M13.9423 1H5.49135C4.67111 1 4 1.675 4 2.5V21.5C4 22.325 4.67111 23 5.49135 23H18.4163C19.2366 23 19.9077 22.325 19.9077 21.5V7L13.9423 1Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask6_1012_141)">
          <path
            d="M15.4337 7C14.6097 7 13.9423 6.32875 13.9423 5.5V5.625C13.9423 6.45375 14.6097 7.125 15.4337 7.125H19.9077V7H15.4337Z"
            fill="#1A237E"
            fillOpacity="0.1"
          />
        </g>
        <path
          d="M13.9423 1H5.49135C4.67111 1 4 1.675 4 2.5V21.5C4 22.325 4.67111 23 5.49135 23H18.4163C19.2366 23 19.9077 22.325 19.9077 21.5V7L13.9423 1Z"
          fill="url(#paint1_radial_1012_141)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_1012_141"
          x1="290.867"
          y1="54.3"
          x2="290.867"
          y2="562.639"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#1A237E" stopOpacity="0.2" />
          <stop offset="1" stopColor="#1A237E" stopOpacity="0.02" />
        </linearGradient>
        <radialGradient
          id="paint1_radial_1012_141"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(54.3964 44.2308) scale(2565.09 2565.09)"
        >
          <stop stopColor="white" stopOpacity="0.1" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </radialGradient>
        <clipPath id="clip0_1012_141">
          <rect
            width="15.9077"
            height="22"
            fill="white"
            transform="translate(4 1)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
