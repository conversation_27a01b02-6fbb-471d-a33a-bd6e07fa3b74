export const ViewedIcn = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="18"
      viewBox="0 0 22 22"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.4498 11.5855C7.36699 11.8071 7.23632 12.0151 7.05783 12.1934C6.39353 12.8571 5.31834 12.8589 4.65632 12.1975C3.9943 11.5361 3.99615 10.4619 4.66045 9.79825C4.84017 9.6187 5.04997 9.48759 5.27353 9.40494L5.2889 9.38959C5.32311 9.38214 5.35726 9.37435 5.39136 9.36622C5.43027 9.35498 5.46949 9.34516 5.50896 9.33676C6.39776 9.10339 7.24013 8.63738 7.93891 7.93926C8.63805 7.24077 9.10461 6.39868 9.33807 5.5102C9.34632 5.47167 9.35593 5.43337 9.36688 5.39538C9.37149 5.37611 9.37599 5.35683 9.38038 5.33753L9.38363 5.34077C9.46409 5.09334 9.6028 4.86053 9.79972 4.6638C10.464 4.00012 11.5392 3.99827 12.2012 4.65967C12.8632 5.32106 12.8614 6.39525 12.1971 7.05893C12.0002 7.25566 11.7672 7.39424 11.5195 7.47463L11.5198 7.47495C11.5183 7.4753 11.5168 7.47565 11.5152 7.476C11.4463 7.49819 11.3762 7.51587 11.3054 7.52904C10.4339 7.76816 9.60785 8.23187 8.91972 8.91936C8.23223 9.60622 7.7683 10.4306 7.52877 11.3004C7.51541 11.3726 7.49735 11.444 7.47459 11.5143C7.46998 11.5343 7.46548 11.5542 7.4611 11.5742L7.4498 11.5855Z"
        fill="#F97316"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.65988 9.79867C5.32128 9.13665 6.39547 9.1385 7.05914 9.8028C7.25588 9.99972 7.39446 10.2327 7.47485 10.4804L7.47516 10.4801C7.47551 10.4816 7.47587 10.4831 7.47622 10.4847C7.4984 10.5536 7.51608 10.6237 7.52926 10.6945C7.76837 11.566 8.23209 12.392 8.91958 13.0802C9.60643 13.7677 10.4308 14.2316 11.3006 14.4711C11.3728 14.4845 11.4443 14.5025 11.5145 14.5253C11.5345 14.5299 11.5544 14.5344 11.5744 14.5388L11.5857 14.5501C11.8073 14.6329 12.0153 14.7636 12.1936 14.9421C12.8573 15.6064 12.8591 16.6816 12.1977 17.3436C11.5363 18.0056 10.4621 18.0037 9.79847 17.3394C9.61891 17.1597 9.4878 16.9499 9.40516 16.7264L9.3898 16.711C9.38235 16.6768 9.37456 16.6426 9.36643 16.6085C9.3552 16.5696 9.34538 16.5304 9.33698 16.491C9.10361 15.6021 8.6376 14.7598 7.93947 14.061C7.24098 13.3618 6.39889 12.8953 5.51042 12.6618C5.47189 12.6536 5.43359 12.644 5.39559 12.633C5.37633 12.6284 5.35705 12.6239 5.33774 12.6195L5.34099 12.6163C5.09356 12.5358 4.86075 12.3971 4.66401 12.2002C4.00033 11.5359 3.99848 10.4607 4.65988 9.79867Z"
        fill="#F97316"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.5924 16.7281C12.5096 16.9497 12.379 17.1577 12.2005 17.336C11.5362 17.9997 10.461 18.0016 9.79896 17.3402C9.13694 16.6788 9.13879 15.6046 9.80309 14.9409C9.98281 14.7613 10.1926 14.6302 10.4162 14.5476L10.4315 14.5322C10.4657 14.5248 10.4999 14.517 10.534 14.5089C10.5729 14.4976 10.6121 14.4878 10.6516 14.4794C11.5404 14.246 12.3828 13.78 13.0816 13.0819C13.7799 12.3842 14.2462 11.5432 14.4799 10.6558C14.4886 10.6152 14.4987 10.5748 14.5103 10.5348C14.5146 10.5166 14.5189 10.4984 14.523 10.4802L14.5261 10.4833C14.6066 10.2358 14.7453 10.003 14.9423 9.80624C15.6066 9.14256 16.6817 9.14071 17.3438 9.80211C18.0058 10.4635 18.0039 11.5377 17.3396 12.2014C17.1427 12.3981 16.9096 12.5367 16.6619 12.6171L16.6624 12.6176C16.66 12.6181 16.6576 12.6187 16.6552 12.6193C16.5879 12.6408 16.5196 12.658 16.4506 12.671C15.5781 12.9099 14.7512 13.3738 14.0624 14.062C13.3749 14.7489 12.9109 15.5733 12.6714 16.4431C12.658 16.5152 12.64 16.5867 12.6172 16.657C12.6126 16.6769 12.6081 16.6969 12.6037 16.7168L12.5924 16.7281Z"
        fill="#F97316"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.80236 4.65634C10.4638 3.99432 11.5379 3.99617 12.2016 4.66047C12.3983 4.85732 12.5368 5.09024 12.6172 5.33781L12.6178 5.33726C12.6184 5.33989 12.619 5.34252 12.6196 5.34516C12.6411 5.41223 12.6582 5.48036 12.6712 5.54913C12.9101 6.42166 13.374 7.24872 14.0622 7.93757C14.749 8.625 15.5733 9.08891 16.4431 9.32845C16.5153 9.34182 16.5868 9.3599 16.6571 9.38268C16.677 9.38728 16.6969 9.39178 16.7169 9.39615L16.7281 9.40743C16.9498 9.49025 17.1577 9.62091 17.3361 9.7994C17.9997 10.4637 18.0016 11.5389 17.3402 12.2009C16.6788 12.8629 15.6046 12.8611 14.9409 12.1968C14.7614 12.0171 14.6303 11.8073 14.5476 11.5837L14.5323 11.5683C14.5248 11.5341 14.517 11.5 14.5089 11.4659C14.4977 11.427 14.4878 11.3877 14.4794 11.3483C14.2461 10.4595 13.7801 9.6171 13.0819 8.91832C12.3851 8.22086 11.5454 7.75486 10.6592 7.52084C10.6164 7.51184 10.5739 7.50118 10.5318 7.48885C10.5146 7.48476 10.4974 7.48076 10.4802 7.47685L10.4832 7.47385C10.2359 7.39337 10.0032 7.25469 9.80649 7.05785C9.14281 6.39355 9.14096 5.31835 9.80236 4.65634Z"
        fill="#F97316"
      />
    </svg>
  )
}
