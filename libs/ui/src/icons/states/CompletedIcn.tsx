interface CompletedIcnProps {
  white?: boolean
}

export const CompletedIcn = ({ white }: CompletedIcnProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.40842 11.0785C1.23548 11.0132 1.07318 10.9099 0.934091 10.7687C0.41825 10.2448 0.416812 9.39693 0.93088 8.87488C1.44495 8.35282 2.27986 8.35428 2.7957 8.87813C2.93476 9.01936 3.03644 9.18414 3.10072 9.35974L3.11322 9.37244C3.11937 9.40111 3.12582 9.42973 3.13257 9.4583C3.14048 9.48641 3.14747 9.51472 3.15353 9.5432C3.33461 10.2454 3.69712 10.911 4.24066 11.463C4.78537 12.0162 5.44243 12.3847 6.13554 12.5682C6.16094 12.5739 6.18621 12.5803 6.21131 12.5875C6.22855 12.5917 6.24581 12.5958 6.26309 12.5998L6.26033 12.6026C6.4527 12.666 6.6337 12.7754 6.78665 12.9308C7.3025 13.4546 7.30393 14.3025 6.78986 14.8246C6.2758 15.3466 5.44089 15.3452 4.92505 14.8213C4.727 14.6202 4.60477 14.3713 4.55844 14.1114C4.3721 13.4264 4.01224 12.7773 3.47951 12.2363C2.9477 11.6963 2.30982 11.3311 1.63662 11.1416C1.57493 11.1305 1.51387 11.1148 1.45393 11.0947C1.44125 11.0917 1.42856 11.0889 1.41586 11.086L1.40842 11.0785Z"
        fill={white ? '#ffffff' : '#16A34A'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.40891 7.26616C1.23578 7.2009 1.07332 7.09756 0.93409 6.95618C0.418249 6.43232 0.416811 5.58443 0.930879 5.06237C1.44495 4.54032 2.27985 4.54178 2.7957 5.06563C2.93463 5.20673 3.03625 5.37133 3.10053 5.54674L3.11362 5.56003C3.12044 5.59184 3.12763 5.62359 3.13519 5.65527C3.14116 5.67706 3.14657 5.69897 3.15143 5.72099C3.33168 6.42667 3.695 7.09576 4.24097 7.65021C4.78836 8.2061 5.44921 8.57554 6.14609 8.7581C6.16447 8.76236 6.18278 8.76702 6.20101 8.77209C6.22169 8.7772 6.2424 8.78215 6.26314 8.78694L6.2601 8.79002C6.45256 8.85346 6.63364 8.96288 6.78665 9.11827C7.3025 9.64213 7.30393 10.49 6.78986 11.0121C6.2758 11.5341 5.44089 11.5327 4.92505 11.0088C4.72678 10.8075 4.60451 10.5582 4.55829 10.2981C4.3719 9.61329 4.0121 8.96439 3.47951 8.42352C2.94847 7.88423 2.31165 7.51939 1.63952 7.32964C1.57587 7.31826 1.51287 7.30213 1.45107 7.28123C1.43935 7.27851 1.42762 7.27585 1.41588 7.27323L1.40891 7.26616Z"
        fill={white ? '#ffffff' : '#16A34A'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.91156 9.14506C8.97583 8.96949 9.0775 8.80475 9.21654 8.66354C9.73239 8.13968 10.5673 8.13822 11.0814 8.66028C11.5954 9.18234 11.594 10.0302 11.0782 10.5541C10.939 10.6954 10.7767 10.7986 10.6037 10.8639L10.5904 10.8774C10.5565 10.8849 10.5226 10.8929 10.4888 10.9013C10.4731 10.9056 10.4573 10.9096 10.4415 10.9133C9.74337 11.0956 9.08119 11.4653 8.53288 12.0222C7.98705 12.5765 7.62378 13.2454 7.44348 13.9508C7.4385 13.9735 7.43292 13.9961 7.42676 14.0186C7.42221 14.0377 7.41779 14.0568 7.4135 14.076L7.41059 14.073C7.3481 14.2683 7.2404 14.4521 7.0875 14.6073C6.57166 15.1312 5.73675 15.1327 5.22269 14.6106C4.70862 14.0885 4.71005 13.2407 5.22589 12.7168C5.37879 12.5615 5.55973 12.4521 5.75203 12.3887L5.752 12.3887C5.75211 12.3886 5.75221 12.3886 5.75232 12.3886C5.80792 12.3703 5.86447 12.3558 5.92156 12.3451C6.59721 12.1561 7.23755 11.7904 7.77111 11.2485C8.30358 10.7078 8.66335 10.059 8.84977 9.3744C8.86058 9.3135 8.87556 9.2532 8.8947 9.19398C8.89779 9.18032 8.90081 9.16665 8.90376 9.15298L8.91156 9.14506Z"
        fill={white ? '#ffffff' : '#16A34A'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.6692 5.17183C12.7335 4.99626 12.8352 4.83151 12.9742 4.69031C13.49 4.16645 14.325 4.16499 14.839 4.68705C15.3531 5.2091 15.3516 6.05699 14.8358 6.58085C14.6967 6.72213 14.5344 6.82541 14.3614 6.89068L14.3481 6.90422C14.3139 6.91178 14.2798 6.91978 14.2458 6.92823C14.2305 6.93243 14.2151 6.93635 14.1996 6.93999C13.5012 7.12216 12.8387 7.49197 12.2902 8.04902C11.7459 8.60183 11.3831 9.2686 11.2023 9.97198C11.1966 9.99829 11.1902 10.0245 11.1829 10.0505C11.1788 10.0678 11.1748 10.0851 11.1709 10.1025L11.1682 10.0997C11.1057 10.2949 10.998 10.4786 10.8452 10.6338C10.3293 11.1577 9.49441 11.1591 8.98034 10.6371C8.46628 10.115 8.46771 9.26711 8.98355 8.74325C9.18182 8.5419 9.42723 8.41773 9.68344 8.37079C10.3575 8.18147 10.9963 7.81612 11.5288 7.27539C12.0612 6.73468 12.421 6.08598 12.6074 5.40143C12.6182 5.34037 12.6332 5.27991 12.6524 5.22054C12.6555 5.20694 12.6585 5.19333 12.6614 5.17972L12.6692 5.17183Z"
        fill={white ? '#ffffff' : '#16A34A'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.3768 5.40361C8.44106 5.22786 8.54279 5.06293 8.68197 4.92159C9.19781 4.39774 10.0327 4.39628 10.5468 4.91833C11.0609 5.44039 11.0594 6.28828 10.5436 6.81214C10.4046 6.95328 10.2424 7.0565 10.0696 7.12178L10.0559 7.13576C10.0161 7.14458 9.97633 7.154 9.93672 7.16402C9.93255 7.1651 9.92839 7.16617 9.92422 7.1672C9.21964 7.34781 8.55101 7.71907 7.99813 8.28054C7.45296 8.83419 7.08991 9.50215 6.90939 10.2067C6.90411 10.2309 6.89816 10.255 6.89155 10.2789C6.88717 10.2973 6.88292 10.3158 6.87879 10.3342L6.87593 10.3313C6.81344 10.5265 6.70576 10.7102 6.55292 10.8654C6.03708 11.3893 5.20218 11.3907 4.68811 10.8687C4.17404 10.3466 4.17548 9.49872 4.69132 8.97486C4.8895 8.7736 5.13477 8.64944 5.39085 8.60246C6.06513 8.41317 6.7041 8.04778 7.23668 7.50692C7.76861 6.96671 8.12819 6.31873 8.31476 5.63488C8.32574 5.57241 8.3411 5.51056 8.36083 5.44985C8.36374 5.43696 8.36659 5.42406 8.36937 5.41115L8.3768 5.40361Z"
        fill={white ? '#ffffff' : '#16A34A'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.1344 1.43007C12.1987 1.25453 12.3003 1.0898 12.4394 0.948607C12.9552 0.424747 13.7901 0.423288 14.3042 0.945346C14.8182 1.4674 14.8168 2.31529 14.301 2.83915C14.1618 2.98044 13.9995 3.08373 13.8265 3.149L13.8134 3.16228C13.7813 3.1694 13.7492 3.17691 13.7172 3.18481C13.6978 3.19023 13.6783 3.19519 13.6588 3.19969C12.9627 3.38245 12.3027 3.7517 11.7558 4.30702C11.2086 4.86275 10.8449 5.53365 10.6651 6.24112C10.6608 6.26012 10.6561 6.27905 10.651 6.29788C10.646 6.31887 10.6412 6.33988 10.6364 6.36092L10.6334 6.35781C10.5709 6.55309 10.4632 6.73683 10.3103 6.8921C9.79447 7.41596 8.95956 7.41742 8.44549 6.89536C7.93142 6.3733 7.93286 5.52542 8.4487 5.00156C8.6463 4.80089 8.8907 4.67688 9.14596 4.62958C9.82108 4.44048 10.4609 4.07487 10.9941 3.53339C11.5262 2.99296 11.8859 2.34467 12.0724 1.66051C12.0833 1.59887 12.0984 1.53786 12.1179 1.47794C12.1209 1.46459 12.1238 1.45123 12.1267 1.43785L12.1344 1.43007Z"
        fill={white ? '#ffffff' : '#16A34A'}
      />
    </svg>
  )
}
