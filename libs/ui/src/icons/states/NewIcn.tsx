export const NewIcn = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g opacity="0.5">
        <rect
          x="12"
          y="4.92871"
          width="10"
          height="10"
          transform="rotate(45 12 4.92871)"
          fill="#14B8A6"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M21.5105 13.8026C20.5184 14.7956 18.9071 14.7928 17.9116 13.7964C17.6165 13.501 17.4086 13.1514 17.288 12.7799L17.2875 12.7804C17.287 12.7781 17.2865 12.7759 17.286 12.7736C17.2527 12.6701 17.2262 12.565 17.2064 12.4588C16.8477 11.1515 16.1522 9.91248 15.1209 8.88029C14.0906 7.84905 12.8541 7.15316 11.5493 6.79386C11.4411 6.77382 11.3339 6.74673 11.2284 6.71259C11.1985 6.70567 11.1686 6.69893 11.1387 6.69236L11.1218 6.67541C10.7893 6.55119 10.4774 6.35519 10.2099 6.08745C9.21436 5.091 9.21159 3.47821 10.2037 2.48519C11.1958 1.49216 12.8071 1.49493 13.8026 2.49138C14.0719 2.76097 14.2686 3.07567 14.3925 3.411L14.4156 3.43406C14.4268 3.48538 14.4384 3.53662 14.4506 3.58777C14.4675 3.64612 14.4822 3.70494 14.4948 3.76412C14.8449 5.09734 15.5439 6.3609 16.5911 7.40908C17.6388 8.45779 18.902 9.15763 20.2347 9.50782C20.2925 9.5202 20.3499 9.5346 20.4069 9.55103C20.4358 9.55794 20.4647 9.56469 20.4937 9.57128L20.4888 9.57615C20.86 9.69684 21.2092 9.90491 21.5043 10.2003C22.4998 11.1967 22.5026 12.8095 21.5105 13.8026Z"
          fill="#14B8A6"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.8018 2.48978C14.7948 3.48188 14.7921 5.09316 13.7956 6.08868C13.5002 6.38378 13.1507 6.59165 12.7792 6.71223L12.7797 6.7127C12.7774 6.71323 12.7751 6.71376 12.7728 6.71428C12.6694 6.74757 12.5642 6.77409 12.4581 6.79384C11.1508 7.15252 9.91175 7.84809 8.87956 8.87932C7.84832 9.90961 7.15242 11.1462 6.79313 12.4509C6.77309 12.5592 6.746 12.6664 6.71185 12.7718C6.70494 12.8017 6.6982 12.8316 6.69163 12.8616L6.67468 12.8785C6.55046 13.2109 6.35446 13.5229 6.08672 13.7904C5.09027 14.7859 3.47748 14.7887 2.48445 13.7966C1.49143 12.8045 1.4942 11.1932 2.49065 10.1977C2.76024 9.92833 3.07494 9.73166 3.41027 9.6077L3.43332 9.58467C3.48465 9.57349 3.53589 9.5618 3.58703 9.5496C3.64538 9.53276 3.70421 9.51803 3.76339 9.50543C5.0966 9.15538 6.36017 8.45637 7.40834 7.40916C8.45706 6.36143 9.1569 5.09828 9.50709 3.76555C9.51947 3.70778 9.53387 3.65034 9.5503 3.59336C9.55721 3.56446 9.56396 3.53553 9.57055 3.50658L9.57542 3.51144C9.69611 3.14029 9.90418 2.79108 10.1996 2.49598C11.196 1.50046 12.8088 1.49769 13.8018 2.48978Z"
          fill="#14B8A6"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.40757 14.3892C3.07512 14.265 2.76318 14.069 2.4957 13.8012C1.50018 12.8048 1.4974 11.192 2.4895 10.199C3.4816 9.20595 5.09288 9.20873 6.08839 10.2052C6.35773 10.4748 6.5544 10.7895 6.67836 11.1248L6.70139 11.1478C6.71257 11.1992 6.72426 11.2504 6.73646 11.3016C6.7533 11.3599 6.76803 11.4187 6.78063 11.4779C7.13068 12.8111 7.82969 14.0747 8.87689 15.1229C9.92347 16.1704 11.185 16.8699 12.5161 17.2205C12.577 17.2334 12.6375 17.2486 12.6975 17.266C12.7248 17.2725 12.7521 17.2789 12.7795 17.2851L12.7748 17.2898C13.146 17.4104 13.4953 17.6185 13.7904 17.9139C14.7859 18.9104 14.7887 20.5232 13.7966 21.5162C12.8045 22.5092 11.1932 22.5064 10.1977 21.51C9.90255 21.2146 9.69467 20.865 9.5741 20.4935L9.57335 20.4942C9.57252 20.4906 9.57169 20.487 9.57085 20.4834C9.53856 20.3824 9.5127 20.2799 9.49327 20.1764C9.1349 18.8678 8.43897 17.6273 7.40673 16.5941C6.37645 15.5628 5.13987 14.8669 3.83514 14.5077C3.7269 14.4876 3.61969 14.4605 3.51424 14.4264C3.48436 14.4195 3.45444 14.4127 3.4245 14.4062L3.40757 14.3892Z"
          fill="#14B8A6"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.3888 20.5921C14.2646 20.9245 14.0686 21.2365 13.8009 21.504C12.8044 22.4995 11.1916 22.5023 10.1986 21.5102C9.20556 20.5181 9.20834 18.9068 10.2048 17.9113C10.4744 17.6419 10.7891 17.4453 11.1244 17.3213L11.1475 17.2983C11.1988 17.2871 11.25 17.2754 11.3012 17.2632C11.3595 17.2464 11.4183 17.2316 11.4775 17.219C12.8107 16.869 14.0743 16.17 15.1225 15.1228C16.1687 14.0775 16.8677 12.8179 17.2187 11.4888C17.2322 11.4245 17.2482 11.3607 17.2667 11.2975C17.2728 11.2718 17.2788 11.246 17.2847 11.2202L17.2892 11.2247C17.4099 10.8537 17.6179 10.5046 17.9132 10.2096C18.9096 9.2141 20.5224 9.21132 21.5155 10.2034C22.5085 11.1955 22.5057 12.8068 21.5093 13.8023C21.214 14.0973 20.8646 14.3051 20.4932 14.4257L20.4941 14.4265C20.4901 14.4275 20.4862 14.4284 20.4822 14.4293C20.3816 14.4615 20.2794 14.4872 20.1763 14.5066C18.8675 14.865 17.6269 15.5609 16.5936 16.5932C15.5625 17.6234 14.8666 18.8599 14.5073 20.1645C14.4872 20.2728 14.4601 20.3801 14.4259 20.4856C14.419 20.5154 14.4123 20.5453 14.4057 20.5752L14.3888 20.5921Z"
          fill="#14B8A6"
        />
      </g>
    </svg>
  )
}
