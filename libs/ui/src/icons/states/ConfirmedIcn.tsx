export const ConfirmedIcn = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.7248 1.41363C14.503 2.19106 14.5008 3.45369 13.7199 4.2338C13.4886 4.46491 13.2149 4.62774 12.924 4.72225L12.9246 4.7228C12.9219 4.72342 12.9193 4.72403 12.9166 4.72465C12.8368 4.75024 12.7557 4.7707 12.6738 4.78603C11.6487 5.06694 10.677 5.61218 9.86769 6.42077C9.05933 7.22838 8.51392 8.19777 8.23245 9.22055C8.21686 9.30431 8.19588 9.38727 8.16952 9.46889C8.16397 9.49285 8.15856 9.51683 8.15329 9.54084L8.13976 9.55436C8.0424 9.81467 7.88888 10.0589 7.67921 10.2684C6.89837 11.0485 5.63455 11.0507 4.85639 10.2733C4.07824 9.49583 4.08041 8.23319 4.86125 7.45308C5.07264 7.24189 5.31943 7.08771 5.5824 6.99058L5.60015 6.97284C5.63922 6.96433 5.67822 6.95545 5.71716 6.94618C5.76503 6.93228 5.81331 6.92019 5.86189 6.90993C6.90542 6.63534 7.89437 6.08786 8.71488 5.26811C9.53639 4.44737 10.0847 3.45795 10.3592 2.414C10.369 2.36821 10.3804 2.32269 10.3935 2.27754C10.3989 2.255 10.4041 2.23243 10.4093 2.20985L10.4131 2.21367C10.5077 1.92303 10.6707 1.64958 10.902 1.41848C11.6828 0.638371 12.9466 0.636197 13.7248 1.41363Z"
        fill="#06B6D4"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.8604 7.45293C5.63783 6.67477 6.90047 6.67694 7.68058 7.45778C7.91217 7.68959 8.0752 7.96396 8.16963 8.25556L8.16985 8.25534C8.17009 8.25639 8.17033 8.25743 8.17057 8.25848C8.19692 8.34026 8.21787 8.42339 8.23343 8.5073C8.5145 9.53165 9.05956 10.5026 9.86762 11.3114C10.6728 12.1173 11.6388 12.6618 12.6581 12.9441C12.7484 12.9604 12.8377 12.983 12.9255 13.0118C12.9466 13.0166 12.9677 13.0214 12.9888 13.026L13.0011 13.0383C13.2617 13.1357 13.5061 13.2893 13.7157 13.4991C14.4958 14.2799 14.498 15.5437 13.7206 16.3219C12.9432 17.1001 11.6805 17.0979 10.9004 16.317C10.6894 16.1058 10.5353 15.8592 10.4381 15.5964L10.4203 15.5786C10.4118 15.5395 10.4029 15.5005 10.3936 15.4615C10.3796 15.4134 10.3675 15.3649 10.3572 15.3162C10.0826 14.2728 9.53515 13.284 8.71551 12.4636C7.89103 11.6384 6.89632 11.0888 5.84712 10.8155C5.81059 10.8075 5.77424 10.7983 5.73812 10.7882C5.71148 10.7818 5.68481 10.7755 5.6581 10.7694L5.66229 10.7652C5.37096 10.6707 5.09685 10.5076 4.86526 10.2757C4.08515 9.4949 4.08297 8.23109 4.8604 7.45293Z"
        fill="#06B6D4"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.0458 6.99342C19.3065 7.09075 19.5511 7.2444 19.7608 7.45433C20.5409 8.23517 20.5431 9.49899 19.7657 10.2772C18.9883 11.0553 17.7256 11.0531 16.9455 10.2723C16.7346 10.0612 16.5805 9.81474 16.4834 9.55214L16.4651 9.53388C16.4563 9.49312 16.447 9.45242 16.4373 9.4118C16.4244 9.36689 16.413 9.32162 16.4033 9.27608C16.1291 8.23074 15.5812 7.23998 14.7602 6.41818C13.9445 5.60177 12.9623 5.05517 11.9255 4.77899C11.8666 4.76699 11.808 4.75232 11.7501 4.73496C11.7342 4.73121 11.7183 4.72752 11.7023 4.72389L11.7053 4.72094C11.4147 4.62632 11.1414 4.46336 10.9104 4.23212C10.1302 3.45128 10.1281 2.18746 10.9055 1.4093C11.6829 0.631139 12.9456 0.633314 13.7257 1.41415C13.9567 1.6454 14.1195 1.91901 14.214 2.20982L14.2144 2.20944C14.2148 2.21125 14.2153 2.21306 14.2157 2.21487C14.2419 2.29635 14.2628 2.37917 14.2784 2.46278C14.5595 3.48679 15.1045 4.45736 15.9123 5.26592C16.7192 6.0736 17.6876 6.61876 18.7094 6.90044C18.7954 6.91629 18.8806 6.93781 18.9643 6.965C18.9871 6.97027 19.01 6.97541 19.0328 6.98043L19.0458 6.99342Z"
        fill="#06B6D4"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.8953 22.5864C10.1172 21.8089 10.1193 20.5463 10.9002 19.7662C11.1315 19.5351 11.4052 19.3723 11.6961 19.2777L11.6955 19.2772C11.6982 19.2766 11.7009 19.276 11.7035 19.2754C11.7834 19.2498 11.8645 19.2293 11.9463 19.214C12.9714 18.9331 13.9431 18.3878 14.7524 17.5792C15.5608 16.7716 16.1062 15.8022 16.3877 14.7794C16.4033 14.6957 16.4242 14.6127 16.4506 14.5311C16.4561 14.5072 16.4616 14.4832 16.4668 14.4592L16.4804 14.4456C16.5777 14.1853 16.7312 13.9411 16.9409 13.7316C17.7217 12.9515 18.9856 12.9493 19.7637 13.7267C20.5419 14.5042 20.5397 15.7668 19.7589 16.5469C19.5475 16.7581 19.3007 16.9123 19.0377 17.0094L19.02 17.0272C18.9809 17.0357 18.9419 17.0446 18.9029 17.0538C18.8551 17.0677 18.8068 17.0798 18.7582 17.0901C17.7147 17.3647 16.7258 17.9121 15.9052 18.7319C15.0837 19.5526 14.5354 20.5421 14.2609 21.586C14.2511 21.6318 14.2397 21.6773 14.2266 21.7225C14.2213 21.745 14.216 21.7676 14.2109 21.7902L14.207 21.7863C14.1124 22.077 13.9494 22.3504 13.7181 22.5815C12.9373 23.3616 11.6735 23.3638 10.8953 22.5864Z"
        fill="#06B6D4"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.7597 16.5471C18.9823 17.3252 17.7196 17.3231 16.9395 16.5422C16.7079 16.3104 16.5449 16.036 16.4505 15.7444L16.4503 15.7447C16.45 15.7436 16.4498 15.7426 16.4495 15.7415C16.4232 15.6597 16.4023 15.5766 16.3867 15.4927C16.1056 14.4684 15.5606 13.4974 14.7525 12.6886C13.9473 11.8827 12.9814 11.3382 11.962 11.0559C11.8717 11.0396 11.7824 11.017 11.6946 10.9882C11.6735 10.9834 11.6524 10.9786 11.6313 10.974L11.619 10.9617C11.3585 10.8643 11.114 10.7107 10.9044 10.5009C10.1243 9.72008 10.1221 8.45626 10.8995 7.6781C11.677 6.89994 12.9396 6.90212 13.7197 7.68296C13.9307 7.8942 14.0849 8.1408 14.182 8.40356L14.1998 8.42139C14.2083 8.46049 14.2172 8.49953 14.2265 8.5385C14.2405 8.58657 14.2526 8.63506 14.2629 8.68386C14.5375 9.72722 15.085 10.716 15.9046 11.5364C16.7291 12.3616 17.7238 12.9112 18.773 13.1845C18.8095 13.1925 18.8459 13.2017 18.882 13.2118C18.9086 13.2182 18.9353 13.2245 18.962 13.2306L18.9578 13.2348C19.2492 13.3293 19.5233 13.4924 19.7549 13.7243C20.535 14.5051 20.5371 15.7689 19.7597 16.5471Z"
        fill="#06B6D4"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.57433 17.0066C5.31364 16.9093 5.06902 16.7556 4.85928 16.5457C4.07917 15.7648 4.077 14.501 4.85443 13.7228C5.63186 12.9447 6.8945 12.9469 7.67461 13.7277C7.88554 13.9388 8.03959 14.1853 8.13674 14.4479L8.15498 14.4661C8.16386 14.5069 8.17314 14.5476 8.18284 14.5882C8.19577 14.6331 8.20709 14.6784 8.21682 14.7239C8.49098 15.7693 9.03889 16.76 9.85993 17.5818C10.6756 18.3982 11.6578 18.9448 12.6946 19.221C12.7536 19.233 12.8121 19.2477 12.87 19.265C12.8859 19.2688 12.9018 19.2725 12.9178 19.2761L12.9148 19.2791C13.2054 19.3737 13.4787 19.5366 13.7098 19.7679C14.4899 20.5487 14.492 21.8125 13.7146 22.5907C12.9372 23.3689 11.6746 23.3667 10.8944 22.5858C10.6634 22.3546 10.5006 22.081 10.4061 21.7902L10.4057 21.7906C10.4053 21.7887 10.4049 21.7869 10.4044 21.7851C10.3782 21.7036 10.3573 21.6208 10.3418 21.5372C10.0606 20.5132 9.51564 19.5426 8.70783 18.7341C7.9009 17.9264 6.93249 17.3812 5.91068 17.0996C5.82469 17.0837 5.73953 17.0622 5.65578 17.035C5.63298 17.0297 5.61016 17.0246 5.58731 17.0196L5.57433 17.0066Z"
        fill="#06B6D4"
      />
    </svg>
  )
}
