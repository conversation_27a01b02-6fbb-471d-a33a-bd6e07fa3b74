export const PrimeIcn = ({ className }: { className?: string }) => {
  return (
    <svg
      data-testid="prime"
      className={className}
      width="77"
      height="24"
      viewBox="0 0 77 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.33828 1.32029C10.8193 2.85132 12.3506 5.47043 13.4812 8.58602C11.8021 6.02708 9.8949 3.86977 8.107 2.38177C8.55909 1.95216 8.96156 1.59834 9.33828 1.32029ZM12.5339 8.12794C10.967 5.87832 9.22167 4.0302 7.63215 2.84188C7.30001 3.16885 6.94121 3.53089 6.54814 3.92798C8.45229 4.90904 10.5381 6.34664 12.5339 8.12794ZM9.08429 15.4013C10.2124 18.5101 11.7394 21.1247 13.2174 22.6573C13.5958 22.3826 13.9995 22.0306 14.453 21.6013C12.6667 20.1133 10.7617 17.9577 9.08429 15.4013ZM10.0326 15.8608C11.5975 18.1075 13.3405 19.9538 14.9285 21.1421C15.2604 20.816 15.6191 20.4544 16.0122 20.0573C14.1097 19.0764 12.0262 17.6401 10.0326 15.8608ZM18.1 17.9478L19.1999 16.8365C19.2849 16.7505 19.3685 16.6661 19.4503 16.5833C17.3001 16.7042 14.754 16.5149 12.1136 15.9743C14.2288 16.9647 16.3116 17.634 18.1 17.9478ZM13.1044 15.6215C16.6045 15.4025 19.798 14.6503 22.0654 13.6263C21.6582 14.2734 21.0185 14.979 20.1463 15.8752C18.2085 16.1499 15.7306 16.0828 13.1044 15.6215ZM17.5823 18.4709C15.5752 18.0054 13.3205 17.1895 11.0649 16.0554C12.9028 17.6062 14.7946 18.8209 16.4755 19.5892L17.5823 18.4709ZM22.5429 12.5165C20.7558 13.7284 17.5905 14.6929 13.9713 15.0216C17.3742 13.8682 20.3238 12.2594 22.2503 10.6394C22.4835 11.0864 22.6001 11.5152 22.6001 11.9725C22.6001 12.1575 22.5811 12.3377 22.5429 12.5165ZM20.8111 8.76649C21.237 9.225 21.587 9.63308 21.8609 10.0153C20.3473 11.5161 17.7483 13.0695 14.6548 14.2155C17.1964 12.513 19.3377 10.5785 20.8111 8.76649ZM15.3034 12.2132C16.432 9.92146 17.2421 7.63068 17.7013 5.59447L18.8133 6.71791C18.0537 8.42329 16.8468 10.3457 15.3034 12.2132ZM15.2218 11.1565C16.209 9.00444 16.874 6.88534 17.182 5.06972L16.127 4.00371C16.0231 3.89877 15.9216 3.79622 15.8223 3.69607C15.9472 5.87878 15.7608 8.46942 15.2218 11.1565ZM6.08469 4.39626C7.76683 5.16438 9.66061 6.37993 11.5004 7.93223C9.2425 6.79699 6.98544 5.98067 4.97692 5.51552L6.08469 4.39626ZM4.45889 6.03892C6.24911 6.35227 8.3349 7.02215 10.4531 8.01399C7.80892 7.47254 5.25911 7.28334 3.10673 7.40525C3.20474 7.30608 3.30507 7.20471 3.40772 7.101L4.45889 6.03892ZM2.41079 8.11407C4.34978 7.83805 6.83078 7.90457 9.46051 8.36653C5.96632 8.58523 2.77768 9.33528 0.511114 10.3568C0.910398 9.70988 1.54362 9.00674 2.41079 8.11407ZM0.0524752 11.4513C0.0174915 11.6226 0 11.7954 0 11.9725C0 12.4331 0.118312 12.8648 0.354937 13.3153C2.28211 11.7066 5.21465 10.1119 8.59429 8.96636C4.99511 9.29318 1.8449 10.2489 0.0524752 11.4513ZM0.743014 13.9348C1.01633 14.3157 1.36507 14.7221 1.7892 15.1787C3.26137 13.3791 5.38896 11.4617 7.91053 9.7726C4.84306 10.909 2.26177 12.4458 0.743014 13.9348ZM11.7907 0.603068C12.9945 2.40615 13.9532 5.61116 14.2793 9.27724C13.1401 5.84598 11.5518 2.87084 9.95179 0.924215C10.4047 0.678189 10.8376 0.555176 11.3 0.555176C11.4667 0.555176 11.6295 0.57114 11.7907 0.603068ZM8.28647 14.7107C8.60908 18.3374 9.55075 21.5129 10.7363 23.3263C10.9215 23.3687 11.1083 23.3899 11.3 23.3899C11.746 23.3899 12.1646 23.2755 12.5999 23.0467C11.0046 21.0997 9.42233 18.132 8.28647 14.7107ZM20.3551 8.28542C19.1798 9.89639 17.3452 11.6675 15.1101 13.2568C16.8798 11.2328 18.3066 9.11688 19.2776 7.18709C19.6719 7.58547 20.0311 7.94898 20.3551 8.28542ZM9.64511 22.8377C9.00927 22.4256 8.31527 21.7828 7.43597 20.9091C7.17067 18.9571 7.23914 16.4697 7.69273 13.8344C7.90783 17.3449 8.643 20.5501 9.64511 22.8377ZM14.873 10.1532C14.655 6.59655 13.9032 3.35318 12.8807 1.06C13.5257 1.46246 14.226 2.10556 15.1172 2.98931C15.3971 4.95443 15.3335 7.47763 14.873 10.1532ZM2.24315 15.6577C2.56541 15.9922 2.9224 16.3536 3.31411 16.7494C4.28467 14.8333 5.70207 12.7368 7.45623 10.7304C5.24146 12.3052 3.41987 14.0585 2.24315 15.6577ZM7.26261 11.7745C5.73524 13.6226 4.53733 15.5245 3.77643 17.2165L4.87881 18.3304C5.34049 16.3099 6.14554 14.0427 7.26261 11.7745ZM7.3436 12.8325C6.36859 14.9582 5.70794 17.0516 5.39519 18.8521L6.47321 19.9413C6.56345 20.0325 6.6519 20.1219 6.73866 20.2094C6.62311 18.0444 6.81146 15.4855 7.3436 12.8325Z"
        fill="black"
      />
      <path
        d="M28.1408 7.28866H27.4303V5.99224H30.6354V6.86194C31.6849 6.16293 32.7385 5.75247 33.7881 5.75247C35.5844 5.75247 37.195 6.93103 37.195 10.3976C37.195 14.2056 35.3139 15.4492 32.8879 15.4492C32.2098 15.4492 31.3943 15.3314 30.6678 15.1769V17.4283H31.8222V18.741H27.4465V17.4283H28.1408C28.3951 17.4283 28.5808 17.4121 28.5808 17.0545V7.66255C28.5808 7.32117 28.3951 7.28866 28.1408 7.28866ZM30.6678 8.25999V13.6204C31.1764 13.7748 31.8707 13.9089 32.3793 13.9089C34.4986 13.9089 35.0597 12.6288 35.0597 10.4789C35.0597 8.32903 34.2644 7.35369 33.0938 7.35369C32.3833 7.35369 31.5154 7.6788 30.6678 8.25999Z"
        fill="black"
      />
      <path
        d="M39.6533 7.71538C39.6533 7.3415 39.4838 7.30492 39.1972 7.30492H38.5029V5.99224H41.6394V7.68287C43.1976 6.14668 44.5216 5.82155 44.7597 5.82155V8.10962C43.1652 8.39815 42.0794 8.80859 41.7403 8.89395V13.9455H42.8948V15.2419H38.5191V13.9455H39.2133C39.4838 13.9455 39.6533 13.913 39.6533 13.535V7.71538Z"
        fill="black"
      />
      <path
        d="M49.4019 13.9618C49.6037 13.9618 49.8258 13.9618 49.8944 13.9455V15.2947C49.4867 15.3638 49.0991 15.3964 48.7399 15.3964C47.4158 15.3964 46.6731 14.9005 46.6731 13.5026V7.71538C46.6731 7.3415 46.5036 7.28866 46.217 7.28866H45.5227V5.99224H48.7601V13.0758C48.7601 13.6407 48.8973 13.9618 49.406 13.9618H49.4019ZM46.6045 2.21679H48.7924V4.09436H46.6045V2.21679Z"
        fill="black"
      />
      <path
        d="M52.1509 7.69506C52.1509 7.33743 51.9652 7.30085 51.6947 7.30085H51.0005V5.98818H54.1692V6.92697C55.1864 6.22796 56.1875 5.78498 57.1886 5.78498C58.1898 5.78498 59.2069 6.1101 59.6833 7.0814C60.7691 6.28079 61.8187 5.76873 62.9409 5.76873C64.3659 5.76873 65.6697 6.50431 65.6697 8.51597V13.4334C65.6697 13.8601 65.7706 13.9455 66.146 13.9455H66.8242V15.2419H62.6704V13.9455H63.6029V8.99553C63.6029 7.91859 63.1467 7.47561 62.1456 7.47561C61.3989 7.47561 60.6521 7.83323 59.9578 8.27623V13.9455H61.0436V15.2419H56.9585V13.9455H57.8749V8.9793C57.8749 7.90235 57.4187 7.47561 56.3813 7.47561C55.6507 7.47561 54.924 7.84947 54.2298 8.29656V13.9455H55.3318V15.2419H51.0085V13.9455H51.7028C51.9733 13.9455 52.1589 13.913 52.1589 13.5513V7.69506H52.1509Z"
        fill="black"
      />
      <path
        d="M67.8333 10.5805C67.8333 7.40651 69.5288 5.7484 72.2414 5.7484C75.4789 5.7484 76.2092 7.93482 76.2092 10.2391V11.0763H70.0535C70.0535 13.0555 70.9337 13.8927 72.5806 13.8927C73.4602 13.8927 74.6469 13.7057 75.0914 12.389H76.0079V14.5754C75.0067 15.1037 73.3962 15.4451 72.4313 15.4451C69.4641 15.4451 67.8373 13.9618 67.8373 10.5805H67.8333ZM74.2231 9.76362C74.2231 8.56885 73.9167 7.21957 72.1888 7.21957C70.663 7.21957 70.0172 8.32903 70.0172 9.76362H74.2231Z"
        fill="black"
      />
    </svg>
  )
}
