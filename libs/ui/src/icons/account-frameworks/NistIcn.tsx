export const NistIcn = ({ className }: { className?: string }) => {
  return (
    <svg
      data-testid="nist"
      className={className}
      width="152"
      height="40"
      viewBox="0 0 152 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_194_52)">
        <path
          d="M10.5696 3.47942e-06C4.69759 3.47942e-06 0 4.6976 0 10.5696V39.9295H9.39519V10.5696C9.39519 9.63007 10.5696 9.16031 11.3917 9.74751L36.0305 36.7822C42.842 43.5937 54.1163 38.6612 54.1163 29.5009V-0.0939484H44.7211V29.5009C44.7211 30.4404 43.5467 30.9102 42.7716 30.323L18.2267 3.31181C15.8779 0.96301 13.9988 0.0234914 10.5931 0.0234914M58.7669 3.47942e-06V29.36C58.7669 32.1632 59.8805 34.8516 61.8626 36.8338C63.8448 38.816 66.5332 39.9295 69.3365 39.9295H108.749C112.02 39.9295 115.156 38.6304 117.469 36.3178C119.781 34.0053 121.08 30.8688 121.08 27.5984C121.08 24.3279 119.781 21.1914 117.469 18.8789C115.156 16.5664 112.02 15.2672 108.749 15.2672H84.0399C83.2613 15.2672 82.5145 14.9579 81.9639 14.4072C81.4133 13.8566 81.1039 13.1099 81.1039 12.3312C81.1039 11.5525 81.4133 10.8057 81.9639 10.2551C82.5145 9.70452 83.2613 9.39519 84.0399 9.39519H125.731V39.9295H135.126V9.39519H151.568V3.47942e-06H84.0399C80.7695 3.47942e-06 77.633 1.29918 75.3205 3.61172C73.0079 5.92427 71.7087 9.06075 71.7087 12.3312C71.7087 15.6016 73.0079 18.7381 75.3205 21.0506C77.633 23.3632 80.7695 24.6624 84.0399 24.6624H108.702C109.481 24.6624 110.228 24.9717 110.778 25.5223C111.329 26.0729 111.638 26.8197 111.638 27.5984C111.638 28.377 111.329 29.1238 110.778 29.6744C110.228 30.225 109.481 30.5344 108.702 30.5344H69.36C69.0485 30.5344 68.7498 30.4106 68.5295 30.1904C68.3093 29.9701 68.1856 29.6714 68.1856 29.36V3.47942e-06H58.7669Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_194_52">
          <rect width="151.732" height="40" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}
