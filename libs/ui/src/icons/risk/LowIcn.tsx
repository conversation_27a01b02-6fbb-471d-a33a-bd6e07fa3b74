interface LowIcnProps {
  gray?: boolean
}
export const LowIcn = ({ gray }: LowIcnProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.15685 8.02198L3.5 13.6788L6.99268 17.1715L11.9853 12.1789L16.9779 17.1715L20.4706 13.6788L14.8137 8.02198C13.2516 6.45988 10.719 6.45988 9.15685 8.02198Z"
        fill={gray ? '#9CA3AF' : '#F87171'}
      />
    </svg>
  )
}
