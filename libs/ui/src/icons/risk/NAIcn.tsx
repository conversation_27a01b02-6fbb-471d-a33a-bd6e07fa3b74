interface NAIcnProps {
  gray?: boolean
}
export const NAIcn = ({ gray }: NAIcnProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M4 10.9999C4 8.7908 5.79086 6.99994 8 6.99994L16 6.99994C18.2091 6.99994 20 8.7908 20 10.9999L4 10.9999Z"
        fill={gray ? '#9CA3AF' : '#D6D3D1'}
      />
      <path
        d="M20 12.9999C20 15.2091 18.2091 16.9999 16 16.9999L8 16.9999C5.79086 16.9999 4 15.2091 4 12.9999L20 12.9999Z"
        fill={gray ? '#9CA3AF' : '#D6D3D1'}
      />
    </svg>
  )
}
