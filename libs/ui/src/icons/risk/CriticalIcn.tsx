interface CriticalIcnProps {
  gray?: boolean
}
export const CriticalIcn = ({ gray }: CriticalIcnProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.15685 2.99994L3.5 8.6568L6.99268 12.1495L11.9853 7.15687L16.9779 12.1495L20.4706 8.6568L14.8137 2.99994C13.2516 1.43784 10.719 1.43784 9.15685 2.99994Z"
        fill={gray ? '#9CA3AF' : '#FCA5A5'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.15685 6.82837L3.5 12.4852L6.99268 15.9779L11.9853 10.9853L16.9779 15.9779L20.4706 12.4852L14.8137 6.82837C13.2516 5.26628 10.719 5.26628 9.15685 6.82837Z"
        fill={gray ? '#9CA3AF' : '#F87171'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.15685 10.8063L3.5 16.4631L6.99268 19.9558L11.9853 14.9632L16.9779 19.9558L20.4706 16.4631L14.8137 10.8063C13.2516 9.24418 10.719 9.24418 9.15685 10.8063Z"
        fill={gray ? '#9CA3AF' : '#EF4444'}
      />
    </svg>
  )
}
