interface HighIcnProps {
  gray?: boolean
}
export const HighIcn = ({ gray }: HighIcnProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.8236 6.82837L4.16675 12.4852L7.65942 15.9779L12.652 10.9853L17.6446 15.9779L21.1373 12.4852L15.4805 6.82837C13.9184 5.26628 11.3857 5.26628 9.8236 6.82837Z"
        fill={gray ? '#9CA3AF' : '#FCA5A5'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.8236 10.8063L4.16675 16.4631L7.65942 19.9558L12.652 14.9632L17.6446 19.9558L21.1373 16.4631L15.4805 10.8063C13.9184 9.24418 11.3857 9.24418 9.8236 10.8063Z"
        fill={gray ? '#9CA3AF' : '#EF4444'}
      />
    </svg>
  )
}
