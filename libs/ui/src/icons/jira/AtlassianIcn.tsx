export const AtlassianIcn = ({ className }: { className?: string }) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M8.32323 11.2985C8.05149 10.9995 7.64386 11.0266 7.45364 11.3799L3.05134 20.2117C2.88829 20.565 3.13286 20.9726 3.51331 20.9726H9.65479C9.84502 20.9726 10.0352 20.8639 10.1167 20.6737C11.4755 17.9291 10.6603 13.7713 8.32323 11.2985Z"
        fill="url(#paint0_linear_526_167)"
      />
      <path
        d="M11.5574 3.28201C9.08453 7.19517 9.24758 11.5159 10.8781 14.7769C12.5086 18.0378 13.7586 20.5108 13.8401 20.701C13.9216 20.8912 14.1119 21 14.3021 21H20.4435C20.824 21 21.0957 20.5923 20.9055 20.239C20.9055 20.239 12.6445 3.71683 12.427 3.30921C12.2912 2.90159 11.802 2.90157 11.5574 3.28201Z"
        fill="#2684FF"
      />
      <defs>
        <linearGradient
          id="paint0_linear_526_167"
          x1="10.7516"
          y1="12.6533"
          x2="6.09618"
          y2="20.7166"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#0052CC" />
          <stop offset="0.9228" stopColor="#2684FF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
