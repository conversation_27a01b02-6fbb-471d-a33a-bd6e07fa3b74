export const LinddunIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      data-testid="linddun-icn"
      className={className}
      width="123"
      height="20"
      viewBox="0 0 123 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <g clipPath="url(#clip0_580_616)">
        <rect x="-1" width="124" height="20" fill="url(#pattern0_580_616)" />
      </g>
      <defs>
        <pattern
          id="pattern0_580_616"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use
            xlinkHref="#image0_580_616"
            transform="matrix(0.00387913 0 0 0.0240385 -0.633115 -0.940385)"
          />
        </pattern>
        <clipPath id="clip0_580_616">
          <rect width="123" height="20" fill="white" />
        </clipPath>
        <image
          id="image0_580_616"
          width="443"
          height="125"
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  )
}
