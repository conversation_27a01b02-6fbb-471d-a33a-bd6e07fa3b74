export const MitreIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      data-testid="mitre-icn"
      className={className}
      width="191"
      height="20"
      viewBox="0 0 191 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_579_614)">
        <path
          d="M16.84 0.313354L12.1333 12.4267L7.38667 0.313354H3.63333L0 19.6934H3.87333L6.17333 7.55336L10.66 19.6934H13.5667L18.0467 7.55336L20.3533 19.6934H24.34L20.5933 0.313354H16.84Z"
          fill="#231F20"
        />
        <path
          d="M28.9467 0.313354H25.3133V19.6934H28.9467V0.313354Z"
          fill="#231F20"
        />
        <path
          d="M30.1333 3.46002H34.3933V19.6934H38V3.46002H42.2667V0.313354H30.1333V3.46002Z"
          fill="#231F20"
        />
        <path
          d="M53.1467 11.2334C54.1629 10.938 55.0829 10.3791 55.8133 9.61336C56.2807 9.11642 56.6403 8.52832 56.8698 7.88589C57.0992 7.24347 57.1935 6.56058 57.1467 5.88002C57.2348 4.55978 56.8015 3.25764 55.94 2.25335C55.2636 1.51997 54.4094 0.97347 53.46 0.666688C52.8962 0.479753 52.3121 0.361128 51.72 0.313354H43.0933V19.6934H46.7267V3.58002H50.9667C51.6423 3.56102 52.3 3.7993 52.8067 4.24669C53.028 4.46252 53.2017 4.72234 53.3166 5.0094C53.4314 5.29646 53.4848 5.60439 53.4733 5.91335C53.4846 6.24248 53.4173 6.56955 53.277 6.8675C53.1368 7.16545 52.9275 7.42569 52.6667 7.62669C52.2382 7.97042 51.7091 8.16472 51.16 8.18002H50.9667H47.36V11.6667L53.2667 19.6667H57.6333L51.1867 11.6C51.8525 11.5581 52.5107 11.435 53.1467 11.2334Z"
          fill="#231F20"
        />
        <path
          d="M58.1133 19.6934H69.3267V16.5467H61.7467V11.5734H69.26V8.42669H61.7467V3.46002H69.3267V0.313354H58.1133V19.6934Z"
          fill="#231F20"
        />
        <path
          d="M100.667 0.366669L92.74 19.64H96.0734L97.8467 15.08H106.073L107.9 19.64H111.233L103.447 0.366669H100.667ZM98.6667 12.6067L101.92 3.94L105.253 12.6067H98.6667Z"
          fill="#231F20"
        />
        <path
          d="M108.627 3.07333H113.053V19.64H116.127V3.07333H120.553V0.366669H108.627V3.07333Z"
          fill="#231F20"
        />
        <path
          d="M133.387 0.366669H121.46V3.07333H125.887V19.64H128.96V3.07333H133.387V0.366669Z"
          fill="#231F20"
        />
        <path
          d="M149.533 7.94667H146.667L144.4 12.4L141.067 9.01334C142.52 8.23334 144.5 7.08667 144.5 4.50667C144.491 3.90034 144.362 3.30177 144.12 2.74578C143.878 2.1898 143.528 1.6875 143.09 1.26812C142.652 0.848747 142.135 0.520659 141.569 0.302952C141.003 0.0852443 140.399 -0.0177354 139.793 4.41656e-06C139.179 -0.0132549 138.568 0.0950172 137.995 0.318603C137.423 0.542188 136.9 0.876682 136.457 1.30287C136.014 1.72906 135.66 2.23856 135.414 2.80209C135.169 3.36562 135.037 3.97209 135.027 4.58667C135.08 6.25334 135.94 7.50001 137.24 8.88001C135.527 9.79334 132.973 11.4067 132.973 14.5067C132.973 17.4267 135.34 20 139.147 20C142.247 20 143.727 18.44 144.767 16.4333L148.207 20L150.08 18.1733L146.227 14.24L149.533 7.94667ZM139.793 2.29334C140.084 2.28517 140.373 2.33599 140.643 2.44273C140.913 2.54947 141.158 2.70991 141.365 2.91434C141.571 3.11876 141.734 3.36292 141.843 3.63204C141.952 3.90116 142.006 4.18963 142 4.48001C142 5.94001 141.067 6.58667 139.473 7.47334C138.72 6.66667 137.707 5.75334 137.707 4.42667C137.7 4.14851 137.748 3.87173 137.85 3.61275C137.952 3.35376 138.104 3.11782 138.299 2.9189C138.494 2.71997 138.726 2.56212 138.983 2.45466C139.239 2.34721 139.515 2.29236 139.793 2.29334ZM139.247 17.4C138.82 17.4235 138.393 17.3602 137.992 17.214C137.59 17.0678 137.223 16.8418 136.911 16.5495C136.6 16.2573 136.351 15.9049 136.179 15.5137C136.008 15.1224 135.917 14.7005 135.913 14.2733C135.913 12.8667 136.773 11.64 138.907 10.5733L142.907 14.6667C142.087 16.3333 141.173 17.4 139.247 17.4Z"
          fill="#231F20"
        />
        <path
          d="M160.667 2.70669C161.901 2.71151 163.11 3.05067 164.167 3.6881C165.223 4.32552 166.087 5.23738 166.667 6.32669H169.92C169.211 4.45187 167.943 2.83993 166.288 1.70854C164.633 0.577145 162.671 -0.0192039 160.667 2.17949e-05C159.345 -0.021434 158.033 0.222171 156.807 0.716449C155.581 1.21073 154.466 1.94565 153.529 2.87783C152.592 3.81 151.851 4.92052 151.35 6.14379C150.85 7.36707 150.599 8.67829 150.613 10C150.605 11.3223 150.861 12.6328 151.366 13.855C151.87 15.0772 152.614 16.1863 153.553 17.1176C154.491 18.0489 155.606 18.7835 156.832 19.2784C158.059 19.7733 159.371 20.0187 160.693 20C162.675 20.0401 164.62 19.4629 166.258 18.3484C167.897 17.234 169.149 15.6375 169.84 13.78H166.607C166.011 14.8342 165.148 15.7135 164.106 16.33C163.064 16.9465 161.878 17.2787 160.667 17.2934C156.607 17.2934 153.687 13.96 153.687 9.96002C153.7 6.06669 156.567 2.70669 160.667 2.70669Z"
          fill="#231F20"
        />
        <path
          d="M184.773 0.366669H181.513L174.46 9.48V0.366669H171.387V19.64H174.46V10.76L181.753 19.64H185.527L177.273 9.97333L184.773 0.366669Z"
          fill="#231F20"
        />
        <path
          d="M81.1733 0.113342H80.6733V19.8867H81.1733V0.113342Z"
          fill="#231F20"
        />
        <path
          d="M81.1733 0.113342H80.6733V19.8867H81.1733V0.113342Z"
          fill="#231F20"
        />
        <path
          d="M188.28 0.173351C188.704 0.174669 189.119 0.301687 189.471 0.538366C189.823 0.775044 190.097 1.11077 190.258 1.50313C190.42 1.8955 190.462 2.32691 190.378 2.74288C190.294 3.15886 190.089 3.54073 189.789 3.84028C189.488 4.13983 189.106 4.34362 188.69 4.42591C188.273 4.5082 187.842 4.4653 187.45 4.30263C187.058 4.13996 186.724 3.86482 186.488 3.51195C186.252 3.15907 186.127 2.7443 186.127 2.32002C186.126 2.0373 186.181 1.75721 186.289 1.49593C186.397 1.23465 186.556 0.997357 186.756 0.797757C186.956 0.598156 187.194 0.440207 187.455 0.333034C187.717 0.225861 187.997 0.171588 188.28 0.173351ZM188.28 0.533351C188.047 0.533309 187.816 0.579741 187.601 0.669931C187.386 0.760121 187.192 0.89226 187.028 1.05862C186.865 1.22498 186.737 1.42222 186.65 1.6388C186.564 1.85539 186.522 2.08698 186.527 2.32002C186.527 2.55027 186.572 2.77826 186.66 2.99099C186.748 3.20371 186.877 3.397 187.04 3.55981C187.203 3.72262 187.396 3.85177 187.609 3.93989C187.822 4.028 188.05 4.07335 188.28 4.07335C188.51 4.07335 188.738 4.028 188.951 3.93989C189.164 3.85177 189.357 3.72262 189.52 3.55981C189.683 3.397 189.812 3.20371 189.9 2.99099C189.988 2.77826 190.033 2.55027 190.033 2.32002C190.037 2.08666 189.994 1.85492 189.907 1.6383C189.82 1.42168 189.691 1.2245 189.527 1.05824C189.364 0.891969 189.168 0.759939 188.953 0.669827C188.738 0.579716 188.507 0.533324 188.273 0.533351H188.28Z"
          fill="#231F20"
        />
        <path
          d="M189.227 1.79335C189.227 1.30002 188.927 1.08002 188.327 1.08002H187.387V3.48002H187.86V2.46002H188.16L188.7 3.48002H189.227L188.667 2.43335C188.746 2.43103 188.825 2.41226 188.897 2.37822C188.969 2.34418 189.034 2.2956 189.086 2.23553C189.139 2.17545 189.178 2.10517 189.203 2.02908C189.227 1.953 189.235 1.87275 189.227 1.79335ZM188.293 2.08668H187.86V1.45335H188.273C188.58 1.45335 188.74 1.52002 188.74 1.75335C188.74 1.98668 188.593 2.08668 188.293 2.08668Z"
          fill="#231F20"
        />
      </g>
      <defs>
        <clipPath id="clip0_579_614">
          <rect width="190.407" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}
