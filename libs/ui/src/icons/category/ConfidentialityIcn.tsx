export const ConfidentialityIcn = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12Z"
        fill="black"
      />
      <path
        d="M12.067 16.836C11.087 16.836 10.2423 16.626 9.53301 16.206C8.82368 15.7767 8.27768 15.184 7.89501 14.428C7.51235 13.6627 7.32101 12.7853 7.32101 11.796C7.32101 10.788 7.51235 9.906 7.89501 9.15C8.27768 8.38467 8.82368 7.78733 9.53301 7.358C10.2423 6.91933 11.087 6.7 12.067 6.7C13.2803 6.7 14.2697 7.00333 15.035 7.61C15.8097 8.21667 16.2903 9.07067 16.477 10.172H14.181C14.069 9.67733 13.831 9.29467 13.467 9.024C13.103 8.744 12.6317 8.604 12.053 8.604C11.5023 8.604 11.031 8.73467 10.639 8.996C10.2563 9.25733 9.96235 9.626 9.75702 10.102C9.56102 10.5687 9.46301 11.1333 9.46301 11.796C9.46301 12.44 9.56102 13 9.75702 13.476C9.96235 13.9427 10.2563 14.3067 10.639 14.568C11.031 14.82 11.5023 14.946 12.053 14.946C12.6317 14.946 13.0983 14.8247 13.453 14.582C13.817 14.33 14.0597 13.9707 14.181 13.504H16.477C16.2903 14.54 15.8097 15.3567 15.035 15.954C14.2697 16.542 13.2803 16.836 12.067 16.836Z"
        fill="black"
      />
    </svg>
  )
}
