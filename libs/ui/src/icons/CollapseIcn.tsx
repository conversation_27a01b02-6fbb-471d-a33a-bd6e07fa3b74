export const CollapseIcn = () => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.5" y="0.5" width="31" height="31" rx="11.5" fill="white" />
      <rect x="0.5" y="0.5" width="31" height="31" rx="11.5" stroke="#1E293B" />
      <g clipPath="url(#clip0_231_14365)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.83325 15.9999C6.83325 15.5397 7.20635 15.1666 7.66659 15.1666H12.6666C13.1268 15.1666 13.4999 15.5397 13.4999 15.9999C13.4999 16.4602 13.1268 16.8333 12.6666 16.8333H7.66659C7.20635 16.8333 6.83325 16.4602 6.83325 15.9999Z"
          fill="#1E293B"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.4999 15.9999C18.4999 15.5397 18.873 15.1666 19.3333 15.1666H24.3333C24.7935 15.1666 25.1666 15.5397 25.1666 15.9999C25.1666 16.4602 24.7935 16.8333 24.3333 16.8333H19.3333C18.873 16.8333 18.4999 16.4602 18.4999 15.9999Z"
          fill="#1E293B"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.9999 6.83325C16.4602 6.83325 16.8333 7.20635 16.8333 7.66659V9.33325C16.8333 9.79349 16.4602 10.1666 15.9999 10.1666C15.5397 10.1666 15.1666 9.79349 15.1666 9.33325V7.66659C15.1666 7.20635 15.5397 6.83325 15.9999 6.83325Z"
          fill="#1E293B"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.9999 11.8333C16.4602 11.8333 16.8333 12.2063 16.8333 12.6666V14.3333C16.8333 14.7935 16.4602 15.1666 15.9999 15.1666C15.5397 15.1666 15.1666 14.7935 15.1666 14.3333V12.6666C15.1666 12.2063 15.5397 11.8333 15.9999 11.8333Z"
          fill="#1E293B"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.9999 16.8333C16.4602 16.8333 16.8333 17.2063 16.8333 17.6666V19.3333C16.8333 19.7935 16.4602 20.1666 15.9999 20.1666C15.5397 20.1666 15.1666 19.7935 15.1666 19.3333V17.6666C15.1666 17.2063 15.5397 16.8333 15.9999 16.8333Z"
          fill="#1E293B"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.9999 21.8333C16.4602 21.8333 16.8333 22.2063 16.8333 22.6666V24.3333C16.8333 24.7935 16.4602 25.1666 15.9999 25.1666C15.5397 25.1666 15.1666 24.7935 15.1666 24.3333V22.6666C15.1666 22.2063 15.5397 21.8333 15.9999 21.8333Z"
          fill="#1E293B"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M22.4225 12.9107C22.7479 13.2361 22.7479 13.7637 22.4225 14.0892L20.5118 15.9999L22.4225 17.9107C22.7479 18.2361 22.7479 18.7637 22.4225 19.0892C22.0971 19.4146 21.5694 19.4146 21.244 19.0892L18.744 16.5892C18.4186 16.2637 18.4186 15.7361 18.744 15.4107L21.244 12.9107C21.5694 12.5852 22.0971 12.5852 22.4225 12.9107Z"
          fill="#1E293B"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.57733 12.9107C9.90277 12.5852 10.4304 12.5852 10.7558 12.9107L13.2558 15.4107C13.5813 15.7361 13.5813 16.2637 13.2558 16.5892L10.7558 19.0892C10.4304 19.4146 9.90277 19.4146 9.57733 19.0892C9.25189 18.7637 9.25189 18.2361 9.57733 17.9107L11.4881 15.9999L9.57733 14.0892C9.25189 13.7637 9.25189 13.2361 9.57733 12.9107Z"
          fill="#1E293B"
        />
      </g>
      <defs>
        <clipPath id="clip0_231_14365">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(6 6)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
