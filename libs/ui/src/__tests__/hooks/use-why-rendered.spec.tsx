import type { MockInstance } from 'vitest'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useWhyRendered } from '../../hooks/use-why-rendered'

describe('useWhyRendered', () => {
  let consoleLogSpy: MockInstance

  beforeEach(() => {
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {
      // Do nothing
    })
  })

  afterEach(() => {
    consoleLogSpy.mockRestore()
  })

  it('should log multiple changes', () => {
    const { rerender } = renderHook(
      ({ props }: { props: Record<string, any> }) =>
        useWhyRendered('TestComponent', props),
      { initialProps: { props: { prop1: 'value1', prop2: 'value2' } } }
    )

    rerender({ props: { prop1: 'newValue1', prop2: 'newValue2' } })

    expect(consoleLogSpy).toHaveBeenCalledWith(
      '[why-rendered] TestComponent:',
      {
        prop1: {
          from: 'value1',
          to: 'newValue1',
        },
        prop2: {
          from: 'value2',
          to: 'newValue2',
        },
      }
    )
  })

  it('should not log when props remain the same', () => {
    const { rerender } = renderHook(
      ({ props }: { props: Record<string, any> }) =>
        useWhyRendered('TestComponent', props),
      { initialProps: { props: { prop1: 'value1', prop2: 'value2' } } }
    )

    rerender({ props: { prop1: 'value1', prop2: 'value2' } })

    expect(consoleLogSpy).not.toHaveBeenCalled()
  })

  it('should handle addition of new props', () => {
    const { rerender } = renderHook(
      ({ props }: { props: Record<string, any> }) =>
        useWhyRendered('TestComponent', props),
      { initialProps: { props: { prop1: 'value1' } } }
    )

    rerender({ props: { prop1: 'value1', prop2: 'value2' } })

    expect(consoleLogSpy).toHaveBeenCalledWith(
      '[why-rendered] TestComponent:',
      {
        prop2: {
          from: undefined,
          to: 'value2',
        },
      }
    )
  })

  it('should handle removal of props', () => {
    const { rerender } = renderHook(
      ({ props }: { props: Record<string, any> }) =>
        useWhyRendered('TestComponent', props),
      { initialProps: { props: { prop1: 'value1', prop2: 'value2' } } }
    )

    rerender({ props: { prop1: 'value1' } })

    expect(consoleLogSpy).toHaveBeenCalledWith(
      '[why-rendered] TestComponent:',
      {
        prop2: {
          from: 'value2',
          to: undefined,
        },
      }
    )
  })
})
