import { describe, it, expect, vi, beforeEach } from 'vitest'
import * as ReactRouterDom from 'react-router'
import { act, renderHook } from '@testing-library/react'
import { useUrlState } from '../../hooks/use-url-state'
import type { Location, NavigateFunction } from 'react-router'

type NavParams = {
  hash: string
  search: string
  pathname?: string
}

vi.mock('react-router', () => ({
  useLocation: vi.fn(),
  useNavigate: vi.fn(),
  MemoryRouter: vi.fn(),
}))

describe('useUrlState', () => {
  let mockLocation: Location
  let mockNavigate: NavigateFunction

  beforeEach(() => {
    mockLocation = {
      pathname: '/',
      search: '',
      hash: '',
      state: null,
      key: 'default',
    }
    mockNavigate = vi.fn() as unknown as NavigateFunction

    vi.mocked(ReactRouterDom.useLocation).mockReturnValue(mockLocation)
    vi.mocked(ReactRouterDom.useNavigate).mockReturnValue(mockNavigate)
  })

  // Basic functionality tests
  describe('basic functionality', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useUrlState({ count: 0 }))
      expect(result.current[0]).toEqual({ count: 0 })
    })

    it('should update state and URL when setState is called', () => {
      const { result } = renderHook(() => useUrlState({ count: 0 }))

      act(() => {
        result.current[1]({ count: 1 })
      })

      expect(mockNavigate).toHaveBeenCalledWith(
        { hash: '', search: 'count=1', pathname: '/' },
        { replace: false, state: null }
      )
    })

    it('should handle custom path option', () => {
      const { result } = renderHook(() =>
        useUrlState({ count: 0 }, { path: '/custom' })
      )

      act(() => {
        result.current[1]({ count: 1 })
      })

      expect(mockNavigate).toHaveBeenCalledWith(
        { hash: '', search: 'count=1', pathname: '/custom' },
        { replace: false, state: null }
      )
    })

    it('should handle function updates', () => {
      const { result } = renderHook(() => useUrlState({ count: 0 }))

      act(() => {
        result.current[1]((prevState) => ({ count: prevState.count + 1 }))
      })

      expect(mockNavigate).toHaveBeenCalledWith(
        { hash: '', search: 'count=1', pathname: '/' },
        { replace: false, state: null }
      )
    })
  })

  // Special characters handling tests
  describe('special characters handling', () => {
    it('should preserve spaces in parameter values', () => {
      const { result } = renderHook(() => useUrlState({ message: '' }))

      act(() => {
        result.current[1]({ message: 'hello world' })
      })

      const mockCalls = vi.mocked(mockNavigate).mock.calls
      const lastNavigateCall = mockCalls[mockCalls.length - 1][0]
      expect((lastNavigateCall as unknown as NavParams).search).toBe(
        'message=hello world'
      )
    })

    it('should preserve most special characters', () => {
      const { result } = renderHook(() => useUrlState({ query: '' }))
      const specialChars = '@#$%^*()'

      act(() => {
        result.current[1]({ query: specialChars })
      })

      const mockCalls = vi.mocked(mockNavigate).mock.calls
      const lastNavigateCall = mockCalls[mockCalls.length - 1][0]

      expect((lastNavigateCall as unknown as NavParams).search).toBe(
        `query=${specialChars}`
      )
    })

    it('should only encode ampersands', () => {
      const { result } = renderHook(() => useUrlState({ text: '' }))

      act(() => {
        result.current[1]({ text: 'this&that@#' })
      })

      const mockCalls = vi.mocked(mockNavigate).mock.calls
      const lastNavigateCall = mockCalls[mockCalls.length - 1][0]
      expect((lastNavigateCall as unknown as NavParams).search).toBe(
        'text=this%26that@#'
      )
    })

    it('should handle multiple special characters including ampersands', () => {
      const { result } = renderHook(() => useUrlState({ text: '' }))

      act(() => {
        result.current[1]({ text: '@test&value#$%' })
      })

      const mockCalls = vi.mocked(mockNavigate).mock.calls
      const lastNavigateCall = mockCalls[mockCalls.length - 1][0]
      expect((lastNavigateCall as unknown as NavParams).search).toBe(
        'text=@test%26value#$%'
      )
    })

    it('should handle combination of spaces and special characters', () => {
      const { result } = renderHook(() => useUrlState({ query: '' }))

      act(() => {
        result.current[1]({ query: 'hello world & goodbye world @ #special' })
      })

      const mockCalls = vi.mocked(mockNavigate).mock.calls
      const lastNavigateCall = mockCalls[mockCalls.length - 1][0]
      expect((lastNavigateCall as unknown as NavParams).search).toBe(
        'query=hello world %26 goodbye world @ #special'
      )
    })
  })

  // Array and object handling tests
  describe('array and object handling', () => {
    it('should handle arrays with ampersands while maintaining readability', () => {
      const { result } = renderHook(() => useUrlState({}))
      const stateWithAmpersand = {
        f: [{ field: 'title', op: 'eq', value: 'rus&' }],
      }

      act(() => {
        result.current[1](stateWithAmpersand)
      })

      const navigateCall = vi.mocked(mockNavigate).mock.calls[0][0]
      expect((navigateCall as unknown as NavParams).search).toContain('rus%26')
      expect((navigateCall as unknown as NavParams).search).toContain('field')
      expect((navigateCall as unknown as NavParams).search).toContain('title')
      expect((navigateCall as unknown as NavParams).search).not.toContain(
        '%22field%22'
      )
    })

    it('should handle nested objects with special characters', () => {
      const { result } = renderHook(() => useUrlState({}))
      const complexState = {
        f: [
          { field: 'title', op: 'eq', value: 'test&value' },
          { field: 'status', op: 'in', value: ['pending&', 'done'] },
        ],
      }

      act(() => {
        result.current[1](complexState)
      })

      const navigateCall = vi.mocked(mockNavigate).mock.calls[0][0]
      expect((navigateCall as unknown as NavParams).search).toContain(
        'test%26value'
      )
      expect((navigateCall as unknown as NavParams).search).toContain(
        'pending%26'
      )
      expect((navigateCall as unknown as NavParams).search).toContain('done')
    })

    it('should handle empty arrays', () => {
      const { result } = renderHook(() => useUrlState({}))

      act(() => {
        result.current[1]({ f: [] })
      })

      const navigateCall = vi.mocked(mockNavigate).mock.calls[0][0]
      expect((navigateCall as unknown as NavParams).search).toBe('f=[]')
    })
  })

  // State restoration tests
  describe('state restoration', () => {
    it('should correctly restore state from URL with encoded ampersands', () => {
      const expectedState = {
        f: [{ field: 'title', op: 'eq', value: 'test&value' }],
      }

      mockLocation.search = `f=[{"field":"title","op":"eq","value":"test%26value"}]`

      const { result } = renderHook(() => useUrlState({}))
      expect(result.current[0]).toEqual(expectedState)
    })

    it('should handle multiple parameters with mixed content', () => {
      const { result } = renderHook(() => useUrlState({}))
      const multiParamState = {
        f: [{ field: 'title', op: 'eq', value: 'test&' }],
        sort: 'name&desc',
        filter: 'status=pending',
      }

      act(() => {
        result.current[1](multiParamState)
      })

      const navigateCall = vi.mocked(mockNavigate).mock.calls[0][0]
      expect((navigateCall as unknown as NavParams).search).toContain(
        'sort=name%26desc'
      )
      expect((navigateCall as unknown as NavParams).search).toContain(
        'filter=status=pending'
      )
      expect((navigateCall as unknown as NavParams).search).toContain('test%26')
    })
  })
})
