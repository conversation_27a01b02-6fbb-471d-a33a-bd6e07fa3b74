import { useIsFocused } from '../../hooks/use-is-focused'
import { fireEvent, render } from '@testing-library/react'
import { describe, it } from 'vitest'

const TestComponent = () => {
  const [ref, isFocused] = useIsFocused<HTMLDivElement>()
  return (
    <div>
      <div ref={ref} tabIndex={0}>
        Focus me
      </div>
      <span>{isFocused ? 'Focused' : 'Not Focused'}</span>
    </div>
  )
}

describe('useIsFocused', () => {
  it('should return false initially', () => {
    const { getByText } = render(<TestComponent />)
    expect(getByText('Not Focused')).toBeTruthy()
  })

  it('should return true when the element is focused', () => {
    const { getByText } = render(<TestComponent />)
    const element = getByText('Focus me')
    fireEvent.focus(element)
    expect(getByText('Focused')).toBeTruthy()
  })

  it('should return false when the element is blurred', () => {
    const { getByText } = render(<TestComponent />)
    const element = getByText('Focus me')
    fireEvent.focus(element)
    fireEvent.blur(element)
    expect(getByText('Not Focused')).toBeTruthy()
  })
})
