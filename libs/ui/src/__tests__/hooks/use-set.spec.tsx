import { describe, expect, it } from 'vitest'
import { useSet } from '../../hooks/use-set'
import { act, renderHook } from '@testing-library/react'

describe('useSet', () => {
  it('adds an item to the set', () => {
    const { result } = renderHook(() => useSet(new Set()))
    act(() => {
      result.current.add('item1')
    })
    expect(result.current.values).toContain('item1')
  })

  it('removes an item from the set', () => {
    const { result } = renderHook(() => useSet(new Set(['item1'])))
    act(() => {
      result.current.remove('item1')
    })
    expect(result.current.values).not.toContain('item1')
  })

  it('toggles an item in the set', () => {
    const { result } = renderHook(() => useSet(new Set(['item1'])))
    act(() => {
      result.current.toggle('item1')
    })
    expect(result.current.values).not.toContain('item1')
    act(() => {
      result.current.toggle('item1')
    })
    expect(result.current.values).toContain('item1')
  })

  it('resets the set to the initial state', () => {
    const { result } = renderHook(() => useSet(new Set(['item1'])))
    act(() => {
      result.current.add('item2')
      result.current.reset()
    })
    expect(result.current.values).toEqual(['item1'])
  })

  it('clears the set', () => {
    const { result } = renderHook(() => useSet(new Set(['item1'])))
    act(() => {
      result.current.clear()
    })
    expect(result.current.values).toEqual([])
  })
})
