import { useDisclosure } from '../../hooks/use-disclosure'
import { renderHook, act } from '@testing-library/react'
import { describe, expect, test } from 'vitest'

describe('useDisclosure', () => {
  test('should initialize with default state (false)', () => {
    const { result } = renderHook(() => useDisclosure())
    expect(result.current.isOpen).toBe(false)
  })

  test('should initialize with custom state', () => {
    const { result } = renderHook(() => useDisclosure(true))
    expect(result.current.isOpen).toBe(true)
  })

  test('onOpen should set isOpen to true', () => {
    const { result } = renderHook(() => useDisclosure())
    act(() => {
      result.current.onOpen()
    })
    expect(result.current.isOpen).toBe(true)
  })

  test('onClose should set isOpen to false', () => {
    const { result } = renderHook(() => useDisclosure(true))
    act(() => {
      result.current.onClose()
    })
    expect(result.current.isOpen).toBe(false)
  })

  test('onToggle should toggle state', () => {
    const { result } = renderHook(() => useDisclosure())
    act(() => {
      result.current.onToggle()
    })
    expect(result.current.isOpen).toBe(true)

    act(() => {
      result.current.onToggle()
    })
    expect(result.current.isOpen).toBe(false)
  })

  test('should maintain consistent function references', () => {
    const { result, rerender } = renderHook(() => useDisclosure())
    const { onOpen, onClose, onToggle } = result.current

    rerender()

    expect(result.current.onOpen).toBe(onOpen)
    expect(result.current.onClose).toBe(onClose)
    expect(result.current.onToggle).toBe(onToggle)
  })
})
