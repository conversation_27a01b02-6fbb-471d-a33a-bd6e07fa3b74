import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { StateBadge } from '../../../components/custom/state-badge'
import { CaseStatus } from 'prime-front-service-client'

describe('StateBadge component', () => {
  it('renders status', () => {
    const status = CaseStatus.open
    const { getByText } = render(<StateBadge state={status} />)

    expect(getByText(status)).toBeInTheDocument()
  })
})
