import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { AlertNotFound } from '../../../../components/custom/alerts/alert-not-found'

describe('AlertNotFound component', () => {
  it('renders title and description correctly', () => {
    const title = 'Not Found'
    const description = 'The requested resource could not be found.'

    const { getByText } = render(
      <AlertNotFound title={title} description={description} />
    )

    expect(getByText(title)).toBeInTheDocument()
    expect(getByText(description)).toBeInTheDocument()
  })

  it('renders CircleXIcon', () => {
    const title = 'Not Found'
    const description = 'The requested resource could not be found.'

    const { getByTestId } = render(
      <AlertNotFound title={title} description={description} />
    )

    expect(getByTestId('circle-x-icon')).toBeInTheDocument()
  })
})
