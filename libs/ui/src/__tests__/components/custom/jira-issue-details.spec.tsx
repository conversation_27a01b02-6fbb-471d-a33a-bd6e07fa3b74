import '@testing-library/jest-dom/vitest'
import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { JiraIssueDetails } from '../../../components/custom/jira-issue-details'
import { MemoryRouter } from 'react-router'

describe('JiraIssueDetails component', () => {
  it('should render Jira issue details correctly', () => {
    const issueNumber = 'JIRA-123'
    const issueLink = 'link'
    const dynamicFields = {
      assignee: '<PERSON>',
      issueType: 'Bug',
      issueStatus: 'In Progress',
      creationDate: new Date('2024-04-28T12:00:00').toLocaleDateString(),
      creator: '<PERSON>',
    }
    const { getByText } = render(
      <MemoryRouter>
        <JiraIssueDetails
          issueNumber={issueNumber}
          issueLink={issueLink}
          customDetails={dynamicFields}
        />
      </MemoryRouter>
    )

    expect(getByText(issueNumber)).toBeInTheDocument()
    expect(getByText(dynamicFields.issueStatus)).toBeInTheDocument()
    expect(getByText('JIRA-123')).toBeInTheDocument()
    expect(getByText(dynamicFields.creationDate)).toBeInTheDocument()
    expect(getByText(dynamicFields.issueType)).toBeInTheDocument()
    expect(getByText(dynamicFields.creator)).toBeInTheDocument()
    expect(getByText(dynamicFields.assignee)).toBeInTheDocument()
  })
})
