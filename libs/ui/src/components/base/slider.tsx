import * as React from 'react'
import * as SliderPrimitive from '@radix-ui/react-slider'
import { cn } from '@libs/common'

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, ...props }, ref) => {
  return (
    <div className="slider-wrapper">
      <SliderPrimitive.Root
        ref={ref}
        className={cn(
          'relative flex w-full touch-none select-none items-center',
          className
        )}
        {...props}
      >
        <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-secondary">
          <SliderPrimitive.Range className="absolute h-full bg-primary" />
        </SliderPrimitive.Track>

        {props.defaultValue?.map((val, index) => (
          <div className="thumb-wrapper flex items-center" key={index}>
            <SliderPrimitive.Thumb className="cursor-grab active:cursor-grabbing relative block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
              <div className=" absolute -top-5  text-xs text-slate-700 font-bold">
                {val}
              </div>
            </SliderPrimitive.Thumb>
          </div>
        ))}
      </SliderPrimitive.Root>
      <div className="flex justify-between mt-3 text-slate-400 text-xs ">
        <div>{props.value?.[0] ?? props.min}</div>
        <div>{props.value?.[1] ?? props.max}</div>
      </div>
    </div>
  )
})
Slider.displayName = SliderPrimitive.Root.displayName

export { Slider }
