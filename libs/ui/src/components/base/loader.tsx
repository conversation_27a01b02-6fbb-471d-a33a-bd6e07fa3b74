interface ClipLoaderProps {
  loading?: boolean
  color?: string
  speedMultiplier?: number
  size?: number
}

export const ClipLoader = ({
  loading = true,
  color = '#000000',
  speedMultiplier = 1,
  size = 35,
  ...additionalProps
}: ClipLoaderProps) => {
  if (!loading) {
    return null
  }

  const animationName = `clip-animation-${Math.random()
    .toString(36)
    .substr(2, 9)}`

  const clipKeyframes = `@keyframes ${animationName} {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(0.8); }
    100% { transform: rotate(360deg) scale(1); }
  }`

  const spinnerStyle = {
    background: 'transparent !important',
    width: `${size}px`,
    height: `${size}px`,
    borderRadius: '100%',
    border: '2px solid',
    borderTopColor: color,
    borderBottomColor: 'transparent',
    borderLeftColor: color,
    borderRightColor: color,
    display: 'inline-block',
    animation: `${animationName} ${0.75 / speedMultiplier}s infinite linear`,
  }

  return (
    <>
      <style>{clipKeyframes}</style>
      <span style={spinnerStyle} {...additionalProps} />
    </>
  )
}

export default ClipLoader
