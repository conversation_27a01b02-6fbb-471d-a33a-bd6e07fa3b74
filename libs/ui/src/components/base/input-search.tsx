import * as React from 'react'
import { Search, XCircle } from 'lucide-react'
import { cn } from '@libs/common'
import { Button } from './button'

export interface InputSearchProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  size?: 'sm' | 'md' | 'lg'
  onClear?: () => void
  showClearButton?: boolean
}

export const InputSearch = React.forwardRef<HTMLInputElement, InputSearchProps>(
  (
    {
      className,
      size = 'md',
      value,
      onChange,
      onClear,
      showClearButton = true,
      ...props
    },
    ref
  ) => {
    const handleClear = (e: React.MouseEvent) => {
      e.preventDefault()
      if (onClear) {
        onClear()
      } else if (onChange) {
        const event = {
          target: { value: '' },
        } as React.ChangeEvent<HTMLInputElement>
        onChange(event)
      }
    }

    return (
      <div className="relative">
        <div
          className={cn(
            'flex items-center rounded-full border border-input bg-background ring-offset-background focus-within:ring-1 focus-within:ring-ring',
            size === 'sm' && 'h-8',
            size === 'md' && 'h-10',
            size === 'lg' && 'h-12',
            className
          )}
        >
          <Search
            className={cn(
              'shrink-0 opacity-50',
              size === 'sm' && 'ml-2 h-3 w-3',
              size === 'md' && 'ml-3 h-4 w-4',
              size === 'lg' && 'ml-4 h-5 w-5'
            )}
          />
          <input
            ref={ref}
            value={value}
            onChange={onChange}
            className={cn(
              'flex-1 bg-transparent outline-none placeholder:text-muted-foreground',
              size === 'sm' && 'px-2 text-xs',
              size === 'md' && 'px-3 text-sm',
              size === 'lg' && 'px-4 text-base'
            )}
            {...props}
          />
          {showClearButton && value && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className={cn(
                'p-0 text-slate-500 absolute right-0 top-1/2 -translate-y-1/2',
                size === 'sm' && 'mr-1 h-4 w-4',
                size === 'md' && 'mr-2 h-5 w-5',
                size === 'lg' && 'mr-3 h-6 w-6'
              )}
              dataTestId="clear-search"
              onClick={handleClear}
            >
              <span className="sr-only">Clear</span>
              <XCircle
                className={cn(
                  size === 'sm' && 'h-4 w-4',
                  size === 'md' && 'h-5 w-5',
                  size === 'lg' && 'h-6 w-6'
                )}
              />
            </Button>
          )}
        </div>
      </div>
    )
  }
)

InputSearch.displayName = 'InputSearch'
