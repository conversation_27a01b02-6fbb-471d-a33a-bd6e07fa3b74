import { ResponsiveLine } from '@nivo/line'
import { useMemo, useState } from 'react'
import { cn } from '@libs/common'
import colors from 'tailwindcss/colors'

interface TrendsChartProps {
  trends: Array<{
    id: string
    color: string
    data: Array<{ x: Date; y: number }>
  }>
}

const theme = {
  fontSize: 14,
  fontFamily: "'DM Sans Variable', sans-serif",
  axis: {
    legend: {
      text: {
        fontWeight: 300,
        fontSize: 14,
      },
    },
    ticks: {
      text: {
        fontFamily: "'DM Sans Variable', sans-serif",
        fontWeight: 300,
        fontSize: 12,
        fill: '#6b7280',
      },
    },
  },
  legends: {
    text: {
      fontWeight: 300,
      fontSize: 14,
      fill: '#6b7280',
    },
  },
}

export const TrendsChart = ({ trends }: TrendsChartProps) => {
  const [tooltip, setTooltip] = useState<{
    x: number
    y: number
    data: any
  } | null>(null)

  const yMin = Math.min(...trends.flatMap((t) => t.data.map((d) => d.y)))
  const yMax = Math.max(...trends.flatMap((t) => t.data.map((d) => d.y)))

  const tickValues = useMemo(() => {
    if (trends.length === 0) return []

    const allDates = trends.flatMap((trend) => trend.data.map((d) => d.x))

    if (allDates.length === 0) return []

    const firstDate = allDates[0]
    const lastDate = allDates[allDates.length - 1]

    const numTicks = Math.min(7, allDates.length)
    const interval = Math.ceil(
      (lastDate.getTime() - firstDate.getTime()) / (numTicks - 1)
    )

    return Array.from(
      { length: numTicks },
      (_, i) => new Date(firstDate.getTime() + i * interval)
    )
  }, [trends])

  const CustomAreaLayer: ({
    series,
    xScale,
    yScale,
  }: {
    series: any
    xScale: any
    yScale: any
  }) => null | JSX.Element = ({ series, xScale, yScale }) => {
    if (series.length < 2) return null // Need at least two lines for an area

    const [line1, line2] = series

    return (
      <g>
        {line1.data.map(
          (point: { data: { x: Date; y: number } }, index: number) => {
            if (index === 0) return null

            const prevPoint1 = line1.data[index - 1]
            const prevPoint2 = line2.data[index - 1]
            const point2 = line2.data[index]

            const x1 = xScale(prevPoint1.data.x)
            const y1 = yScale(prevPoint1.data.y)
            const x2 = xScale(point.data.x)
            const y2 = yScale(point.data.y)

            const y1Other = yScale(prevPoint2.data.y)
            const y2Other = yScale(point2.data.y)

            const isTealAbove = prevPoint1.data.y > prevPoint2.data.y
            const gradientId = isTealAbove ? 'tealGradient' : 'roseGradient'

            return (
              <polygon
                key={index}
                points={`${x1},${y1} ${x2},${y2} ${x2},${y2Other} ${x1},${y1Other}`}
                fill={`url(#${gradientId})`}
                opacity={0.4}
                onMouseOver={(e) => {
                  const chartContainer = e.currentTarget.closest(
                    '.chart-container'
                  ) as HTMLElement | null
                  if (!chartContainer) return

                  const { left, top } = chartContainer.getBoundingClientRect()

                  setTooltip({
                    x: e.clientX - left,
                    y: e.clientY - top,
                    data: {
                      date: point.data.x.toLocaleDateString(),
                      trends: [
                        {
                          id: line1.id,
                          value: prevPoint1.data.y,
                          color: line1.color,
                        },
                        {
                          id: line2.id,
                          value: prevPoint2.data.y,
                          color: line2.color,
                        },
                      ],
                    },
                  })
                }}
                onMouseOut={() => setTooltip(null)}
              />
            )
          }
        )}
      </g>
    )
  }

  return (
    <div className={cn('relative chart-container w-full', 'h-60')}>
      <ResponsiveLine
        data={trends}
        colors={trends.map((t) => t.color)}
        enableArea={false}
        areaOpacity={0.3}
        margin={{ top: 20, right: 30, bottom: 70, left: 50 }}
        xScale={{
          type: 'time',
          precision: 'day',
        }}
        yScale={{
          type: 'linear',
          min: 0,
          max: yMin === yMax ? yMax : 'auto',
          reverse: false,
        }}
        axisLeft={{
          format: (value) => (Number.isInteger(value) ? value : ''),
          tickValues: 5,
        }}
        axisBottom={{
          tickValues: tickValues,
          tickSize: 10,
          tickPadding: 7,
          tickRotation: 0,
          truncateTickAt: 0,
          format: (value) => new Date(value).toLocaleDateString(),
        }}
        yFormat=" >-.2f"
        enableGridX={false}
        pointLabel="data.yFormatted"
        pointLabelYOffset={-12}
        pointSize={0}
        pointBorderWidth={1}
        pointBorderColor={{ from: 'serieColor' }}
        useMesh={true}
        legends={[
          {
            anchor: 'bottom',
            direction: 'row',
            justify: false,
            translateX: 0,
            translateY: 55,
            itemDirection: 'left-to-right',
            itemWidth: 80,
            itemHeight: 20,
            symbolSize: 12,
            symbolShape: 'square',
            symbolBorderColor: 'rgba(0, 0, 0, .5)',
          },
        ]}
        theme={theme}
        layers={['grid', 'axes', 'lines', CustomAreaLayer, 'points', 'legends']}
        defs={[
          {
            id: 'tealGradient',
            type: 'linearGradient',
            colors: [
              { offset: 0, color: colors.teal['400'] },
              { offset: 100, color: colors.teal['200'] },
            ],
          },
          {
            id: 'roseGradient',
            type: 'linearGradient',
            colors: [
              { offset: 0, color: colors.rose['400'] },
              { offset: 100, color: colors.rose['200'] },
            ],
          },
        ]}
        fill={[
          { match: '*', id: 'tealGradient' },
          { match: '*', id: 'roseGradient' },
        ]}
      />
      {tooltip && (
        <div
          className="absolute p-2 bg-white shadow-md border rounded-md text-sm"
          style={{ left: tooltip.x + 10, top: tooltip.y + 10 }}
        >
          <div className="text-gray-500 text-center font-medium">
            {tooltip.data.date}
          </div>
          {tooltip.data.trends.map(
            (trend: { id: string; value: number; color: string }) => (
              <div key={trend.id} className="flex items-center">
                <span
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: trend.color }}
                />
                <span className="font-medium">
                  {trend.id}: {trend.value}
                </span>
              </div>
            )
          )}
        </div>
      )}
    </div>
  )
}
