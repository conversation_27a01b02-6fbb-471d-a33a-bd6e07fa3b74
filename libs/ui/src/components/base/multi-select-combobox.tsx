'use client'

import * as React from 'react'
import { ChevronsUpDown, X } from 'lucide-react'
import { Popover, PopoverContent, PopoverTrigger } from './popover'
import { Button } from './button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from './command'
import { cn } from '@libs/common'
import { Badge } from './badge'
import { Checkbox } from './checkbox'

export type Option = {
  value: string
  label: string
  meta?: string
}

interface MultiSelectComboboxProps {
  options: Option[]
  placeholder?: string
  emptyMessage?: string
  value?: Option[]
  onChange?: (value: Option[]) => void
  isInModal?: boolean
  disabled?: boolean
  buttonSize?: 'default' | 'sm' | 'lg' | 'icon' | null | undefined
}

export function MultiSelectCombobox({
  options,
  placeholder = 'Select items...',
  emptyMessage = 'No items found.',
  value,
  onChange,
  isInModal,
  disabled = false,
  buttonSize = 'default',
}: MultiSelectComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [selected, setSelected] = React.useState<Option[]>(value || [])

  React.useEffect(() => {
    if (value !== undefined) {
      setSelected(value)
    }
  }, [value])

  const handleSelect = (option: Option) => {
    const updatedSelected = selected.some((item) => item.value === option.value)
      ? selected.filter((item) => item.value !== option.value)
      : [...selected, option]

    setSelected(updatedSelected)
    onChange?.(updatedSelected)
  }

  const handleRemove = (option: Option) => {
    if (disabled) return
    const updatedSelected = selected.filter(
      (item) => item.value !== option.value
    )
    setSelected(updatedSelected)
    onChange?.(updatedSelected)
  }

  return (
    <Popover open={open} onOpenChange={setOpen} modal={isInModal}>
      <PopoverTrigger asChild>
        <Button
          dataTestId="multi-select-combobox-button"
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          size={buttonSize}
          className={cn(
            'w-full justify-between text-xs shadow-none',
            'border-gray-200 text-gray-400',
            'font-medium',
            selected.length > 0 && 'border-slate-600 text-slate-600'
          )}
        >
          {selected.length > 0 ? `${selected.length} selected` : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-full p-0"
        align="start"
        side="bottom"
        sideOffset={4}
      >
        <Command>
          <CommandInput placeholder="Search..." />

          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  onSelect={() => handleSelect(option)}
                >
                  <Checkbox
                    checked={selected.some(
                      (item) => item.value === option.value
                    )}
                    className="mr-2"
                  />
                  <div className="w-full flex items-center justify-between gap-3">
                    {option.label}
                    {option?.meta && (
                      <div className="text-gray-400 capitalize">
                        {option?.meta}
                      </div>
                    )}
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
      <div
        className={cn('flex flex-wrap gap-1 mt-2', disabled && 'opacity-50')}
      >
        {selected.map((item) => (
          <Badge key={item.value} variant="secondary">
            {item.label}
            <Button
              dataTestId="remove-button"
              variant="ghost"
              size="sm"
              className="h-auto p-0 ml-2"
              onClick={() => handleRemove(item)}
            >
              <X className="h-3 w-3" />
              <span className="sr-only">Remove</span>
            </Button>
          </Badge>
        ))}
      </div>
    </Popover>
  )
}
