import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@libs/common'

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-full text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default:
          'bg-teal-800 text-white hover:bg-teal-700 disabled:opacity-50 shadow',
        destructive:
          'border-none text-white bg-red-500 hover:bg-red-600 disabled:bg-red-200 active:bg-red-700',
        outline:
          'border border-teal-800 bg-slate-50 shadow-sm text-teal-800 hover:bg-accent hover:border-teal-700 hover:text-teal-700 hover:bg-slate-100 disabled:bg-white disabled:border-slate-300 disabled:text-slate-300',
        secondary:
          'border border-teal-800 bg-slae-50 shadow-sm text-teal-800 hover:bg-accent hover:border-teal-700 hover:text-teal-700 hover:bg-slate-100 disabled:bg-white disabled:border-slate-300 disabled:text-slate-300',
        ghost:
          'text-teal-800 hover:bg-slate-100 hover:text-teal-800 disabled:text-slate-300',
        icon: 'text-teal-800 hover:bg-slate-100 hover:text-teal-800 disabled:text-slate-300',
        link: 'text-teal-800 hover:bg-slate-100 hover:text-teal-800 disabled:text-slate-300',
      },
      size: {
        default: 'h-10 rounded-full px-4 py-2',
        sm: 'h-8 rounded-full px-3 text-xs',
        lg: 'h-10 rounded-full px-8',
        icon: 'h-8 w-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  dataTestId: string
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { className, variant, size, asChild = false, dataTestId, ...props },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button'
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        data-testid={dataTestId}
        {...props}
      />
    )
  }
)
Button.displayName = 'Button'

export { Button, buttonVariants }
