import { Button } from '../base'
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react'
import { cn } from '@libs/common'
import { t } from 'i18next'

interface PaginationProps {
  currentPage: number
  limit: number
  total: number
  totalPages: number
  handlePageChange: (page: number) => void
  showSelected?: boolean
  selected?: number
}

export const Pagination = ({
  currentPage,
  limit,
  total,
  selected,
  showSelected,
  totalPages,
  handlePageChange,
}: PaginationProps) => {
  return (
    <div className="flex justify-between items-center border-t py-4">
      <div className="flex items-center gap-4">
        <div>
          {!!total && (
            <span>
              {currentPage * limit + 1}-
              {Math.min((currentPage + 1) * limit, total || 0)} of{' '}
            </span>
          )}
          {total} results
        </div>
        {!!showSelected && (
          <div className="ml-6 capitalize">
            {t('selected')}: {selected}
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <Button
          dataTestId="previous-page"
          variant="ghost"
          className="text-slate-800"
          disabled={currentPage === 0}
          onClick={() => handlePageChange(currentPage - 1)}
        >
          <ChevronLeftIcon />
        </Button>

        <Button
          dataTestId="first-page"
          variant="link"
          className={cn(
            'text-slate-800 px-2 py-1',
            currentPage === 0 ? 'border rounded border-slate-800' : ''
          )}
          onClick={() => handlePageChange(0)}
        >
          1
        </Button>
        {currentPage > 2 && totalPages > 4 && <span className="px-2">...</span>}

        {Array.from({ length: 3 }, (_, i) => {
          const pageNumber =
            currentPage === 0
              ? i + 1
              : currentPage === totalPages - 1
              ? totalPages - 3 + i
              : currentPage - 1 + i

          if (pageNumber > 0 && pageNumber < totalPages - 1) {
            return (
              <Button
                variant="link"
                dataTestId={`page-${pageNumber}`}
                key={pageNumber}
                className={cn(
                  'text-slate-800 px-2 py-1',
                  currentPage === pageNumber
                    ? 'border rounded border-slate-800'
                    : ''
                )}
                onClick={() => handlePageChange(pageNumber)}
              >
                {pageNumber + 1}
              </Button>
            )
          }
          return null
        })}
        {currentPage < totalPages - 3 && totalPages > 4 && (
          <span className="px-2">...</span>
        )}
        {totalPages > 1 && (
          <Button
            variant="link"
            dataTestId="pages-button"
            className={cn(
              'text-slate-800',
              currentPage === totalPages - 1
                ? 'border rounded border-slate-800 px-2 py-1'
                : ''
            )}
            onClick={() => handlePageChange(totalPages - 1)}
          >
            {totalPages}
          </Button>
        )}
        <Button
          dataTestId="next-page"
          variant="link"
          className="text-slate-800"
          disabled={currentPage === totalPages - 1}
          onClick={() => handlePageChange(currentPage + 1)}
        >
          <ChevronRightIcon />
        </Button>
      </div>
    </div>
  )
}
