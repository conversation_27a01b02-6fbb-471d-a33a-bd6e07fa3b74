import { AlertTriangle, HouseIcon, RefreshCcw } from 'lucide-react'
import { Button } from '../base'
import { t } from 'i18next'

export const ErrorSomethingWentWrong = () => {
  return (
    <div className="flex flex-col items-center justify-start min-h-full mt-24">
      <AlertTriangle className="w-12 h-12 text-yellow-500 mb-6" />
      <h1 className="text-2xl font-bold mb-4">{t('somethingWentWrong')}</h1>
      <p className="text-gray-400 mb-8 text-center max-w-md text-sm">
        {t('unexpectedError')}
      </p>
      <div className="flex gap-4">
        <Button
          onClick={() => window.location.reload()}
          dataTestId="retry-button"
          size="sm"
        >
          <RefreshCcw className="w-4 h-4 mr-2" />
          {t('retry')}
        </Button>
        <Button
          onClick={() => (window.location.href = '/')}
          variant="outline"
          dataTestId="go-home-button"
          size="sm"
        >
          <HouseIcon className="w-4 h-4 mr-2" />
          {t('goHome')}
        </Button>
      </div>
    </div>
  )
}
