import { Alert, AlertDescription, AlertTitle } from '../../base/alert'
import { CircleXIcon } from 'lucide-react'

interface AlertNotFoundProps {
  title: string
  description: string
}

export const AlertNotFound = ({ title, description }: AlertNotFoundProps) => {
  return (
    <Alert variant="destructive" className="bg-white">
      <CircleXIcon className="h-8 w-8 " data-testid="circle-x-icon" />
      <div className="p-0">
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription>{description}</AlertDescription>
      </div>
    </Alert>
  )
}
