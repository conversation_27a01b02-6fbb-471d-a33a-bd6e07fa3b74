import { cn } from '@libs/common'
import type { RiskScoreCategory } from 'prime-front-service-client'
import { riskScoreCategoryToClass } from '../../utils/states-to-icons'

interface PriorityBadgeProps {
  priority?: RiskScoreCategory
}

export const PriorityLabel = ({ priority }: PriorityBadgeProps) => {
  return (
    <div>
      <div
        className={cn(
          'priority-label flex items-center justify-center h-8 w-24 gap-1',
          priority === 'intervene'
            ? riskScoreCategoryToClass['intervene']
            : priority === 'analyze'
            ? riskScoreCategoryToClass['analyze']
            : riskScoreCategoryToClass['monitor']
        )}
      >
        <span className="text-sm font-semibold">{priority}</span>
      </div>
    </div>
  )
}
