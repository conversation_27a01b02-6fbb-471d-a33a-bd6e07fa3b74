import type { RiskFactorLevel } from 'prime-front-service-client'
import { cn } from '@libs/common'

interface SeverityLevelProps {
  level: RiskFactorLevel
}

const firstLevelToColor: Record<RiskFactorLevel, string> = {
  low: 'bg-slate-400',
  medium: 'bg-slate-500',
  high: 'bg-slate-600',
}

const secondLevelToColor: Record<RiskFactorLevel, string> = {
  low: 'bg-slate-200',
  medium: 'bg-slate-500',
  high: 'bg-slate-600',
}

const thirdLevelToColor: Record<RiskFactorLevel, string> = {
  low: 'bg-slate-200',
  medium: 'bg-slate-200',
  high: 'bg-slate-600',
}

export const SeverityLevel = ({ level }: SeverityLevelProps) => {
  return (
    <div className="inline-flex items-center rounded-full overflow-hidden gap-0.5">
      <div
        className={cn(
          'w-[20px] h-[10px]  rounded-l-full',
          firstLevelToColor[level]
        )}
      ></div>
      <div className={cn('w-[20px] h-[10px]', secondLevelToColor[level])}></div>
      <div
        className={cn(
          'w-[20px] h-[10px] rounded-r-full',
          thirdLevelToColor[level]
        )}
      ></div>
    </div>
  )
}
