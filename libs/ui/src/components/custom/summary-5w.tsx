import type { Summary5W as Summary5WModel } from 'prime-front-service-client/src/models/Summary5W'
import { t } from 'i18next'
interface Summary5WProps {
  summary: Summary5WModel
}

export const Summary5W = ({ summary }: Summary5WProps) => {
  return (
    <div className="px-4 text-slate-600">
      <ul>
        <li className="py-2">
          <span className="font-bold uppercase">{t('what')}</span>
          <ul className="list-disc pl-8">
            <li>
              <span className="mr-1 capitalize">{t('description')}:</span>
              {summary?.what?.description || t('insufficientInformation')}
            </li>
            <li>
              <span className="mr-1 capitalize">{t('summary')}:</span>
              {summary?.what?.summary || t('insufficientInformation')}
            </li>
          </ul>
        </li>
        <li className="py-2">
          <span className="font-bold uppercase">{t('who')}</span>
          <ul className="list-disc pl-8">
            <li>
              <span className="mr-1 capitalize">{t('affected')}:</span>
              {summary?.who?.affected || t('insufficientInformation')}
            </li>
            <li>
              <span className="mr-1 capitalize">{t('stakeholders')}:</span>
              {summary?.who?.stakeholders || t('insufficientInformation')}
            </li>
          </ul>
        </li>
        <li className="py-2">
          <span className="font-bold uppercase">{t('where')}</span>
          <ul className="list-disc pl-8">
            <li>
              <span className="mr-1 capitalize">{t('components')}:</span>
              {summary?.where?.components || t('insufficientInformation')}
            </li>
            <li>
              <span className="mr-1 capitalize">{t('environment')}:</span>
              {summary?.where?.environment || t('insufficientInformation')}
            </li>
            <li>
              <span className="mr-1 capitalize">{t('products')}:</span>
              {summary?.where?.products || t('insufficientInformation')}
            </li>
          </ul>
        </li>
        <li className="py-2">
          <span className="font-bold uppercase">{t('why')}</span>
          <ul className="list-disc pl-8">
            <li>
              <span className="mr-1 capitalize">{t('impact')}:</span>
              {summary?.why?.impact || t('insufficientInformation')}
            </li>
            <li>
              <span className="mr-1 capitalize font-medium">
                {t('purpose')}:
              </span>
              {summary?.why?.purpose || t('insufficientInformation')}
            </li>
          </ul>
        </li>
        <li className="py-2">
          <span className="font-bold uppercase">{t('how')}</span>
          <ul className="list-disc pl-8">
            <li>
              <span className="mr-1 capitalize">{t('acceptance')}:</span>
              {summary?.how?.acceptance || t('insufficientInformation')}
            </li>
            <li>
              <span className="mr-1 capitalize">{t('approach')}:</span>
              {summary?.how?.approach || t('insufficientInformation')}
            </li>
          </ul>
        </li>
      </ul>
    </div>
  )
}
