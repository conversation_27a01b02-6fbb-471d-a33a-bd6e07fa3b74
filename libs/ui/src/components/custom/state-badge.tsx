import type { CaseStatus } from 'prime-front-service-client'

import { Badge } from '../base'
import { cn } from '@libs/common'

interface StateBadgeProps {
  state: CaseStatus
}

export const stateToClass: Record<CaseStatus, string> = {
  open: 'capitalize rounded-full font-medium border text-sm bg-emerald-100 border-teal-600 text-teal-600 hover:text-teal-900 hover:border-teal-900 hover:bg-emerald-100',
  done: 'capitalize rounded-full font-medium border text-sm bg-gray-50 border-teal-700 text-teal-700 hover:text-teal-900 hover:border-teal-900 hover:bg-gray-100',
  dismissed:
    'capitalize rounded-full font-medium border text-sm bg-gray-50 border-teal-700 text-teal-700 hover:text-teal-900 hover:border-teal-900 hover:bg-gray-100',
}

export const StateBadge = ({ state }: StateBadgeProps) => {
  return (
    <Badge
      className={cn(
        stateToClass[state],
        'shadow-none flex justify-start items-center gap-1 py-1 px-4 h-8'
      )}
      data-testid="status-badge"
    >
      {state}
    </Badge>
  )
}
