#custom-mermaid {
  background-color: transparent;
  border-radius: 8px;
  padding: 16px;
}

/* Nodes */
#custom-mermaid .node rect,
#custom-mermaid .node circle,
#custom-mermaid .node ellipse,
#custom-mermaid .node polygon,
#custom-mermaid .node path {
  fill: #f0f4f8;
  stroke: #a0aec0;
  stroke-width: 1px;
  rx: 5px;
  ry: 5px;
}

/* Node Text */
#custom-mermaid .node .label {
  color: #2d3748;
  font-family: var(--mermaid-font-family);
  font-weight: 500;
}

/* Edges */
#custom-mermaid .edgePath .path {
  stroke: #4a5568;
  stroke-width: 1.5px;
}

/* Arrow heads */
#custom-mermaid .arrowheadPath {
  fill: #4a5568;
  stroke: none;
}

/* Flowchart */
#custom-mermaid .flowchart-link {
  stroke: #4a5568;
  fill: none;
}

/* Sequence diagrams */
#custom-mermaid .actor {
  fill: #f0f4f8;
  stroke: #a0aec0;
}

#custom-mermaid .messageLine0 {
  stroke: #4a5568;
}

/* Gantt charts */
#custom-mermaid .taskText {
  fill: #2d3748;
}

#custom-mermaid .taskTextOutsideRight {
  fill: #2d3748;
}

#custom-mermaid .taskTextOutsideLeft {
  fill: #2d3748;
}

#custom-mermaid .task {
  fill: #f0f4f8;
  stroke: #a0aec0;
}

#custom-mermaid .taskText0,
#custom-mermaid .taskText1,
#custom-mermaid .taskText2,
#custom-mermaid .taskText3 {
  fill: #2d3748;
}

#custom-mermaid .active0,
#custom-mermaid .active1,
#custom-mermaid .active2,
#custom-mermaid .active3 {
  fill: #e2e8f0;
  stroke: #a0aec0;
}

/* Add smooth transitions */
#custom-mermaid .node rect,
#custom-mermaid .node circle,
#custom-mermaid .node ellipse,
#custom-mermaid .node polygon,
#custom-mermaid .node path,
#custom-mermaid .edgePath .path {
  transition: all 0.3s ease;
}

/* Hover effects */
#custom-mermaid .node:hover rect,
#custom-mermaid .node:hover circle,
#custom-mermaid .node:hover ellipse,
#custom-mermaid .node:hover polygon,
#custom-mermaid .node:hover path {
  fill: #e2e8f0;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}
