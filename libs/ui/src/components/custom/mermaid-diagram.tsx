import './mermaid-theme.css'
import type React from 'react'
import { useEffect, useRef, useState } from 'react'
import mermaid from 'mermaid'
import { Plus, Minus } from 'lucide-react'
import { Button } from '../base'

interface MermaidDiagramProps {
  chart: string
  theme?: 'default' | 'modern'
}

export const MermaidDiagram: React.FC<MermaidDiagramProps> = ({
  chart,
  theme = 'default',
}) => {
  const ref = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [zoom, setZoom] = useState(2)
  const [isDragging, setIsDragging] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [startPos, setStartPos] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const renderDiagram = async () => {
      if (ref.current) {
        const diagramContent = chart.trim()
        if (!diagramContent) {
          ref.current.innerHTML = 'graph TD\n  A[No diagram available]'
          return
        }

        ref.current.innerHTML = diagramContent

        try {
          mermaid.initialize({
            startOnLoad: false,
            theme: theme === 'modern' ? 'neutral' : 'default',
            securityLevel: 'loose',
          })

          await mermaid
            .render('custom-mermaid', diagramContent)
            .then(({ svg }) => {
              if (ref.current) {
                ref.current.innerHTML = svg
              }
            })
        } catch (error) {
          console.error('Mermaid error:', error)

          if (ref.current) {
            ref.current.innerHTML = 'graph TD\n  A[Error rendering diagram]'
          }
        }
      }
    }
    renderDiagram()
  }, [chart, theme])

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setStartPos({ x: e.clientX - position.x, y: e.clientY - position.y })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return
    setPosition({ x: e.clientX - startPos.x, y: e.clientY - startPos.y })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  return (
    <div className="relative  w-full h-full flex flex-col flex-1 bg-slate-100 rounded-2xl ">
      <div
        ref={containerRef}
        className="relative w-full flex-1 overflow-hidden rounded-lg cursor-grab active:cursor-grabbing min-h-[300px]"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
      >
        <div
          className="mermaid absolute"
          ref={ref}
          style={{
            transform: `translate(${position.x}px, ${position.y}px) scale(${zoom})`,
            transformOrigin: 'top left',
            transition: isDragging ? 'none' : 'transform 0.2s ease-in-out',
          }}
        />
      </div>

      <div className="flex w-full justify-end gap-2 mt-2 p-4">
        <Button
          variant="outline"
          size="sm"
          dataTestId="zoom-in"
          onClick={() => setZoom((z) => Math.min(z + 0.5, 5))}
        >
          <Plus size={14} />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setZoom((z) => Math.max(z - 0.5, 0.5))}
          dataTestId="zoom-out"
        >
          <Minus size={14} />
        </Button>
      </div>
    </div>
  )
}
