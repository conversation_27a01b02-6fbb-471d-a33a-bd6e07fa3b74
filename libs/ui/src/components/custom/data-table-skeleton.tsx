import {
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '../base'

export function TableSkeleton() {
  return (
    <div className="px-6">
      <Table>
        <TableHeader className="border-0 text-sm bg-white sticky top-0 z-10 border-b border-dashed">
          <TableRow className="border-b-0">
            {Array.from({ length: 4 }).map((_, index) => (
              <TableCell key={index} className="px-2 py-5">
                <Skeleton className="h-4 w-[100px]" />
              </TableCell>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 10 }).map((_, rowIndex) => (
            <TableRow key={rowIndex}>
              {Array.from({ length: 4 }).map((_, colIndex) => (
                <TableCell key={colIndex} className="px-2 py-7">
                  <Skeleton className="h-4 w-3/4" />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
