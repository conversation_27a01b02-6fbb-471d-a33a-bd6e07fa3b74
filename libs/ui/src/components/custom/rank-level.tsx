import type {
  ConfidenceScoreLevel,
  RiskFactorLevel,
} from 'prime-front-service-client'
import { Badge } from '../base'
import { cn } from '@libs/common'

export const levelToColor: Record<
  ConfidenceScoreLevel | RiskFactorLevel,
  string
> = {
  low: 'bg-slate-400',
  medium: 'bg-slate-500',
  high: 'bg-slate-600',
}

interface RankLevelProps {
  level: ConfidenceScoreLevel | RiskFactorLevel
}

export const RankLevel = ({ level }: RankLevelProps) => {
  return (
    level && (
      <Badge
        className={cn(`text-xs shadow-none capitalize`, levelToColor[level])}
      >
        {level}
      </Badge>
    )
  )
}
