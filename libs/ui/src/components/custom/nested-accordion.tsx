import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../base'
import { cn } from '@libs/common'
import { useMemo } from 'react'
import { t } from 'i18next'

interface Item {
  id: string
  title: string
  type: string
  children?: Item[]
}

const firstLevelClass = 'bg-teal-700 text-white'
const secondLevelClass = 'border-2 border-teal-700 bg-white'
const defaultClass = 'border border-slate-300 bg-white'

export function NestedAccordion({
  data,
  level = 0,
}: {
  data: Item[]
  level?: number
}) {
  const currentClass = useMemo(() => {
    if (level === 0) return firstLevelClass
    return level % 2 === 1 ? secondLevelClass : defaultClass
  }, [level])

  const defaultOpenIds = useMemo(() => data.map((item) => item.id), [data])

  return (
    <Accordion
      type="multiple"
      className="max-w-[450px]"
      defaultValue={defaultOpenIds}
    >
      {data.map((item, index) => {
        const hasChildren = item.children && item.children.length > 0
        const isLast = index === data.length - 1

        const connectorClasses = cn(
          'relative pl-6',
          'before:absolute before:left-2 before:w-4 before:content-[""] before:border-gray-300',
          isLast
            ? 'before:top-0 before:h-1/2 before:border-l-2'
            : 'before:top-0 before:bottom-0 before:border-l-2'
        )

        const contentWrapper = (
          <div className="before:absolute before:top-1/2 before:left-2 before:w-4 before:border-t-2 before:border-gray-300 before:content-['']">
            <div className={cn('p-2')}>
              <div
                className={cn(currentClass, 'my-1 rounded-lg w-full py-4 px-2')}
              >
                <div className="font-semibold text-sm">{item.title}</div>
                <div
                  className={cn(
                    'font-normal text-sm',
                    level === 0 ? 'text-gray-300' : 'text-gray-400'
                  )}
                >
                  {item.type} | {item.id}
                </div>
              </div>
            </div>
          </div>
        )

        return hasChildren ? (
          <AccordionItem key={item.id} value={item.id} className="border-b-0">
            <div className={cn(level > 0 && connectorClasses)}>
              <AccordionTrigger
                className={cn(
                  'my-2 p-2 py-4 rounded-lg w-full hover:no-underline relative mx-2',
                  currentClass
                )}
              >
                <div
                  className={cn(
                    'flex w-full justify-between items-start gap-2'
                  )}
                >
                  <div className="flex flex-col text-left">
                    <div className="font-semibold text-sm">{item.title}</div>
                    <div
                      className={cn(
                        'font-normal text-sm',
                        level === 0 ? 'text-gray-300' : 'text-gray-400'
                      )}
                    >
                      {item.type} | {item.id}
                    </div>
                  </div>

                  {!!item.children?.length && (
                    <div
                      className={cn(
                        'font-normal shrink-0 self-end mr-4 text-sm',
                        level === 0 ? 'text-gray-300' : 'text-gray-400'
                      )}
                    >
                      {item.children.length}{' '}
                      {item.children.length === 1 ? t('child') : t('children')}
                    </div>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="ml-4">
                <NestedAccordion data={item.children!} level={level + 1} />
              </AccordionContent>
            </div>
          </AccordionItem>
        ) : (
          <div key={item.id} className={connectorClasses}>
            {contentWrapper}
          </div>
        )
      })}
    </Accordion>
  )
}
