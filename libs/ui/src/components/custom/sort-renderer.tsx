import type { ReactNode } from 'react'
import type { Column } from '@tanstack/react-table'
import type { ExternalCaseWorkroom } from 'prime-front-service-client'
import { <PERSON><PERSON>, Tooltip, TooltipContent, TooltipTrigger } from '../base'
import { ArrowDown, ArrowDownUpIcon } from 'lucide-react'
import { cn } from '@libs/common'
import { categoryToIcon } from './risk-badge'
import { JiraIcn } from '../../icons'

const riskFactorsColumns = [
  'confidentiality_level',
  'integrity_level',
  'availability_level',
]

export const SortRenderer = ({
  title,
  column,
  tooltip,
  staticColumns,
  withJiraIcon,
  enableSort = true,
}: {
  title: string | ReactNode
  column: Column<ExternalCaseWorkroom>
  staticColumns: string[]
  tooltip?: string
  withJiraIcon?: boolean
  enableSort?: boolean
}) => {
  const isRiskFactor = riskFactorsColumns.includes(column.id)

  const isSorted = column.getIsSorted()
  const isSortedAsc = isSorted === 'asc'
  return (
    <div className="flex items-center">
      {tooltip ? (
        <Tooltip>
          <TooltipTrigger asChild>
            <div>
              {isRiskFactor ? (
                column.id === 'confidentiality_level' ? (
                  categoryToIcon.confidentiality
                ) : column.id === 'integrity_level' ? (
                  categoryToIcon.integrity
                ) : column.id === 'availability_level' ? (
                  categoryToIcon.availability
                ) : null
              ) : staticColumns.includes(column.id) ? (
                <div className="flex items-center gap-2">{title}</div>
              ) : (
                title
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>{tooltip}</TooltipContent>
        </Tooltip>
      ) : (
        <div className="flex items-center gap-2">
          {withJiraIcon && <JiraIcn className="w-4 h-4 text-jira" />}
          <span>{title}</span>
        </div>
      )}
      {enableSort && (
        <Button
          variant="ghost"
          className="p-0 h-6 w-6 text-slate-600"
          size="icon"
          dataTestId="sort-button"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <div className="flex p-0 font-light">
            {isSorted ? (
              <ArrowDown
                className={cn(
                  'p-0 h-5 w-4',
                  isSorted ? 'text-teal-600' : 'text-primary',
                  isSorted && isSortedAsc ? 'rotate-180' : ''
                )}
              />
            ) : (
              <ArrowDownUpIcon className="p-0 h-5 w-4 text-gray-500" />
            )}
          </div>
        </Button>
      )}
    </div>
  )
}
