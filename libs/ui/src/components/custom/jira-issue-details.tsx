import { Badge, Tooltip, TooltipContent, TooltipTrigger } from '../base'
import { ExternalLink } from 'lucide-react'
import { Link } from 'react-router'
import { useCallback } from 'react'
import { ConfluenceIcn, GDriveIcn } from '../../icons'
import { t } from 'i18next'
import type { IssueLinks, ProviderFieldInfo } from 'prime-front-service-client'
import { IssueLinkType } from 'prime-front-service-client'

interface JiraIssueDetailsProps {
  issueNumber: string | null
  issueLink?: string
  customDetails?: Record<string, any>
  providerFieldsTypes?: { [key: string]: ProviderFieldInfo } | null
  issueLinks: IssueLinks[]
}

export const JiraIssueDetails = ({
  issueNumber,
  issueLink,
  customDetails,
  providerFieldsTypes,
  issueLinks,
}: JiraIssueDetailsProps) => {
  const propTitleClass = 'mb-1 font-light text-xs text-slate-500 capitalize'
  const filterProps = ['issue_link', 'issue_name', 'issue_id']

  const filteredFields = useCallback(() => {
    if (customDetails)
      return Object.keys(customDetails)?.filter(
        (fieldName) =>
          !filterProps.includes(fieldName) && !!getFieldValueDisplay(fieldName)
      )
  }, [customDetails])

  const getFieldValueDisplay = useCallback(
    (fieldName: string) => {
      if (providerFieldsTypes && customDetails) {
        if (providerFieldsTypes[fieldName].type === 'date') {
          return new Date(customDetails[fieldName]).toLocaleDateString()
        }
        if (providerFieldsTypes[fieldName].type === 'array') {
          return customDetails[fieldName]
            .slice(1, -1)
            .split(',')
            .map((s: string) => s.trim())
            .join(', ')
        }
      }
      return customDetails && customDetails[fieldName]
    },
    [customDetails, providerFieldsTypes]
  )

  return (
    <div className="relative text-card-foreground font-light rounded-md mb-6">
      <div className="flex flex-wrap items-center">
        <div className="flex items-center gap-2">
          {issueLink && (
            <Link to={issueLink} target="_blank">
              <Badge className="h-7 bg-white border-none shadow-none text-gray-700 hover:bg-white-50/80 underline font-light hover:text-gray-800">
                <ExternalLink size={18} className="text-blue-500" />
                <span className="ml-2 text-sm">{issueNumber}</span>
              </Badge>
            </Link>
          )}
        </div>
        <div className="flex items-center flex-wrap">
          {!!issueLinks?.length &&
            issueLinks
              .filter((link) => link.link_type === 'confluence')
              .map((link, index) => (
                <Link key={crypto.randomUUID()} to={link?.url} target="_blank">
                  <Badge className="h-7 bg-white border-none shadow-none text-gray-700 hover:bg-white-50/80 underline font-light hover:text-gray-800 w-36">
                    <ConfluenceIcn />
                    <span className="ml-2 text-sm capitalize">
                      {t('confluence')} [{index + 1}]
                    </span>
                  </Badge>
                </Link>
              ))}
        </div>
        <div className="flex items-center flex-wrap">
          {!!issueLinks?.length &&
            issueLinks
              .filter((link) => link.link_type === IssueLinkType.gdrive)
              ?.map((link) => (
                <Link key={crypto.randomUUID()} to={link?.url} target="_blank">
                  <Badge className="h-7 bg-white border-none shadow-none text-gray-700 hover:bg-white-50/80 underline font-light hover:text-gray-900 cursor-pointer">
                    <GDriveIcn className="h-5 w-5" />
                    <span className="ml-2 text-sm capitalize">
                      {t('gDrive')}
                    </span>
                  </Badge>
                </Link>
              ))}
        </div>
        {issueNumber === 'AI-101' && (
          <div className="flex items-center">
            <Link
              to="https://docs.google.com/document/d/1jm0ks0zz7WE0O__5GAPjMKS4h8gebH4D0s9pII6MHOQ/edit?usp=drive_web&ouid=114822833493328366714"
              target="_blank"
            >
              <Badge className="h-7 bg-white border-none shadow-none text-gray-700 hover:bg-white-50/80 underline font-light hover:text-gray-900 cursor-pointer">
                <GDriveIcn className="h-5 w-5" />
                <span className="ml-2 text-sm capitalize">{t('gDrive')}</span>
              </Badge>
            </Link>
          </div>
        )}
        {issueNumber === 'AI-104' && (
          <div className="flex items-center">
            <Link
              to="https://docs.google.com/document/d/11oOA9PGIlJZKCQFqZJH9l8RXVv9WuMe0IX6Kro2TGvQ/edit?tab=t.0#heading=h.x21kde5rmtq5"
              target="_blank"
            >
              <Badge className="h-7 bg-white border-none shadow-none text-gray-700 hover:bg-white-50/80 underline font-light hover:text-gray-900 cursor-pointer">
                <GDriveIcn className="h-5 w-5" />
                <span className="ml-2 text-sm capitalize">{t('gDrive')}</span>
              </Badge>
            </Link>
          </div>
        )}
      </div>
      <div className="attributes-grid grid grid-cols-2 2xl:grid-cols-3 gap-3 mt-6 w-full overflow-x-hidden">
        {customDetails &&
          filteredFields()?.map((fieldName) => (
            <div key={crypto.randomUUID()}>
              <div className={propTitleClass}>
                {providerFieldsTypes && providerFieldsTypes[fieldName].name}
              </div>
              {getFieldValueDisplay(fieldName).length > 20 ? (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="cursor-text text-sm ">
                      {`${getFieldValueDisplay(fieldName).slice(0, 20)}...`}
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-sm break-words max-w-72">
                      {getFieldValueDisplay(fieldName)}
                    </div>
                  </TooltipContent>
                </Tooltip>
              ) : (
                <div className="text-sm break-words">
                  {getFieldValueDisplay(fieldName)}
                </div>
              )}
            </div>
          ))}
      </div>
    </div>
  )
}
