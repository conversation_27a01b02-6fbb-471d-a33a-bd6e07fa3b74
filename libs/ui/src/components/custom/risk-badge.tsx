import {
  AvailabilityIcn,
  ComplianceIcn,
  ConfidentialityIcn,
  IntegrityIcn,
  ThirdPartyIcn,
} from '../../icons/category'
import { Tooltip, TooltipContent, TooltipTrigger } from '../base'
import { useTranslation } from 'react-i18next'
import type { RiskFactorLevel } from 'prime-front-service-client'
import { RankLevel } from './rank-level'
import React from 'react'

export type RiskScoreCategoryType =
  | 'availability'
  | 'compliance'
  | 'confidentiality'
  | 'integrity'
  | 'thirdParty'

interface RiskBadgeProps {
  category: RiskScoreCategoryType
  risk: RiskFactorLevel
}

export const categoryToIcon: Record<RiskScoreCategoryType, React.ReactNode> = {
  availability: <AvailabilityIcn />,
  compliance: <ComplianceIcn />,
  confidentiality: <ConfidentialityIcn />,
  integrity: <IntegrityIcn />,
  thirdParty: <ThirdPartyIcn />,
}

export const RiskBadge = ({ category, risk }: RiskBadgeProps) => {
  const { t } = useTranslation()
  return (
    <div className="flex items-center text-gray-400 gap-2">
      <Tooltip>
        <TooltipTrigger asChild>
          <span>{categoryToIcon[category]}</span>
        </TooltipTrigger>
        <TooltipContent side="top">
          <span className="capitalize">{t(category)}</span>
        </TooltipContent>
      </Tooltip>
      <RankLevel level={risk} />
    </div>
  )
}
