import { useEffect, useState } from 'react'
import { ChevronDown } from 'lucide-react'
import { cn } from '@libs/common'
import type { DateRange } from 'react-day-picker'
import { format } from 'date-fns'
import { t } from 'i18next'

import { toast } from 'sonner'
import {
  Button,
  Calendar,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../base'
import { presets } from './filter-date-range-picker'
import type { DateRangePreset } from './filter-date-range-picker'
import type { FilterItem } from './filter-interfaces'
interface DateRangePickerProps {
  filterKey: string
  savedFilter?: FilterItem
  onFilterChange: (filter: FilterItem) => void
}

export const DateRangePicker = ({
  filterKey,
  savedFilter,
  onFilterChange,
}: DateRangePickerProps) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false)
  const [tempDate, setTempDate] = useState<DateRange | undefined>(
    savedFilter?.value
      ? {
          from: new Date(savedFilter?.value[0]),
          to: new Date(savedFilter?.value[1]),
        }
      : presets.past30Days
  )
  const [preset, setPreset] = useState<DateRangePreset | null>(
    savedFilter ? 'custom' : 'past30Days'
  )

  const handleDateRangePreset = (preset: DateRangePreset) => {
    setPreset(preset)
    setTempDate(presets[preset])
  }

  const handleApply = () => {
    if (!tempDate) {
      return
    }

    if (!tempDate.from || !tempDate.to) {
      toast.error('Please select a valid date range')
      return
    }
    const from = tempDate?.from?.toISOString() || ''
    const to = tempDate?.to?.toISOString() || ''
    onFilterChange({
      field: filterKey,
      value: [from, to],
      op: 'between',
    })
    setIsPopoverOpen(false)
  }

  const handleClear = () => {
    setTempDate(presets.past30Days)
  }

  useEffect(() => {
    if (savedFilter) {
      setTempDate({
        from: new Date(savedFilter?.value[0]),
        to: new Date(savedFilter?.value[1]),
      })
      setPreset('custom')
    }
  }, [savedFilter])

  return (
    <div className={cn('grid gap-2')}>
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            dataTestId="date-range-picker-button"
            className={cn('h-8 capitalize text-sm outline-none pl-4 pr-2 w-42')}
          >
            <div className="flex items-center">
              {savedFilter ? (
                <div>
                  {format(savedFilter.value[0], 'LLL dd, y')} -{' '}
                  {format(savedFilter.value[1], 'LLL dd, y')}
                </div>
              ) : (
                <div>{t('selectDateRange')}</div>
              )}
              <ChevronDown className="ml-4" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="popover-content w-auto p-0 flex flex-row"
          align="start"
        >
          <div className="presets-list flex flex-col justify-start items-start gap-1 p-2">
            {Object.keys(presets).map((key) => (
              <Button
                key={crypto.randomUUID()}
                variant="ghost"
                size="sm"
                onClick={() => handleDateRangePreset(key as DateRangePreset)}
                className={cn(
                  'w-full text-left font-normal justify-start',
                  preset === key && 'bg-accent'
                )}
                dataTestId="date-range-preset-button"
              >
                {presets?.[key as DateRangePreset].title}
              </Button>
            ))}
          </div>
          <div className="calendar-wrapper">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={tempDate?.from}
              selected={tempDate || { from: undefined, to: undefined }}
              onSelect={(value: { from?: Date; to?: Date } | undefined) => {
                if (value?.from != null) {
                  setTempDate({ from: value.from, to: value?.to })
                }
              }}
              numberOfMonths={2}
            />
            <div className="p-4 flex justify-between">
              <Button
                size="sm"
                onClick={handleClear}
                variant="secondary"
                className="capitalize"
                dataTestId="clear-date-range"
              >
                {t('clear')}
              </Button>
              <Button
                size="sm"
                onClick={handleApply}
                className="capitalize"
                disabled={!tempDate || !tempDate.from || !tempDate.to}
                dataTestId="apply-date-range"
              >
                {t('apply')}
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
