import { EqualIcon, EqualNotIcon, XIcon } from 'lucide-react'
import type { FilterItem, FilterOperator } from './filter-interfaces'
import { format } from 'date-fns'
import React from 'react'
import {
  Badge,
  Button,
  Separator,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '../../base'
import { t } from 'i18next'

interface DataTableFacetedFilterProps {
  id: string
  title: string
  isDate?: boolean
  enableDelete?: boolean
  getFilter: (field: string) => FilterItem | undefined
  deleteFilter: (field: string, op: FilterOperator, isSearch: boolean) => void
}

export function ActiveFilterBadge({
  id,
  title,
  isDate,
  getFilter,
  enableDelete = true,
  deleteFilter,
}: DataTableFacetedFilterProps) {
  const currentFilter = getFilter(id)
  const { value } = currentFilter || {}
  const isValueArray = Array.isArray(value)
  const isExistOperator = currentFilter?.op === 'exist'

  if (!currentFilter) return null

  if (isDate) {
    return (
      <Badge className="h-8 border border-slate-500 bg-slate-100 rounded-full text-slate-500 hover:bg-slate-100 capitalize relative flex items-center shadow-none">
        {t(title)}
        <Separator orientation="vertical" className="mx-2 h-4" />
        <div className="font-normal">
          {format(currentFilter.value[0], 'LLL dd, y')} -{' '}
          {format(currentFilter.value[1], 'LLL dd, y')}
        </div>
        <div className="mx-1">
          <Button
            onClick={() =>
              deleteFilter(
                currentFilter?.field || '',
                currentFilter?.op as FilterOperator,
                false
              )
            }
            variant="ghost"
            className="h-5 w-6 text-slate-600 hover:bg-slate-200 rounded-full"
            dataTestId="date-delete-filter-button"
          >
            <XIcon className="h-3.5 w-3.5" />
          </Button>
        </div>
      </Badge>
    )
  }

  return (
    <Badge className="h-8 border border-slate-500 bg-slate-100 text-slate-500 hover:bg-slate-100 capitalize relative flex items-center shadow-none">
      <Tooltip disableHoverableContent>
        <TooltipTrigger asChild>
          <span className="flex">
            <div>{t(title)}</div>
            <div className="mx-2 flex items-center">
              {isExistOperator ? (
                '|'
              ) : currentFilter.op === 'between' ? (
                <EqualIcon size={12} />
              ) : currentFilter?.op === 'eq' ? (
                <EqualIcon size={12} />
              ) : (
                <EqualNotIcon size={12} />
              )}
            </div>
            <div className="hidden space-x-1 lg:flex">
              {isExistOperator ? (
                currentFilter.value === 'true' ? (
                  <div className="font-light">{t('hasValue')}</div>
                ) : (
                  <div className="font-light">{t('noValue')}</div>
                )
              ) : (
                <div className="font-light flex gap-2">
                  <span className="truncate max-w-[300px]">
                    {isValueArray ? value?.slice(0, 2)?.join(', ') : value}
                  </span>
                  {isValueArray && value?.length > 2 && (
                    <span className="text-slate-500 border border-slate-500 rounded-full px-2">
                      {`+${value?.length - 2} `}
                    </span>
                  )}
                </div>
              )}
            </div>
          </span>
        </TooltipTrigger>
        <TooltipContent className="max-w-[400px]">
          {isValueArray ? value?.join(', ') : value}
        </TooltipContent>
      </Tooltip>

      {enableDelete && (
        <div className="mx-1">
          <Button
            onClick={() =>
              deleteFilter(
                currentFilter?.field || '',
                currentFilter?.op as FilterOperator,
                false
              )
            }
            size="icon"
            variant="ghost"
            className="h-5 w-6 text-slate-600 hover:bg-slate-200 rounded-full"
            dataTestId="delete-filter-button"
          >
            <XIcon className="h-3.5 w-3.5" />
          </Button>
        </div>
      )}
    </Badge>
  )
}
