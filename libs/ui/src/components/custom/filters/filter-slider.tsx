import { useEffect, useState } from 'react'
import {
  Collapsible,
  Slider,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../../base'
import type { FilterItem } from './filter-interfaces'
import { cn } from '@libs/common'
import { ChevronDown, ChevronUp } from 'lucide-react'

interface FilterSliderProps {
  title: string
  filterKey: string
  savedFilter?: FilterItem
  onFilterChange: (filter: FilterItem) => void
}
export const FilterSlider = ({
  title,
  filterKey,
  savedFilter,
  onFilterChange,
}: FilterSliderProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [values, setValues] = useState([0, 100])

  const handleFilterChange = (values: number[]) => {
    setValues(values)
    const stringifiedValues = values.map(String)
    onFilterChange({
      field: filterKey,
      value: stringifiedValues,
      op: 'between',
    })
  }

  useEffect(() => {
    if (savedFilter) {
      const values = savedFilter.value as string[]
      const parsedValues = values.map((v) => parseInt(v))
      setValues(parsedValues)
    }
  }, [savedFilter])

  return (
    <Collapsible
      className={cn(
        'mb-4 w-full',
        isOpen ? 'border-b border-gray-200 mb-2 pb-2' : ''
      )}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <CollapsibleTrigger asChild>
        <div
          className={cn(
            'flex justify-between items-center gap-3 cursor-pointer capitalize mb-3 font-medium pb-2',
            isOpen ? '' : 'border-b border-gray-200'
          )}
        >
          <div className="flex gap-2 text-lg">
            <span>{title}</span>
            {savedFilter && <span>(1)</span>}
          </div>
          <div className="flex items-center gap-1">
            {isOpen ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
          </div>
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent className="w-[300px] py-4 space-y-4 px-2">
        <div
          className="relative"
          onPointerDown={(e) => e.stopPropagation()}
          onPointerMove={(e) => e.stopPropagation()}
        >
          <Slider
            onValueCommit={handleFilterChange}
            onValueChange={handleFilterChange}
            defaultValue={values}
            min={0}
            max={100}
            step={1}
          />
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
