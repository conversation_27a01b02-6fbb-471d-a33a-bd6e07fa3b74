import type { FilterItem, FilterOperator } from './filter-interfaces'
import { ChevronDown, ChevronUp, SearchIcon, XIcon } from 'lucide-react'
import { t } from 'i18next'
import React, { useEffect, useState } from 'react'
import { cn } from '@libs/common'
import {
  Button,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Input,
} from '../../base'

interface FilterSearchInputProps {
  title: string
  showOperator?: boolean
  filterKey: string
  savedFilter?: FilterItem
  minimal?: boolean
  onFilterChange: (filter: FilterItem) => void
}

export function FilterSearchInput({
  filterKey,
  title,
  showOperator = false,
  savedFilter,
  onFilterChange,
}: FilterSearchInputProps) {
  const [isOpen, setIsOpen] = useState(false)

  const [filterOperator, setFilterOperator] = useState(
    (savedFilter?.op as FilterOperator) || 'eq'
  )

  const [searchQuery, setSearchQuery] = useState(
    (savedFilter?.value as string) || ''
  )

  useEffect(() => {
    if (savedFilter) {
      setFilterOperator(savedFilter.op as FilterOperator)
      setSearchQuery(savedFilter.value as string)
    }
  }, [savedFilter])

  useEffect(() => {
    if (searchQuery) {
      onFilterChange({
        field: filterKey,
        value: searchQuery || '',
        op: filterOperator,
      })
    }
  }, [filterKey, filterOperator, onFilterChange, searchQuery])

  return (
    <Collapsible
      className={cn(
        'mb-4 w-full',
        isOpen ? 'border-b border-gray-200 mb-2 pb-2' : ''
      )}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <CollapsibleTrigger asChild>
        <div
          className={cn(
            'flex justify-between items-center gap-3 cursor-pointer capitalize mb-3 font-medium pb-2',
            isOpen ? '' : 'border-b border-gray-200'
          )}
        >
          <div className="flex gap-2 text-lg">
            <span>{title}</span>
            {savedFilter && <span>(1)</span>}
          </div>
          <div className="flex items-center gap-1">
            {isOpen ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
          </div>
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent className="w-[300px] py-4 space-y-4 px-2">
        {showOperator && (
          <div onClick={(e) => e.stopPropagation()}>
            <Select
              defaultValue={filterOperator}
              onValueChange={(value) =>
                setFilterOperator(value as FilterOperator)
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent align="center">
                <SelectItem value="eq">{t('eq')}</SelectItem>
                <SelectItem value="ne">{t('ne')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
        <div className="relative">
          <SearchIcon className="absolute left-2.5 top-[8px] h-4 w-4 text-muted-foreground" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onBlur={() => {
              if (
                searchQuery !== savedFilter?.value ||
                filterOperator !== savedFilter?.op
              ) {
                onFilterChange({
                  field: filterKey,
                  value: searchQuery || '',
                  op: filterOperator,
                })
              }
            }}
            className="h-8 bg-white pl-8"
            placeholder="Search here..."
          />
          {searchQuery && (
            <Button
              variant="ghost"
              className="absolute capitalize text-muted-foreground right-2.5 top-[-1px]  bg-transparent px-0"
              dataTestId="clear-search"
              onClick={() => {
                setSearchQuery('')
                onFilterChange({
                  field: filterKey,
                  value: '',
                  op: filterOperator,
                })
              }}
            >
              <XIcon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
