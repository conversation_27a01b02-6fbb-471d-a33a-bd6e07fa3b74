import React, { useEffect } from 'react'
import { Check, Plus, Tag, XIcon } from 'lucide-react'
import { cn } from '@libs/common'
import {
  Badge,
  Button,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../base'
import { t } from 'i18next'

interface TagsSelectorProps {
  selectedTags: string[]
  availableTags: string[]
  onTagsChange: (tags: string[]) => void
  onOpenChange?: (open: boolean) => void
  tagIcon?: boolean
  className?: string
  showSelected?: boolean
  placeholder?: string
  noTagsMessage?: string
  createNewTag?: boolean
  maxHeight?: string
  disabled?: boolean
  customTrigger?: string
  validateTag?: (tag: string) => string | undefined
  renderTag?: (tag: string, onDelete: () => void) => React.ReactNode
  translations?: {
    addTag?: string
    searchTags?: string
    create?: string
    error?: string
  }
}

export const TagsSelector = ({
  selectedTags = [],
  availableTags = [],
  onTagsChange,
  className,
  tagIcon = false,
  placeholder = 'Search tags...',
  noTagsMessage = 'No tags found',
  createNewTag = true,
  showSelected = true,
  maxHeight = '15rem',
  disabled = false,
  validateTag,
  renderTag,
  customTrigger,
  onOpenChange = () => {
    return
  },
  translations = {
    addTag: t('addTag'),
    searchTags: t('searchTags'),
    create: t('createTag'),
    error: t('errorTag'),
  },
}: TagsSelectorProps) => {
  const [open, setOpen] = React.useState(false)
  const [search, setSearch] = React.useState('')
  const [error, setError] = React.useState('')

  const handleSelect = (currentValue: string) => {
    if (disabled) return

    const nextTags = selectedTags.includes(currentValue)
      ? selectedTags.filter((tag) => tag !== currentValue)
      : [...selectedTags, currentValue]

    onTagsChange(nextTags)
  }

  const handleDelete = (tagToDelete: string) => {
    if (disabled) return

    const nextTags = selectedTags.filter((tag) => tag !== tagToDelete)
    onTagsChange(nextTags)
  }

  const handleCreateNew = () => {
    if (disabled) return

    const newTag = search.trim()

    if (validateTag) {
      const validationError = validateTag(newTag)
      if (validationError) {
        setError(validationError)
        return
      }
    }

    if (newTag && !availableTags.includes(newTag)) {
      onTagsChange([...selectedTags, newTag])
      setSearch('')
      setError('')
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && createNewTag) {
      event.preventDefault()
      handleCreateNew()
    }
  }

  useEffect(() => {
    onOpenChange(open)
  }, [open])

  const defaultRenderTag = (tag: string, onDelete: () => void) => (
    <Badge
      key={tag}
      variant="secondary"
      className="rounded-full text-gray-400 border-gray-400 h-6 bg-white font-light gap-2"
    >
      <span>{tag}</span>
      <Button
        onClick={onDelete}
        size="icon"
        variant="ghost"
        className="w-3.5 h-3.5 hover:bg-slate-200"
        disabled={!!disabled}
        dataTestId="delete-tag-button"
      >
        <XIcon />
      </Button>
    </Badge>
  )

  return (
    <div className={cn('flex flex-wrap gap-2', className || '')}>
      {showSelected &&
        selectedTags.map((tag) =>
          renderTag
            ? renderTag(tag, () => handleDelete(tag))
            : defaultRenderTag(tag, () => handleDelete(tag))
        )}

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          {customTrigger ? (
            <Button
              variant="ghost"
              className="capitalize"
              dataTestId="add-tag-button"
            >
              {!!tagIcon && <Tag className="mr-1 h-4 w-4" />}
              {customTrigger}
            </Button>
          ) : (
            <Button
              variant="outline"
              className="rounded-full h-6 gap-2"
              dataTestId="add-tag-button"
              disabled={disabled}
            >
              <span>{translations.addTag}</span>
              <Plus className="text-slate-800" size={16} />
            </Button>
          )}
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder={placeholder}
              value={search}
              onValueChange={(value) => {
                setSearch(value)
                setError('')
              }}
              onKeyDown={handleKeyDown}
            />
            {error && (
              <span className="p-2 text-destructive-foreground text-xs">
                {error}
              </span>
            )}

            <CommandEmpty className="p-2">
              {createNewTag ? (
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  dataTestId="create-new-tag"
                  onClick={handleCreateNew}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  <span className="capitalize mr-1">
                    {translations.create}
                  </span>{' '}
                  "{search}"
                </Button>
              ) : (
                <p className="text-sm text-muted-foreground">{noTagsMessage}</p>
              )}
            </CommandEmpty>

            <CommandGroup className="overflow-auto" style={{ maxHeight }}>
              {availableTags.map((tag) => (
                <CommandItem key={tag} onSelect={() => handleSelect(tag)}>
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      selectedTags.includes(tag) ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  {tag}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
