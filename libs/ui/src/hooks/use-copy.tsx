import { useState, useCallback } from 'react'

type UseCopyResult = {
  copyToClipboard: (text: string) => Promise<void>
  isCopied: boolean
  error: string | null
}

export const useCopy = (): UseCopyResult => {
  const [isCopied, setIsCopied] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setIsCopied(true)
      setError(null)
      setTimeout(() => setIsCopied(false), 2000) // Reset `isCopied` after 2 seconds
    } catch (err) {
      setError('Failed to copy text')
      setIsCopied(false)
    }
  }, [])

  return { copyToClipboard, isCopied, error }
}
