import { useState, useEffect } from 'react'
import { toast } from 'sonner'

const getOnLineStatus = () =>
  typeof navigator !== 'undefined' && typeof navigator.onLine === 'boolean'
    ? navigator.onLine
    : true

export const useOnLine = () => {
  const [status, setStatus] = useState(getOnLineStatus())
  const [isReconnected, setIsReconnected] = useState(false)

  const setOnline = () => {
    setStatus(true)
    setIsReconnected(true)
  }

  const setOffline = () => setStatus(false)

  useEffect(() => {
    if (!status && !isReconnected) {
      toast.error('You are offline')
    } else if (isReconnected) {
      toast.success('You are online')
      setIsReconnected(false)
    }
  }, [status, isReconnected])

  useEffect(() => {
    window.addEventListener('online', setOnline)
    window.addEventListener('offline', setOffline)

    return () => {
      window.removeEventListener('online', setOnline)
      window.removeEventListener('offline', setOffline)
    }
  }, [])

  return status
}
