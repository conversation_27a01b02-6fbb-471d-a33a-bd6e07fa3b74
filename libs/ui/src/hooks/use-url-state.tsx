import qs from 'query-string'
import type { ParseOptions, StringifyOptions } from 'query-string'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useLocation, useNavigate } from 'react-router'

export interface UseUrlStateOptions {
  navigateMode?: 'push' | 'replace'
  parseOptions?: ParseOptions
  stringifyOptions?: StringifyOptions
  path?: string
}

const baseParseConfig: ParseOptions = {
  parseNumbers: false,
  parseBooleans: false,
}

const baseStringifyConfig: StringifyOptions = {
  skipNull: true,
  skipEmptyString: true,
  encode: false,
}

type UrlState = Record<string, any>

const useUpdate = () => {
  const [, setState] = useState({})
  return useCallback(() => setState({}), [])
}

function useMemoizedFn<T extends (...args: any[]) => any>(fn: T): T {
  const fnRef = useRef<T>(fn)
  fnRef.current = fn
  const memoizedFn = useCallback((...args: Parameters<T>): ReturnType<T> => {
    return fnRef.current(...args)
  }, [])
  return memoizedFn as T
}

const isObject = (value: any): value is Record<string, any> =>
  typeof value === 'object' && value !== null && !Array.isArray(value)

const serializeValue = (value: any): string => {
  if (value === null || value === undefined) {
    return ''
  }
  if (Array.isArray(value) || isObject(value)) {
    return JSON.stringify(value)
  }
  return String(value)
}

const deserializeValue = (value: string): any => {
  try {
    return JSON.parse(value)
  } catch {
    return value
  }
}

const escapeAmpersands = (value: any): any => {
  if (typeof value === 'string') {
    return value.replace(/&/g, '%26')
  }
  if (Array.isArray(value)) {
    return value.map(escapeAmpersands)
  }
  if (typeof value === 'object' && value !== null) {
    return Object.entries(value).reduce(
      (acc, [key, val]) => ({
        ...acc,
        [key]: escapeAmpersands(val),
      }),
      {}
    )
  }
  return value
}

export const useUrlState = <S extends UrlState = UrlState>(
  initialState?: S | (() => S),
  options?: UseUrlStateOptions
) => {
  type State = Partial<{ [key in keyof S]: any }>

  const {
    navigateMode = 'push',
    parseOptions,
    stringifyOptions,
    path,
  } = options || {}

  const mergedParseOptions = { ...baseParseConfig, ...parseOptions }
  const mergedStringifyOptions = { ...baseStringifyConfig, ...stringifyOptions }

  const location = useLocation()
  const navigate = useNavigate()
  const update = useUpdate()

  const initialStateRef = useRef<S>(
    typeof initialState === 'function'
      ? (initialState as () => S)()
      : initialState || ({} as S)
  )

  const queryFromUrl = useMemo(() => {
    const parsed = qs.parse(location.search, mergedParseOptions)
    return Object.fromEntries(
      Object.entries(parsed).map(([key, value]) => [
        key,
        typeof value === 'string' ? deserializeValue(value) : value,
      ])
    ) as State
  }, [location.search])

  const targetQuery: State = useMemo(
    () => ({
      ...initialStateRef.current,
      ...queryFromUrl,
    }),
    [queryFromUrl]
  )

  useEffect(() => {
    const initialStateKeys = Object.keys(initialStateRef.current) as Array<
      keyof S
    >
    const missingKeys = initialStateKeys.filter((key) => !(key in queryFromUrl))

    if (missingKeys.length > 0) {
      const newQuery: State = { ...queryFromUrl }
      missingKeys.forEach((key) => {
        newQuery[key] = initialStateRef.current[key]
      })

      navigate(
        {
          hash: location.hash,
          search:
            qs.stringify(
              Object.fromEntries(
                Object.entries(newQuery).map(([key, value]) => [
                  key,
                  serializeValue(value),
                ])
              ),
              mergedStringifyOptions
            ) || '?',
        },
        {
          replace: true,
          state: location.state,
        }
      )
    }
  }, [])

  const setState = (s: React.SetStateAction<State>) => {
    const newQuery = typeof s === 'function' ? s(targetQuery) : s
    const { __path, ...restQuery } = newQuery

    // Filter out undefined values
    const filteredQuery = Object.fromEntries(
      Object.entries(restQuery).filter(([_, value]) => value !== undefined)
    )

    const processedQuery = Object.entries(filteredQuery).reduce(
      (acc, [key, value]) => {
        if (
          Array.isArray(value) ||
          (typeof value === 'object' && value !== null)
        ) {
          // For arrays and objects, stringify and escape ampersands
          const stringified = JSON.stringify(escapeAmpersands(value))
          acc[key] = stringified
        } else {
          // For primitive values, just escape ampersands
          acc[key] = escapeAmpersands(value)
        }
        return acc
      },
      {} as Record<string, any>
    )

    update()
    if (navigate) {
      const searchString =
        qs.stringify(processedQuery, mergedStringifyOptions) || '?'

      navigate(
        {
          hash: location.hash,
          search: searchString,
          pathname: __path || path || location.pathname,
        },
        {
          replace: navigateMode === 'replace',
          state: location.state,
        }
      )
    }
  }

  return [targetQuery, useMemoizedFn(setState)] as const
}
