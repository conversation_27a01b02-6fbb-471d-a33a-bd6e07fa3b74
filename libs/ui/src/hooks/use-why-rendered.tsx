import { useRef, useEffect } from 'react'

type Props = Record<string, any>

interface ChangesObj {
  [key: string]: {
    from: any
    to: any
  }
}

export function useWhyRendered<T extends Props>(name: string, props: T): void {
  const prevProps = useRef<T | null>(null)

  useEffect(() => {
    if (prevProps.current) {
      const allKeys = Object.keys({ ...prevProps.current, ...props })
      const changesObj: ChangesObj = {}

      allKeys.forEach((key) => {
        if (prevProps.current?.[key] !== props[key]) {
          changesObj[key] = {
            from: prevProps.current?.[key],
            to: props[key],
          }
        }
      })

      if (Object.keys(changesObj).length) {
        console.log(`[why-rendered] ${name}:`, changesObj)
      }
    }

    prevProps.current = props
  })
}
