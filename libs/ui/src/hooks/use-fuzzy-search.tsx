import { useState, useMemo } from 'react'

type SearchableItem = Record<string, any>

interface FuzzySearchOptions<T extends SearchableItem> {
  keys?: Array<keyof T>
  threshold?: number
  caseSensitive?: boolean
}

interface SearchResult<T> {
  performSearch: (query: string) => void
  results: T[]
}

interface ScoredResult<T> {
  item: T
  score: number
}

export function useFuzzySearch<T extends SearchableItem>(
  items: T[],
  options: FuzzySearchOptions<T> = {}
): SearchResult<T> {
  const [results, setResults] = useState<T[]>(items)

  const {
    keys = [] as Array<keyof T>,
    threshold = 0.4,
    caseSensitive = false,
  } = options

  const getLevenshteinDistance = (str1: string, str2: string): number => {
    const m = str1.length
    const n = str2.length
    const dp: number[][] = Array(m + 1)
      .fill(0)
      .map(() => Array(n + 1).fill(0))

    // Initialize first row and column
    for (let i = 0; i <= m; i++) dp[i][0] = i
    for (let j = 0; j <= n; j++) dp[0][j] = j

    // Fill dp table
    for (let i = 1; i <= m; i++) {
      for (let j = 1; j <= n; j++) {
        if (str1[i - 1] === str2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1]
        } else {
          dp[i][j] = Math.min(
            dp[i - 1][j - 1] + 1, // substitution
            dp[i - 1][j] + 1, // deletion
            dp[i][j - 1] + 1 // insertion
          )
        }
      }
    }

    return dp[m][n]
  }

  const calculateSimilarity = useMemo(
    () =>
      (text: string, query: string): number => {
        if (!text || !query) return 0

        const str1 = caseSensitive ? text : text.toLowerCase()
        const str2 = caseSensitive ? query : query.toLowerCase()

        // Exact match
        if (str1 === str2) return 1

        // Handle substring matches with high score
        if (str1.includes(str2)) return 0.9

        // Split into words and check each word for fuzzy matches
        const words = str1.split(/[\s-_]+/)

        // Get best fuzzy match score among all words
        const bestScore = Math.max(
          ...words.map((word) => {
            // For very short queries, be more lenient
            if (str2.length <= 3) {
              // Direct substring check for short queries
              if (word.includes(str2) || str2.includes(word)) return 0.8
            }

            const maxLength = Math.max(word.length, str2.length)
            const distance = getLevenshteinDistance(word, str2)

            // Convert distance to similarity score (0 to 1)
            return 1 - distance / maxLength
          })
        )

        return bestScore
      },
    [caseSensitive]
  )

  const performSearch = useMemo(
    () =>
      (query: string): void => {
        if (!query.trim()) {
          setResults(items)
          return
        }

        const searchResults: T[] = items
          .map((item): ScoredResult<T> => {
            let searchFields: string[]

            if (keys.length > 0) {
              searchFields = keys
                .map((key) => {
                  const value = item[key]
                  return value ? String(value) : ''
                })
                .filter(Boolean)
            } else {
              searchFields = Object.entries(item)
                .map(([_, value]) => (value ? String(value) : ''))
                .filter(Boolean)
            }

            const scores = searchFields.map((field) =>
              calculateSimilarity(field, query)
            )
            const maxScore = Math.max(...scores, 0)

            return {
              item,
              score: maxScore,
            }
          })
          .filter((result) => result.score >= threshold)
          .sort((a, b) => b.score - a.score)
          .map((result) => result.item)

        setResults(searchResults)
      },
    [items, keys, calculateSimilarity, threshold]
  )

  return { performSearch, results }
}
