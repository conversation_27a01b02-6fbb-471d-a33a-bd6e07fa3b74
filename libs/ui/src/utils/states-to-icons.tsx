import type { RiskScoreCategory } from 'prime-front-service-client'

export const riskScoreCategoryColors: Record<RiskScoreCategory, string> = {
  intervene: 'bg-red-100 text-red-900 hover:bg-red-200',
  analyze: 'bg-orange-100 text-orange-900 hover:bg-orange-200',
  monitor: 'bg-yellow-100 text-yellow-900 hover:bg-yellow-200',
  None: 'bg-gray-100 text-gray-900 hover:bg-gray-200',
}

export const riskScoreCategoryToClass: Record<RiskScoreCategory, string> = {
  intervene:
    'capitalize rounded-full h-8 font-semibold border-none text-sm bg-red-100 text-red-900 hover:bg-red-200',
  analyze:
    'capitalize rounded-full h-8 font-semibold border-none text-sm bg-orange-100 text-orange-900 hover:bg-orange-200',
  monitor:
    'capitalize rounded-full h-8 font-semibold border-none text-sm bg-yellow-100 text-yellow-900 hover:bg-yellow-200',
  None: 'capitalize rounded-full h-8 font-semibold border-none text-sm bg-gray-100 text-gray-900 hover:bg-gray-200',
}
