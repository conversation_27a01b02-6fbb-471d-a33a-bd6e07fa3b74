/// <reference types='vitest' />
import { defineConfig } from 'vite'

import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin'

export default defineConfig({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/libs/ui',
  plugins: [nxViteTsPaths()],
  test: {
    globals: true,
    cache: {
      dir: '../../node_modules/.vitest',
    },
    environment: 'jsdom',
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default'],
    coverage: {
      reportsDirectory: '../../coverage/libs/ui',
      exclude: [
        '**/index.ts',
        '**/columns.tsx',
        '**/icons/**',
        '**/components/base/**',
        '**/components/custom/**chart.tsx',
      ],
      provider: 'v8',
    },
    alias: [
      {
        find: '@nivo/annotations',
        replacement: '@nivo/annotations/dist/nivo-annotations.es.js',
      },
      { find: '@nivo/arcs', replacement: '@nivo/arcs/dist/nivo-arcs.es.js' },
      { find: '@nivo/axes', replacement: '@nivo/axes/dist/nivo-axes.es.js' },
      { find: '@nivo/bar', replacement: '@nivo/bar/dist/nivo-bar.es.js' },
      {
        find: '@nivo/colors',
        replacement: '@nivo/colors/dist/nivo-colors.es.js',
      },
      { find: '@nivo/core', replacement: '@nivo/core/dist/nivo-core.es.js' },
      {
        find: '@nivo/legends',
        replacement: '@nivo/legends/dist/nivo-legends.es.js',
      },
      { find: '@nivo/line', replacement: '@nivo/line/dist/nivo-line.es.js' },
      { find: '@nivo/pie', replacement: '@nivo/pie/dist/nivo-pie.es.js' },
      {
        find: '@nivo/recompose',
        replacement: '@nivo/recompose/dist/nivo-recompose.es.js',
      },
      {
        find: '@nivo/scales',
        replacement: '@nivo/scales/dist/nivo-scales.es.js',
      },
      {
        find: '@nivo/scatterplot',
        replacement: '@nivo/scatterplot/dist/nivo-scatterplot.es.js',
      },
      {
        find: '@nivo/tooltip',
        replacement: '@nivo/tooltip/dist/nivo-tooltip.es.js',
      },
      {
        find: '@nivo/voronoi',
        replacement: '@nivo/voronoi/dist/nivo-voronoi.es.js',
      },
      {
        find: '@nivo/waffle',
        replacement: '@nivo/waffle/dist/nivo-waffle.es.js',
      },
      {
        find: '@nivo/heatmap',
        replacement: '@nivo/heatmap/dist/nivo-heatmap.es.js',
      },
      {
        find: '@nivo/generators',
        replacement: '@nivo/generators/dist/nivo-generators.es.js',
      },
    ],
  },
})
