# Primesec frontend Dashboard 

This is a monorepo project bootstrapped with [NX](https://nx.dev/).

##  Tech stack

- 🌐 [Next.js](https://nextjs.org/) - React framework
- 📦 [pnpm](https://pnpm.io/) - Package manager
- 🎨 [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- 🖼️ [shadcn/ui](https://ui.shadcn.com/) - UI components
- 🧪 [Vitest](https://vitest.dev/) - Test runner
- 🚩 [Playwright](https://playwright.dev/) - E2E testing

## Getting Started

### Prerequisites

Install [nodejs](https://nodejs.org/en/download/) version 20 or higher

> It's preferable to use [nvm](https://github.com/nvm-sh/nvm)

Enable corepack:
Corepack is included by default with all Node.js installs, but is currently opt-in. To enable it, run the following command:

```bash
corepack enable
```

This will automatically install pnpm on your system. However, it probably won't be the latest version of pnpm. To upgrade it, check what is the [latest pnpm version](https://github.com/pnpm/pnpm/releases/latest) and run:

```bash
corepack prepare pnpm@<version> --activate
```

With Node.js v16.17 or newer, you may install the latest version of pnpm by just specifying the tag:

```bash
corepack prepare pnpm@latest --activate
```

#### install the dependencies

```bash
pnpm install
```

## Contribution Guide

### Naming conventions

- `kebab-case.ts` for file names
- `UPPERCASE`, `CONSTANT_CASE`, or `camelCase` for constants
- `PascalCase` for TypeScript interfaces/types and Classes
- `kebab-case` for HTML attributes such class names, id, key, and data attributes
- `camelCase` for general code

### Coding Best Practices

- ⚠️ **No comments** - Remove any commented-out code
- ⚠️ **No console logs** - Use **devConsoleLog()** function if necessary
- ⚠️ **No unused code and dependencies** - Remove unused files, variables, functions, and any code and dependencies that are not in use
- ⚠️ **Try not to install new dependencies**
  - If you have to install a new dependency, try to import (copy-paste) the code to one of our libs
- ⚠️ **Avoid [spaghetti code](https://en.wikipedia.org/wiki/Spaghetti_code)** - Strive for clean and maintainable code structure.

### Code Formatter

We use an automatic code formatter called [Prettier](https://prettier.io/).

In your IDE, install the Prettier extension and set `formatOnSave` to `true`.

Check the Prettier extension for VSCode:

[Prettier extension](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

## Periodical deps updates and cleanups

Update the dependencies every 2 weeks and remove unused dependencies.

Check updates

```bash
pnpm outdated
```

> Read more about [pnpm outdated](https://pnpm.io/cli/outdated)

Output example:

```
┌───────────────────────────────────────────────────────────┬──────────────────────────────────────┬──────────────────────────────────────┐
│ Package                                                   │ Current                              │ Latest                               │
├───────────────────────────────────────────────────────────┼──────────────────────────────────────┼──────────────────────────────────────┤
│ http2                                                     │ 3.3.7                                │ Deprecated                           │
├───────────────────────────────────────────────────────────┼──────────────────────────────────────┼──────────────────────────────────────┤
│ @bufbuild/protobuf                                        │ 1.3.0                                │ 1.3.1                                │
├───────────────────────────────────────────────────────────┼──────────────────────────────────────┼──────────────────────────────────────┤
│ @types/react-dom                                          │ 18.2.6                               │ 18.2.7                               │
├───────────────────────────────────────────────────────────┼──────────────────────────────────────┼──────────────────────────────────────┤
│ autoprefixer                                              │ 10.4.14                              │ 10.4.15                              │
├───────────────────────────────────────────────────────────┼──────────────────────────────────────┼──────────────────────────────────────┤
│ eslint-config-next                                        │ 13.4.9                               │ 13.4.19                              │
├───────────────────────────────────────────────────────────┼──────────────────────────────────────┼──────────────────────────────────────┤
│ framer-motion                                             │ 10.16.1                              │ 10.16.3                              │

```

Update `framer-motion`

```bash
pnpm update framer-motion
```

> Read more about [pnpm update](https://pnpm.io/cli/update)
