import { useEffect } from 'react'

interface MetaTagsProps {
  title: string
  description: string
  siteName: string
  imageUrl?: string // For social media or messengers sharing
}

export const MetaTagsManager = ({
  title,
  description,
  siteName,
  imageUrl = '',
}: MetaTagsProps) => {
  useEffect(() => {
    document.title = `${title} | ${siteName}`

    let metaDescription = document.querySelector<HTMLMetaElement>(
      'meta[name="description"]'
    )
    if (!metaDescription) {
      metaDescription = document.createElement('meta')
      metaDescription.setAttribute('name', 'description')
      document.head.appendChild(metaDescription)
    }
    metaDescription.setAttribute('content', description)

    if (imageUrl) {
      let metaImage = document.querySelector<HTMLMetaElement>(
        'meta[property="og:image"]'
      )
      if (!metaImage) {
        metaImage = document.createElement('meta')
        metaImage.setAttribute('property', 'og:image')
        document.head.appendChild(metaImage)
      }
      metaImage.setAttribute('content', imageUrl)
    }

    let metaTwitterTitle = document.querySelector<HTMLMetaElement>(
      'meta[name="twitter:title"]'
    )
    if (!metaTwitterTitle) {
      metaTwitterTitle = document.createElement('meta')
      metaTwitterTitle.setAttribute('name', 'twitter:title')
      document.head.appendChild(metaTwitterTitle)
    }
    metaTwitterTitle.setAttribute('content', `${title} | ${siteName}`)

    return () => {
      document.title = siteName
      metaDescription.setAttribute('content', '')
      if (imageUrl) {
        const metaImage = document.querySelector<HTMLMetaElement>(
          'meta[property="og:image"]'
        )
        if (metaImage) {
          metaImage.setAttribute('content', imageUrl)
        }
      }
    }
  }, [title, description, siteName, imageUrl])

  return null
}
