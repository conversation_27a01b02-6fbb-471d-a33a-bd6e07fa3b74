/* eslint-disable max-lines */

import {
  cn,
  extractJiraSubdomainName,
  snakeCaseToCamelCase,
  camelCaseToSnakeCase,
  parseSortParam,
  getUserUTCHoursOffset,
  getUserUTCOffsetFormatted,
  convertTimeToUTC,
  getUserTimezoneOffset,
  convertUTCToLocal,
} from './utils'
import { describe } from 'vitest'

describe('cn function', () => {
  it('should return a string', () => {
    const result = cn('foo', 'bar')
    expect(typeof result).toBe('string')
  })

  it('should merge class names correctly', () => {
    const result = cn('foo', 'bar')
    expect(result).toBe('foo bar')
  })
})

describe('snakeCaseToCamelCase function', () => {
  it('should return a string', () => {
    const result = snakeCaseToCamelCase('foo_bar')
    expect(typeof result).toBe('string')
  })

  it('should convert snake_case to camelCase', () => {
    const result = snakeCaseToCamelCase('foo_bar')
    expect(result).toBe('fooBar')
  })

  it('should convert snake_case to camelCase', () => {
    const result = snakeCaseToCamelCase({
      foo_bar: 'baz',
      qux_quux: 'quuz',
    })

    const camelCaseData = {
      fooBar: 'baz',
      quxQuux: 'quuz',
    }
    expect(result).toMatchObject(camelCaseData)
  })
})

describe('extractJiraSubdomainName', () => {
  it('returns the input URL if it does not contain "atlassian"', () => {
    const url = 'https://example.com'
    expect(extractJiraSubdomainName(url)).toBe(url)
  })

  it('returns the subdomain from the URL if it contains "atlassian"', () => {
    const url = 'https://mycompany.atlassian.net'
    expect(extractJiraSubdomainName(url)).toBe('mycompany')
  })

  it('handles URLs with http protocol', () => {
    const url = 'http://mycompany.atlassian.net'
    expect(extractJiraSubdomainName(url)).toBe('mycompany')
  })

  it('handles URLs with subdirectories', () => {
    const url = 'https://mycompany.atlassian.net/projects/PROJ'
    expect(extractJiraSubdomainName(url)).toBe('mycompany')
  })

  it('handles URLs with additional subdomains', () => {
    const url = 'https://subdomain.mycompany.atlassian.net'
    expect(extractJiraSubdomainName(url)).toBe('subdomain')
  })

  it('handles URLs with port numbers', () => {
    const url = 'https://mycompany.atlassian.net:8080'
    expect(extractJiraSubdomainName(url)).toBe('mycompany')
  })

  it('handles URLs without protocol', () => {
    const url = 'mycompany.atlassian.net'
    expect(extractJiraSubdomainName(url)).toBe('mycompany')
  })
})

describe('camelCaseToSnakeCase function', () => {
  it('should return a string', () => {
    const result = camelCaseToSnakeCase('foo_bar')
    expect(typeof result).toBe('string')
  })

  it('should convert camelCase to snake_case', () => {
    const result = camelCaseToSnakeCase('fooBar')
    expect(result).toBe('foo_bar')
  })

  it('should convert camelCase to snake_case', () => {
    const result = camelCaseToSnakeCase({
      fooBar: 'baz',
      quxQuux: 'quuz',
    })

    const snakeCaseData = {
      foo_bar: 'baz',
      qux_quux: 'quuz',
    }
    expect(result).toMatchObject(snakeCaseData)
  })
})

describe('parseSortString', () => {
  it('should parse a simple sort string', () => {
    const input = 'status:asc'
    const expectedOutput = { field: 'status', order: 'asc' }
    const result = parseSortParam(input)
    expect(result).toEqual(expectedOutput)
  })

  it('should handle different field and order', () => {
    const input = 'name:desc'
    const expectedOutput = { field: 'name', order: 'desc' }
    const result = parseSortParam(input)
    expect(result).toEqual(expectedOutput)
  })

  it('should return undefined for missing order', () => {
    const input = 'status:'
    const expectedOutput = { field: 'status', order: '' }
    const result = parseSortParam(input)
    expect(result).toEqual(expectedOutput)
  })

  it('should handle no colon in input', () => {
    const input = 'status'
    const expectedOutput = { field: 'status', order: undefined }
    const result = parseSortParam(input)
    expect(result).toEqual(expectedOutput)
  })
})

describe('getUserUTCHoursOffset', () => {
  it('should return a number', () => {
    const result = getUserUTCHoursOffset()
    expect(typeof result).toBe('number')
  })

  it("should return the user's UTC offset in hours", () => {
    const result = getUserUTCHoursOffset()
    expect(result).toBe(new Date().getTimezoneOffset() / -60)
  })

  it('should return the correct offset for UTC+3', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-180) // UTC+3
    const expectedOffset = 3
    const result = getUserUTCHoursOffset()
    expect(result).toBe(expectedOffset)
    vi.restoreAllMocks()
  })

  it('should return UTC+3 when getTimezoneOffset is mocked to -180 minutes', () => {
    // Mock getTimezoneOffset to return -180 minutes (UTC+3)
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-180)

    const offset = getUserUTCHoursOffset()
    expect(offset).toBe(3) // UTC+3

    vi.restoreAllMocks() // Restore original Date behavior after the test
  })

  it('should return UTC-5 when getTimezoneOffset is mocked to 300 minutes', () => {
    // Mock getTimezoneOffset to return 300 minutes (UTC-5)
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(300)

    const offset = getUserUTCHoursOffset()
    expect(offset).toBe(-5) // UTC-5

    vi.restoreAllMocks() // Restore original Date behavior after the test
  })

  it('should return UTC+0 when getTimezoneOffset is mocked to 0 minutes', () => {
    // Mock getTimezoneOffset to return 0 minutes (UTC+0)
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(0)

    const offset = getUserUTCHoursOffset()
    expect(Math.abs(offset)).toBe(0) // Ignores the sign of zero

    vi.restoreAllMocks() // Restore original Date behavior after the test
  })
})

describe('getUserUTCOffsetFormatted', () => {
  it('should return +09:00 when getTimezoneOffset is mocked to -540 minutes (UTC+09:00)', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-540) // UTC+09:00

    const offset = getUserUTCOffsetFormatted()
    expect(offset).toBe('+09:00')

    vi.restoreAllMocks()
  })

  it('should return -05:30 when getTimezoneOffset is mocked to 330 minutes (UTC-05:30)', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(330) // UTC-05:30

    const offset = getUserUTCOffsetFormatted()
    expect(offset).toBe('-05:30')

    vi.restoreAllMocks()
  })

  it('should return +00:00 when getTimezoneOffset is mocked to 0 minutes (UTC+00:00)', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(0) // UTC+00:00

    const offset = getUserUTCOffsetFormatted()
    expect(offset).toBe('+00:00')

    vi.restoreAllMocks()
  })
})

describe('convertTimeToUTC', () => {
  it('converts time correctly for UTC+3 timezone', () => {
    // Mock getTimezoneOffset to return -180 minutes (UTC+3)
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-180)
    expect(convertTimeToUTC('21:00', '+03:00')).toBe('18:00')
  })

  it('converts time correctly for UTC-5 timezone', () => {
    // Mock getTimezoneOffset to return 300 minutes (UTC-5)
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(300)
    expect(convertTimeToUTC('15:00', '-05:00')).toBe('20:00')
  })

  it('handles midnight correctly', () => {
    // Mock getTimezoneOffset to return -60 minutes (UTC+1)
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-60)
    expect(convertTimeToUTC('00:00', '+01:00')).toBe('23:00')
  })

  it('handles noon correctly', () => {
    // Mock getTimezoneOffset to return 0 minutes (UTC+0)
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(0)
    expect(convertTimeToUTC('12:00', '+00:00')).toBe('12:00')
  })

  it('works with half-hour timezones', () => {
    // Mock getTimezoneOffset to return -330 minutes (UTC+5:30)
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-330)
    expect(convertTimeToUTC('18:30', '+05:30')).toBe('13:00')
  })

  it('works with positive half-hour timezones', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-330)
    expect(convertTimeToUTC('18:30', '+05:30')).toBe('13:00')
  })

  it('works with negative half-hour timezones', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(210)
    expect(convertTimeToUTC('09:00', '-03:30')).toBe('12:30')
  })
})

describe('getUserTimezoneOffset', () => {
  it('returns correct offset for UTC+3', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-180)
    expect(getUserTimezoneOffset()).toBe('+03:00')
  })

  it('returns correct offset for UTC-5', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(300)
    expect(getUserTimezoneOffset()).toBe('-05:00')
  })

  it('handles zero offset correctly', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(0)
    expect(getUserTimezoneOffset()).toBe('+00:00')
  })

  it('handles half-hour timezones correctly', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-330)
    expect(getUserTimezoneOffset()).toBe('+05:30')
  })

  it('handles positive half-hour timezones correctly', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-330)
    expect(getUserTimezoneOffset()).toBe('+05:30')
  })

  it('handles negative half-hour timezones correctly', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(210)
    expect(getUserTimezoneOffset()).toBe('-03:30')
  })
})

describe('convertUTCToLocal', () => {
  it('converts UTC time to local time for UTC+3 timezone', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-180)
    expect(convertUTCToLocal('18:00', '+03:00')).toBe('21:00')
  })

  it('converts UTC time to local time for UTC-5 timezone', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(300)
    expect(convertUTCToLocal('20:00', '-05:00')).toBe('15:00')
  })

  it('handles midnight correctly', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-60)
    expect(convertUTCToLocal('23:00', '+01:00')).toBe('00:00')
  })

  it('works with half-hour timezones', () => {
    vi.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-330)
    expect(convertUTCToLocal('13:00', '+05:30')).toBe('18:30')
  })
})
