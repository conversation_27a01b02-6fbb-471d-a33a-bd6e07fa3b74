import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const isEmptyObject = (
  obj: Record<string, unknown> | Record<string, never>
): boolean => {
  return Object.keys(obj).length === 0
}

function convertToSnake(key: string): string {
  return key.replace(/[A-Z]/g, (match) => '_' + match.toLowerCase())
}

function convertToCamel(key: string): string {
  return key.replace(/(_\w)/g, (m) => m[1].toUpperCase())
}

export function snakeCaseToCamelCase(jsonData: any): any {
  return convertKeys(jsonData, convertToCamel)
}

export function camelCaseToSnakeCase(jsonData: any): any {
  return convertKeys(jsonData, convertToSnake)
}

function convertKeys(jsonData: any, convertKey: any) {
  if (typeof jsonData === 'string') {
    return convertKey(jsonData)
  }

  function recursiveConvert(data: any): any {
    if (typeof data === 'object' && data !== null) {
      if (Array.isArray(data)) {
        return data.map(recursiveConvert)
      } else {
        const newData: any = {}
        for (const key in data) {
          newData[convertKey(key)] = recursiveConvert(data[key])
        }
        return newData
      }
    } else {
      return data
    }
  }

  return recursiveConvert(jsonData)
}

export const extractJiraSubdomainName = (url: string) => {
  if (!url.includes('atlassian')) {
    return url
  }
  const urlParts = url.split('.')
  const title = url.startsWith('http')
    ? urlParts[0].split('//')[1]
    : urlParts[0]
  return title
}

export function parseSortParam(input: string) {
  const [field, order] = input.split(':')
  return {
    field: field,
    order: order,
  }
}

export function getUserUTCHoursOffset() {
  const date = new Date()
  const timezoneOffsetMinutes = date.getTimezoneOffset()

  // Convert minutes to hours and reverse the sign (positive values mean UTC+)
  const timezoneOffsetHours = -timezoneOffsetMinutes / 60

  return timezoneOffsetHours
}

export function getUserUTCOffsetFormatted() {
  const date = new Date()
  const timezoneOffsetMinutes = date.getTimezoneOffset()

  // Convert total minutes into hours and minutes
  const hours = Math.floor(Math.abs(timezoneOffsetMinutes) / 60)
  const minutes = Math.abs(timezoneOffsetMinutes) % 60

  // Determine if the offset is positive or negative
  const sign = timezoneOffsetMinutes > 0 ? '-' : '+'

  // Format with leading zeros for hours and minutes
  const formattedOffset = `${sign}${String(hours).padStart(2, '0')}:${String(
    minutes
  ).padStart(2, '0')}`

  return formattedOffset
}

export function getUserTimezoneOffset(): string {
  const offset = new Date().getTimezoneOffset()
  const absOffset = Math.abs(offset)
  const offsetHours = Math.floor(absOffset / 60)
    .toString()
    .padStart(2, '0')
  const offsetMinutes = (absOffset % 60).toString().padStart(2, '0')
  const offsetSign = offset <= 0 ? '+' : '-'
  return `${offsetSign}${offsetHours}:${offsetMinutes}`
}

export function convertTimeToUTC(time: string, userTimezone: string): string {
  if (!time) {
    return ''
  }
  // Parse the input time
  const [hours, minutes] = time.split(':').map(Number)

  // Create a Date object for today with the given time in UTC
  const date = new Date()
  date.setUTCHours(hours, minutes, 0, 0)

  // Parse the user's timezone offset
  const userOffset = parseInt(userTimezone.replace(':', ''))
  const userOffsetMinutes = Math.abs(userOffset) % 100
  const userOffsetHours = Math.floor(Math.abs(userOffset) / 100)
  const totalOffsetMinutes =
    (userOffsetHours * 60 + userOffsetMinutes) * (userOffset >= 0 ? -1 : 1)

  // Adjust the time to UTC
  date.setUTCMinutes(date.getUTCMinutes() + totalOffsetMinutes)

  // Format the UTC time as a string
  const utcHours = date.getUTCHours().toString().padStart(2, '0')
  const utcMinutes = date.getUTCMinutes().toString().padStart(2, '0')

  const result = `${utcHours}:${utcMinutes}`

  return result
}

export function convertUTCToLocal(time: string, userTimezone: string): string {
  // Parse the input time
  const [hours, minutes] = time.split(':').map(Number)

  // Create a Date object for today with the given time in UTC
  const date = new Date()
  const validHours =
    typeof hours === 'number' && hours >= 0 && hours <= 23 ? hours : 0
  const validMinutes =
    typeof minutes === 'number' && minutes >= 0 && minutes <= 59 ? minutes : 0

  date.setUTCHours(validHours, validMinutes, 0, 0)

  // Parse the user's timezone offset
  const userOffset = parseInt(userTimezone.replace(':', ''))
  const userOffsetMinutes = Math.abs(userOffset) % 100
  const userOffsetHours = Math.floor(Math.abs(userOffset) / 100)
  const totalOffsetMinutes =
    (userOffsetHours * 60 + userOffsetMinutes) * (userOffset >= 0 ? 1 : -1)

  // Adjust the time to local time
  date.setUTCMinutes(date.getUTCMinutes() + totalOffsetMinutes)

  // Format the local time as a string
  const localHours = date.getUTCHours().toString().padStart(2, '0')
  const localMinutes = date.getUTCMinutes().toString().padStart(2, '0')

  const result = `${localHours}:${localMinutes}`

  return result
}

export function getFilenameFromContentDisposition(
  contentDisposition: string | null | undefined
) {
  if (!contentDisposition) return null

  // Try to match filename*= (RFC 5987 encoded) first
  const filenameStarMatch = contentDisposition.match(
    /filename\*=(?:UTF-8'')?([^;]+)/i
  )
  if (filenameStarMatch) {
    return decodeURIComponent(filenameStarMatch[1])
  }

  const filenameMatch = contentDisposition.match(
    /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/i
  )
  if (filenameMatch) {
    let filename = filenameMatch[1]

    if (
      filename.charAt(0) === '"' &&
      filename.charAt(filename.length - 1) === '"'
    ) {
      filename = filename.slice(1, -1)
    }
    return filename
  }

  return null
}
