import '@testing-library/jest-dom/vitest'
import { MetaTagsManager } from '../meta-tags-manager'
import { renderHook } from '@testing-library/react'

describe('MetaTagsManager', () => {
  afterEach(() => {
    document.title = ''
    document.head.innerHTML = ''
  })

  it('should set the document title and meta description', () => {
    const props = {
      title: 'Test Title',
      description: 'Test Description',
      siteName: 'Test Site',
    }

    renderHook(() => MetaTagsManager(props))

    expect(document.title).toBe(`${props.title} | ${props.siteName}`)

    const metaDescription = document.querySelector<HTMLMetaElement>(
      'meta[name="description"]'
    )
    expect(metaDescription).toBeInTheDocument()
    expect(metaDescription?.getAttribute('content')).toBe(props.description)
  })

  it('should not set the og:image meta tag when imageUrl is not provided', () => {
    const props = {
      title: 'Test Title',
      description: 'Test Description',
      siteName: 'Test Site',
    }

    renderHook(() => MetaTagsManager(props))

    expect(document.querySelector('meta[property="og:image"]')).toBeNull()
  })

  it('should set the og:image meta tag when imageUrl is provided', () => {
    const props = {
      title: 'Test Title',
      description: 'Test Description',
      siteName: 'Test Site',
      imageUrl: 'https://example.com/image.jpg',
    }

    renderHook(() => MetaTagsManager(props))

    const metaImage = document.querySelector<HTMLMetaElement>(
      'meta[property="og:image"]'
    )
    expect(metaImage).toBeInTheDocument()
    expect(metaImage?.getAttribute('content')).toBe(props.imageUrl)
  })

  it('should set the twitter:title meta tag', () => {
    const props = {
      title: 'Test Title',
      description: 'Test Description',
      siteName: 'Test Site',
    }

    renderHook(() => MetaTagsManager(props))

    const metaTwitterTitle = document.querySelector<HTMLMetaElement>(
      'meta[name="twitter:title"]'
    )
    expect(metaTwitterTitle).toBeInTheDocument()
    expect(metaTwitterTitle?.getAttribute('content')).toBe(
      `${props.title} | ${props.siteName}`
    )
  })
})
