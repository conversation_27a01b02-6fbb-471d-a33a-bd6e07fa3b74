{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "react-jsx", "module": "commonjs", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}