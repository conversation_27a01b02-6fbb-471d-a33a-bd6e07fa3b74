{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx", "unused-imports"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx", "*.mts", "*.mjs"], "rules": {"@typescript-eslint/consistent-type-imports": "error", "@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}], "@typescript-eslint/no-unused-vars": "error", "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["error", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "max-lines": ["error", 300]}, "parserOptions": {"sourceType": "module"}}, {"files": ["**/vite.config.{js,ts,mjs,mts}"], "rules": {"@typescript-eslint/consistent-type-imports": "off"}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nx/typescript"], "rules": {"@typescript-eslint/no-extra-semi": "error", "no-extra-semi": "off"}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {"@typescript-eslint/no-extra-semi": "error", "no-extra-semi": "off"}}]}