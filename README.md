# Primesec Frontend

![Language](https://img.shields.io/badge/language-Typescript-F7DF1E?style=flat&logo=typescript)
![Framework](https://img.shields.io/badge/framework-React-61DAFB?style=flat&logo=react)
![CSS Framework](https://img.shields.io/badge/css-Tailwind_CSS-38B2AC?style=flat&logo=tailwind-css)
![Build](https://img.shields.io/badge/build-Vite-brightgreen?style=flat&logo=vite)
![Tests](https://img.shields.io/badge/tests-Vitest-9cf?style=flat&logo=github-actions)
![License](https://img.shields.io/github/license/primesec/primesec-frontend)

This is a monorepo project bootstrapped with [NX](https://nx.dev/).

## Table of Contents

- [Primesec Frontend](#primesec-frontend)
  - [Table of Contents](#table-of-contents)
  - [Tech Stack](#tech-stack)
  - [Getting Started](#getting-started)
    - [Prerequisites](#prerequisites)
    - [Installation](#installation)
  - [Setup Local Domain](#setup-local-domain)
  - [Install Certificate for local tls work](#install-certificate-for-local-tls-work)
  - [Environment Variables](#environment-variables)
  - [Running the Dashboard App](#running-the-dashboard-app)
  - [Contribution Guide](#contribution-guide)
    - [Naming Conventions](#naming-conventions)
    - [File Naming Conventions](#file-naming-conventions)
    - [Coding Best Practices](#coding-best-practices)
  - [Code Formatter](#code-formatter)
  - [Periodical Deps Updates and Cleanups](#periodical-deps-updates-and-cleanups)
  - [AI Tools Configuration](#ai-tools-configuration)

## Tech Stack

- 📦 [NX](https://nx.dev/) - Monorepo management
- 🌐 [Vite](https://vitejs.dev/) - React build tool
- ⚛️ [React](https://reactjs.org/) - UI library
- 📝 [Typescript](https://www.typescriptlang.org/) - Language
- 📦 [pnpm](https://pnpm.io/) - Package manager
- 📦 [ESLint](https://eslint.org/) - Linter
- 🎨 [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- 🖼️ [shadcn/ui](https://ui.shadcn.com/) - UI components
- 🧪 [Vitest](https://vitest.dev/) - Test runner
- 🚩 [Playwright](https://playwright.dev/) - E2E testing
- 🧲 [MSW](https://mswjs.io/) - API Mocking
- 📊 [Datadog](https://www.datadoghq.com/) - Monitoring
- 🚀 [LaunchDarkly](https://launchdarkly.com/) - Feature flags

## Getting Started

### Prerequisites

- Install [Node.js](https://nodejs.org/en/download/) (version 20 or higher)
- Recommended: Use [nvm](https://github.com/nvm-sh/nvm) for managing Node.js versions.

Enable Corepack:

```shell
corepack enable
```

This will install pnpm. To upgrade:

```shell
corepack prepare pnpm@latest --activate
```

### Installation

Login to AWS and allow access to the ECR registry:

```shell
 aws codeartifact login --tool npm --repository main-repo --domain primesec > /dev/null 2>&1
```

```shell
aws sso login
```

Install dependencies:

```shell
pnpm install
```

## Setup Local Domain

Add this to your `/etc/hosts`:

```shell
127.0.0.1 local.primesec.dev
```

## Install Certificate for local tls work

go to 'certificates' folder
and install 'local.primesec.dev.pem' file

```bash
brew install mkcert
cd certificates && mkcert -install local.primesec.dev
```

## Environment Variables

Create a `.env` file at the root with the following:

```dotenv
VITE_APP_VERSION=1

NX_NO_CLOUD=true
VITE_USE_LOGIN_MOCK=false
VITE_FRONT_SERVICE_URL=api.example.com

VITE_DATADOG_APPLICATION_ID=some_app_id
VITE_DATADOG_CLIENT_TOKEN=some_client_token
VITE_DATADOG_SERVICE=local.primesec.dev
VITE_DATADOG_ENV=local

VITE_LAUNCHDARKLY_CLIENT_ID=someclientid
VITE_LAUNCHDARKLY_ENV_KEY=local

VITE_BEAMER_PRODUCT_ID=some_product_id
```

## Running the Dashboard App

Start the app in development mode:

```shell
pnpm run dashboard:dev
```

Visit [https://local.primesec.dev:5173/login?from=/](https://local.primesec.dev:5173/login?from=/)

## Contribution Guide

### Naming Conventions

Here is an example of a branch name that follows the naming convention:
`your_name/issue_title_432`

- `your_name`: name or username
- `issue_title`: the title of the issue on Jira
- `432`: is the issue number on Jira

### File Naming Conventions

- `kebab-case.ts` for file names
- `UPPERCASE`, `CONSTANT_CASE`, or `camelCase` for constants
- `PascalCase` for TypeScript interfaces/types and Classes
- `kebab-case` for HTML attributes such class names, id, key, and data attributes
- `camelCase` for general code

### Coding Best Practices

- **No comments**: Remove commented-out code.
- **No console logs**: Use `devConsoleLog()` for necessary logs.
- **Avoid spaghetti code**: Aim for clear, maintainable structure.
- **No unused code and dependencies**: Remove unused files, variables, functions, and any code and dependencies that are not in use.
- **Try not to install new dependencies**: If you have to, try to import (copy-paste) the code to one of our libs.

## Code Formatter

We use [Prettier](https://prettier.io/). Ensure it formats on save.

## Periodical Deps Updates and Cleanups

Regularly update dependencies and remove unused ones:

```shell
pnpm outdated
pnpm update framer-motion
```

Learn more about dependency management with [pnpm update](https://pnpm.io/cli/update).

## AI Tools Configuration

This project includes configuration for various AI coding assistants:

- `.aiconfig.json` - Primary configuration for AI tools
- `.github/copilot-instructions.md` - GitHub Copilot specific patterns and examples
- `.cursorrules` - Cursor AI specific rules

When making significant architectural or pattern changes, please update these files to keep AI assistance accurate and consistent.
